import 'dart:async';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../core/themes/color_schemes.dart';

/// A versatile carousel widget that can be used for products, categories, banners, etc.
class AppCarousel<T> extends StatefulWidget {
  /// List of items to display in the carousel
  final List<T> items;

  /// Builder function to create widgets from items
  final Widget Function(BuildContext context, T item, int index) itemBuilder;

  /// Height of the carousel
  final double height;

  /// Width of the carousel (null means full width)
  final double? width;

  /// Aspect ratio of each item (width / height)
  final double viewportFraction;

  /// Whether to enable auto-scrolling
  final bool autoPlay;

  /// Duration between auto-scrolls
  final Duration autoPlayInterval;

  /// Duration of the scroll animation
  final Duration animationDuration;

  /// Whether to enable infinite scrolling
  final bool infiniteScroll;

  /// Whether to show pagination indicators
  final bool showIndicator;

  /// Type of pagination indicator to show
  final CarouselIndicatorType indicatorType;

  /// Position of the pagination indicator
  final CarouselIndicatorPosition indicatorPosition;

  /// Custom colors for the pagination indicator
  final Color? activeIndicatorColor;
  final Color? inactiveIndicatorColor;

  /// Padding around the carousel
  final EdgeInsetsGeometry padding;

  /// Spacing between items
  final double itemSpacing;

  /// Border radius of the carousel container
  final double borderRadius;

  /// Callback when an item is tapped
  final Function(int index)? onItemTap;

  /// Callback when page changes
  final Function(int index)? onPageChanged;

  /// Initial page to show
  final int initialPage;

  /// Whether to enable page snapping
  final bool pageSnapping;

  /// Custom scroll physics
  final ScrollPhysics? physics;

  /// Custom controller for the page view
  final PageController? controller;

  /// Whether to show title and subtitle if available in the item
  final bool showTitleOverlay;

  /// Function to get title from item (if item has title)
  final String Function(T item)? getTitleFromItem;

  /// Function to get subtitle from item (if item has subtitle)
  final String Function(T item)? getSubtitleFromItem;

  AppCarousel({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.height = 200,
    this.width,
    this.viewportFraction = 0.9,
    this.autoPlay = true,
    this.autoPlayInterval = const Duration(seconds: 3),
    this.animationDuration = const Duration(milliseconds: 800),
    this.infiniteScroll = true,
    this.showIndicator = true,
    this.indicatorType = CarouselIndicatorType.dot,
    this.indicatorPosition = CarouselIndicatorPosition.bottom,
    this.activeIndicatorColor,
    this.inactiveIndicatorColor,
    this.padding = EdgeInsets.zero,
    this.itemSpacing = 16,
    this.borderRadius = 6,
    this.onItemTap,
    this.onPageChanged,
    this.initialPage = 0,
    this.pageSnapping = true,
    this.physics,
    this.controller,
    this.showTitleOverlay = false,
    this.getTitleFromItem,
    this.getSubtitleFromItem,
  }) : assert(items.isNotEmpty, 'Items cannot be empty');

  @override
  State<AppCarousel<T>> createState() => _AppCarouselState<T>();
}

class _AppCarouselState<T> extends State<AppCarousel<T>> {
  late PageController _pageController;
  Timer? _autoPlayTimer;
  int _currentPage = 0;
  int? get _itemCount => widget.infiniteScroll ? null : widget.items.length;
  int get _actualItemCount => widget.items.length;

  @override
  void initState() {
    super.initState();
    _currentPage = widget.initialPage;
    _pageController = widget.controller ??
        PageController(
          initialPage:
              widget.infiniteScroll ? _currentPage * 1000 : _currentPage,
          viewportFraction: widget.viewportFraction,
        );

    if (widget.autoPlay) {
      _startAutoPlay();
    }
  }

  @override
  void dispose() {
    _stopAutoPlay();
    if (widget.controller == null) {
      _pageController.dispose();
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant AppCarousel<T> oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.autoPlay != widget.autoPlay) {
      if (widget.autoPlay) {
        _startAutoPlay();
      } else {
        _stopAutoPlay();
      }
    }

    if (oldWidget.items.length != widget.items.length) {
      // If the number of items changed, we need to reset the page controller
      if (widget.controller == null) {
        _pageController.dispose();
        _pageController = PageController(
          initialPage:
              widget.infiniteScroll ? _currentPage * 1000 : _currentPage,
          viewportFraction: widget.viewportFraction,
        );
      }
    }
  }

  void _startAutoPlay() {
    _autoPlayTimer = Timer.periodic(widget.autoPlayInterval, (timer) {
      if (_pageController.hasClients) {
        final nextPage = _pageController.page!.round() + 1;
        _pageController.animateToPage(
          nextPage,
          duration: widget.animationDuration,
          curve: Curves.easeInOut,
        );
      }
    });
  }

  void _stopAutoPlay() {
    _autoPlayTimer?.cancel();
    _autoPlayTimer = null;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: Column(
        children: [
          if (widget.indicatorPosition == CarouselIndicatorPosition.top &&
              widget.showIndicator)
            _buildIndicator(),
          Expanded(
            child: Padding(
              padding: widget.padding,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: _itemCount,
                  physics: widget.physics,
                  pageSnapping: widget.pageSnapping,
                  padEnds: widget.viewportFraction < 1.0,
                  onPageChanged: (index) {
                    setState(() {
                      _currentPage = widget.infiniteScroll
                          ? index % _actualItemCount
                          : index;
                    });
                    widget.onPageChanged?.call(_currentPage);
                  },
                  itemBuilder: (context, index) {
                    final actualIndex = widget.infiniteScroll
                        ? index % _actualItemCount
                        : index;
                    final item = widget.items[actualIndex];

                    Widget itemWidget =
                        widget.itemBuilder(context, item, actualIndex);

                    if (widget.showTitleOverlay &&
                        (widget.getTitleFromItem != null ||
                            widget.getSubtitleFromItem != null)) {
                      itemWidget = Stack(
                        fit: StackFit.expand,
                        children: [
                          itemWidget,
                          Positioned(
                            left: 16,
                            bottom: 16,
                            right: 16,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (widget.getTitleFromItem != null)
                                  Text(
                                    widget.getTitleFromItem!(item),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      shadows: [
                                        Shadow(
                                          offset: Offset(1, 1),
                                          blurRadius: 3,
                                          color: Colors.black54,
                                        ),
                                      ],
                                    ),
                                  ),
                                if (widget.getSubtitleFromItem != null)
                                  Text(
                                    widget.getSubtitleFromItem!(item),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      shadows: [
                                        Shadow(
                                          offset: Offset(1, 1),
                                          blurRadius: 2,
                                          color: Colors.black54,
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      );
                    }

                    return Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: widget.itemSpacing / 2),
                      child: GestureDetector(
                        onTap: widget.onItemTap != null
                            ? () => widget.onItemTap!(actualIndex)
                            : null,
                        child: itemWidget,
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
          if (widget.indicatorPosition == CarouselIndicatorPosition.bottom &&
              widget.showIndicator)
            _buildIndicator(),
        ],
      ),
    );
  }

  Widget _buildIndicator() {
    switch (widget.indicatorType) {
      case CarouselIndicatorType.dot:
        return _buildDotIndicator();
      case CarouselIndicatorType.fraction:
        return _buildFractionIndicator();
      case CarouselIndicatorType.bar:
        return _buildBarIndicator();
      case CarouselIndicatorType.dottedBar:
        return _buildDottedBarIndicator();
    }
  }

  Widget _buildDotIndicator() {
    return Padding(
      padding: const EdgeInsets.only(top: 8, bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(_actualItemCount, (index) {
          return Container(
            width: 8,
            height: 8,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: _currentPage == index
                  ? widget.activeIndicatorColor ?? AppColors.primary
                  : widget.inactiveIndicatorColor ?? Colors.grey.shade300,
            ),
          );
        }),
      ),
    );
  }

  Widget _buildFractionIndicator() {
    return Padding(
      padding: const EdgeInsets.only(top: 8, bottom: 8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.black.withAlpha(128),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          '${_currentPage + 1}/$_actualItemCount',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildBarIndicator() {
    return Padding(
      padding: const EdgeInsets.only(top: 8, bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(_actualItemCount, (index) {
          return Container(
            width: 16,
            height: 4,
            margin: const EdgeInsets.symmetric(horizontal: 2),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2),
              color: _currentPage == index
                  ? widget.activeIndicatorColor ?? AppColors.primary
                  : widget.inactiveIndicatorColor ?? Colors.grey.shade300,
            ),
          );
        }),
      ),
    );
  }

  Widget _buildDottedBarIndicator() {
    return Padding(
      padding: const EdgeInsets.only(top: 8, bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(_actualItemCount, (index) {
          return Container(
            width: _currentPage == index ? 16 : 6,
            height: 6,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: _currentPage == index
                  ? widget.activeIndicatorColor ?? AppColors.primary
                  : widget.inactiveIndicatorColor ?? Colors.grey.shade300,
            ),
          );
        }),
      ),
    );
  }
}

/// Types of pagination indicators
enum CarouselIndicatorType {
  /// Circular dots
  dot,

  /// Fraction (e.g., "1/5")
  fraction,

  /// Horizontal bars
  bar,

  ///Dotted bar
  dottedBar,
}

/// Positions for the pagination indicator
enum CarouselIndicatorPosition {
  /// Above the carousel
  top,

  /// Below the carousel
  bottom,
}

/// Banner carousel specifically for promotional banners
class BannerCarousel extends StatelessWidget {
  final List<String> bannerUrls;
  final double height;
  final double borderRadius;
  final bool autoPlay;
  final Function(int index)? onBannerTap;
  final BoxFit imageFit;
  final Widget Function(BuildContext, String, int)? bannerBuilder;
  final EdgeInsetsGeometry padding;

  /// A carousel specifically for displaying promotional banners
  const BannerCarousel({
    super.key,
    required this.bannerUrls,
    this.height = 180,
    this.borderRadius = 12,
    this.autoPlay = true,
    this.onBannerTap,
    this.imageFit = BoxFit.cover,
    this.bannerBuilder,
    this.padding = const EdgeInsets.symmetric(horizontal: 16),
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: AppCarousel<String>(
        items: bannerUrls,
        height: height,
        borderRadius: borderRadius,
        autoPlay: autoPlay,
        onItemTap: onBannerTap,
        indicatorType: CarouselIndicatorType.dot,
        itemBuilder: bannerBuilder ?? _defaultBannerBuilder,
      ),
    );
  }

  Widget _defaultBannerBuilder(
      BuildContext context, String imageUrl, int index) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        fit: imageFit,
        placeholder: (context, url) => Container(
          color: Colors.grey[200],
          child: Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
            ),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: Colors.grey[200],
          child: Center(
            child: Icon(
              Icons.image_not_supported_outlined,
              color: Colors.grey[400],
              size: 40,
            ),
          ),
        ),
      ),
    );
  }
}

/// Product carousel for displaying product cards
class ProductCarousel extends StatelessWidget {
  final List<Widget> productCards;
  final String? title;
  final VoidCallback? onSeeAll;
  final double height;
  final double viewportFraction;
  final double itemSpacing;
  final EdgeInsetsGeometry padding;
  final bool showAsGrid;
  final TextStyle? titleStyle;
  final TextStyle? seeAllStyle;

  /// A carousel specifically for displaying product cards
  const ProductCarousel({
    super.key,
    required this.productCards,
    this.title,
    this.onSeeAll,
    this.height = 220,
    this.viewportFraction = 0.42,
    this.itemSpacing = 8,
    this.padding = const EdgeInsets.symmetric(vertical: 8),
    this.showAsGrid = false,
    this.titleStyle,
    this.seeAllStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title!,
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (onSeeAll != null)
                  TextButton(
                    onPressed: onSeeAll,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      minimumSize: const Size(0, 28),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      textStyle: const TextStyle(fontSize: 12),
                    ),
                    child: Row(
                      children: const [
                        Text('See All'),
                        SizedBox(width: 2),
                        Icon(Icons.arrow_forward_ios, size: 10),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 8),
        ],
        showAsGrid
            ? Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.7,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: productCards.length,
                  itemBuilder: (context, index) => productCards[index],
                ),
              )
            : SizedBox(
                height: height,
                child: AppCarousel<Widget>(
                  items: productCards,
                  height: height,
                  viewportFraction: viewportFraction,
                  itemSpacing: itemSpacing,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  showIndicator: false,
                  itemBuilder: (context, widget, index) => widget,
                ),
              ),
      ],
    );
  }
}

/// Category carousel for displaying category cards
class CategoryCarousel extends StatelessWidget {
  final List<Widget> categoryCards;
  final String? title;
  final VoidCallback? onSeeAll;
  final double height;
  final double viewportFraction;
  final double itemSpacing;
  final EdgeInsetsGeometry padding;
  final bool showAsGrid;
  final TextStyle? titleStyle;
  final TextStyle? seeAllStyle;
  final bool useGrid;
  final int gridCrossAxisCount;

  /// A carousel specifically for displaying category cards
  const CategoryCarousel({
    super.key,
    required this.categoryCards,
    this.title,
    this.onSeeAll,
    this.height = 110,
    this.viewportFraction = 0.22,
    this.itemSpacing = 8,
    this.padding = const EdgeInsets.symmetric(vertical: 8),
    this.showAsGrid = false,
    this.titleStyle,
    this.seeAllStyle,
    this.useGrid = false,
    this.gridCrossAxisCount = 4,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title!,
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (onSeeAll != null)
                  TextButton(
                    onPressed: onSeeAll,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      minimumSize: const Size(0, 28),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      textStyle: const TextStyle(fontSize: 12),
                    ),
                    child: Row(
                      children: const [
                        Text('See All'),
                        SizedBox(width: 2),
                        Icon(Icons.arrow_forward_ios, size: 10),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 8),
        ],
        useGrid || showAsGrid
            ? Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: gridCrossAxisCount,
                    childAspectRatio: 0.9,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: categoryCards.length,
                  itemBuilder: (context, index) => categoryCards[index],
                ),
              )
            : SizedBox(
                height: height,
                child: AppCarousel<Widget>(
                  items: categoryCards,
                  height: height,
                  viewportFraction: viewportFraction,
                  itemSpacing: itemSpacing,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  showIndicator: false,
                  itemBuilder: (context, widget, index) => widget,
                ),
              ),
      ],
    );
  }
}

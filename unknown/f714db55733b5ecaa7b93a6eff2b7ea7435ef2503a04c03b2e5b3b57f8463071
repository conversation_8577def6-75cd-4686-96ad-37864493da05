import 'package:equatable/equatable.dart';
import '../models/legal_content_model.dart';

abstract class LegalState extends Equatable {
  const LegalState();

  @override
  List<Object?> get props => [];
}

class LegalInitial extends LegalState {}

class LegalLoading extends LegalState {}

class LegalLoaded extends LegalState {
  final LegalContentModel content;

  const LegalLoaded(this.content);

  @override
  List<Object?> get props => [content];
}

class LegalError extends LegalState {
  final String message;

  const LegalError(this.message);

  @override
  List<Object?> get props => [message];
}

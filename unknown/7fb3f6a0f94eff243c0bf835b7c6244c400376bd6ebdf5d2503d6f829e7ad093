import 'package:equatable/equatable.dart';
import '../services/legal_content_service.dart';

abstract class LegalEvent extends Equatable {
  const LegalEvent();

  @override
  List<Object?> get props => [];
}

class LoadLegalContent extends LegalEvent {
  final LegalContentType contentType;
  final String language;

  const LoadLegalContent({
    required this.contentType,
    required this.language,
  });

  @override
  List<Object?> get props => [contentType, language];
}

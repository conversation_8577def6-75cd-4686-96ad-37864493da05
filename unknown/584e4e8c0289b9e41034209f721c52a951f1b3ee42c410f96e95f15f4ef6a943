// Google Places API JavaScript interop functions
// This file exposes Google Places API functionality to Dar<PERSON> through JS interop

// Global variable to store the session token
let placesSessionToken = null;

// Initialize the Places service
function initPlacesService() {
  if (typeof google !== 'undefined' && google.maps && google.maps.places) {
    console.log('Google Places API is available');
    return true;
  } else {
    console.error('Google Places API is not available');
    return false;
  }
}

// Generate a new session token for autocomplete requests
function generateSessionToken() {
  placesSessionToken = new google.maps.places.AutocompleteSessionToken();
  return true;
}

// Get the current session token as string
function getSessionToken() {
  return placesSessionToken ? placesSessionToken.toString() : '';
}

// Perform place autocomplete search
function getPlaceAutocomplete(input, options) {
  return new Promise((resolve, reject) => {
    if (!initPlacesService()) {
      reject('Places API not available');
      return;
    }

    if (!placesSessionToken) {
      generateSessionToken();
    }

    const autocompleteService = new google.maps.places.AutocompleteService();
    
    // Prepare request options
    const requestOptions = {
      input: input,
      sessionToken: placesSessionToken,
    };

    // Add optional parameters if provided
    if (options) {
      if (options.types) requestOptions.types = options.types;
      if (options.componentRestrictions) requestOptions.componentRestrictions = options.componentRestrictions;
      if (options.bounds) requestOptions.bounds = options.bounds;
    }

    // Make the autocomplete request
    autocompleteService.getPlacePredictions(
      requestOptions,
      (predictions, status) => {
        if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
          // Convert predictions to a simple JSON structure
          const results = predictions.map(prediction => ({
            placeId: prediction.place_id,
            description: prediction.description,
            mainText: prediction.structured_formatting ? prediction.structured_formatting.main_text : '',
            secondaryText: prediction.structured_formatting ? prediction.structured_formatting.secondary_text : '',
            types: prediction.types || [],
          }));
          resolve(JSON.stringify(results));
        } else {
          reject(`Autocomplete request failed with status: ${status}`);
        }
      }
    );
  });
}

// Get place details by place ID
function getPlaceDetails(placeId) {
  return new Promise((resolve, reject) => {
    if (!initPlacesService()) {
      reject('Places API not available');
      return;
    }

    // Create a dummy div for the PlacesService (required by the API)
    const placesServiceDiv = document.createElement('div');
    const placesService = new google.maps.places.PlacesService(placesServiceDiv);

    placesService.getDetails(
      {
        placeId: placeId,
        fields: ['address_components', 'formatted_address', 'geometry', 'name', 'place_id']
      },
      (place, status) => {
        if (status === google.maps.places.PlacesServiceStatus.OK && place) {
          // Extract and format the place details
          const result = {
            placeId: place.place_id,
            name: place.name || '',
            formattedAddress: place.formatted_address || '',
            // Structure geometry and location to match what Dart expects
            geometry: {
              location: {
                lat: place.geometry?.location?.lat() || 0,
                lng: place.geometry?.location?.lng() || 0
              }
            },
            addressComponents: place.address_components?.map(component => ({
              longName: component.long_name,
              shortName: component.short_name,
              types: component.types || []
            })) || []
          };
          resolve(JSON.stringify(result));
        } else {
          reject(`Place details request failed with status: ${status}`);
        }
      }
    );
  });
}

// Make functions available globally for Dart JS interop
window.rozanaPlacesInterop = {
  initPlacesService,
  generateSessionToken,
  getSessionToken,
  getPlaceAutocomplete,
  getPlaceDetails
};

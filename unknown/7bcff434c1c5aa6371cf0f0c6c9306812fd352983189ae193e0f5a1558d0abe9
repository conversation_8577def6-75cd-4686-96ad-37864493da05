part of 'web_view_bloc.dart';

@freezed
class WebViewState with _$WebViewState {
  const factory WebViewState.initial() = _Initial;
  const factory WebViewState.loading() = _Loading;
  const factory WebViewState.loaded() = _Loaded;
  const factory WebViewState.error(String message) = _Error;
  const factory WebViewState.webViewMode() = _WebViewMode;
  const factory WebViewState.nativeViewMode() = _NativeViewMode;
}

import 'package:flutter/material.dart';
import 'package:rozana/widgets/skeleton_loader_factory.dart';

class ProductSkeletonLoader extends StatelessWidget {
  final bool useGridView;
  final bool showAsRow;
  final int gridCrossAxisCount;
  final double gridChildAspectRatio;
  final int itemCount;

  const ProductSkeletonLoader({
    super.key,
    this.useGridView = false,
    this.showAsRow = false,
    this.gridCrossAxisCount = 2,
    this.gridChildAspectRatio = 2 / 3,
    this.itemCount = 12,
  });

  @override
  Widget build(BuildContext context) {
    return showAsRow
        ? _buildRowSkeleton(context)
        : useGridView
            ? _buildGridSkeleton(context)
            : _buildListSkeleton(context);
  }

  Widget _buildRowSkeleton(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: itemCount,
      itemBuilder: (context, index) => Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: SkeletonLoaderFactory.createProductSkeleton(
          width: screenWidth - 32,
          height: 100,
          radius: 12,
        ),
      ),
    );
  }

  Widget _buildGridSkeleton(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final cardWidth =
        (screenWidth - (gridCrossAxisCount + 1) * 12) / gridCrossAxisCount;
    final cardHeight = (cardWidth / gridChildAspectRatio) - 12;

    return SkeletonLoaderFactory.createSkeletonGrid(
      itemCount: itemCount,
      itemHeight: cardHeight,
      itemWidth: cardWidth,
      crossAxisCount: gridCrossAxisCount,
      mainAxisSpacing: 12,
      crossAxisSpacing: 12,
      padding: const EdgeInsets.symmetric(horizontal: 16),
    );
  }

  Widget _buildListSkeleton(BuildContext context) {
    return ListView.builder(
      itemCount: itemCount,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemBuilder: (context, index) => Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: SkeletonLoaderFactory.createProductSkeleton(
          width: double.infinity,
          height: 220,
          radius: 12,
        ),
      ),
    );
  }

  // _buildCard method removed as we now use SkeletonLoaderFactory for all skeleton elements
}

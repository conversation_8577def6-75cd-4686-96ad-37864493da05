#!/bin/bash

# Build script for production environment
# Note: Currently uses same configuration as other environments
echo "Building for PRODUCTION environment..."

flutter build apk \
  --dart-define=ENVIRONMENT=production \
  --dart-define=API_BASE_URL=https://oms-dev.rozana.tech/ \
  --dart-define=TYPESENSE_HOST=rozanats.headrun.com \
  --dart-define=TYPESENSE_API_KEY=WzQSB1f8gpXWmsQzRjvEO149v04bfaJa \
  --dart-define=FIREBASE_API_KEY=AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI \
  --dart-define=AMPLITUDE_API_KEY=fa4004be40bfd5c03e30e6725351c87e \
  --dart-define=BRANCH_LOGGING_ENABLED=false \
  --dart-define=ENABLE_LOGGING=false \
  --dart-define=APP_NAME="Rozana" \
  --release \
  --obfuscate \
  --split-debug-info=build/debug-info

echo "Production build completed!"
echo "Remember to test the build thoroughly before releasing!"

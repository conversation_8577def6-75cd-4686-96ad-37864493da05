// Razorpay web integration helper

// Create a global object to handle Razorpay integration
window.RozanaRazorpay = {
  // Store the Razorpay instance
  instance: null,
  isAvailable: function() {
    return typeof Razorpay === 'function';
  },
  
  // Initialize and open Razorpay checkout
  open: function(options) {
    console.log('Razorpay checkout called with options:', options);
    try {
      if (!this.isAvailable()) {
        console.error('Razorpay is not available yet');
        throw new Error('Razorpay is not available');
      }
      
      // Create a new instance
      this.instance = new Razorpay(options);
      
      // Open the checkout
      this.instance.open();
      console.log('Razorpay checkout opened successfully');
      return true;
    } catch (error) {
      console.error('Razorpay error:', error);
      throw error;
    }
  }
};

// Alias for backward compatibility
window.openRazorpayCheckout = function(options) {
  return window.RozanaRazorpay.open(options);
};

// Check if Razorpay is available
window.checkRazorpayAvailability = function() {
  return typeof Razorpay === 'function';
};

// Log that it's ready
console.log('Razorpay web integration ready');

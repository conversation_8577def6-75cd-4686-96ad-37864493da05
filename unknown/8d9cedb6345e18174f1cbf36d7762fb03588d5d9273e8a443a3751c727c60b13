import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../data/models/adress_model.dart';

part 'location_event.freezed.dart';

@freezed
class LocationEvent with _$LocationEvent {
  const factory LocationEvent.started() = _Started;
  const factory LocationEvent.refreshLocation({AddressModel? tempAddress}) =
      _RefreshLocation;
  const factory LocationEvent.requestPermissionAndDetect() =
      _RequestPermissionAndDetect;
}

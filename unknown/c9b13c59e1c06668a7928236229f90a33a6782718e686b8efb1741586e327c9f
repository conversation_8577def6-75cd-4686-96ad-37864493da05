// Razorpay Web Integration Helper
// This file provides helper functions for integrating Razorpay checkout in Flutter web

// Make sure the function is available as soon as the script loads
(function() {
  console.log('Initializing Razorpay web integration');
  
  // Define the function in the global scope
  window.openRazorpayCheckout = function(options) {
    try {
      console.log('Opening Razorpay checkout with options:', options);
      var rzp = new Razorpay(options);
      rzp.open();
      
      // Add global error handler
      rzp.on('payment.failed', function(response) {
        console.log('Payment failed:', response.error);
        // The error will be handled by the ondismiss callback
      });
      
      return true;
    } catch (error) {
      console.error('Razorpay initialization error:', error);
      return false;
    }
  };
  
  // Log that the function is ready
  console.log('Razorpay web integration ready');
})();

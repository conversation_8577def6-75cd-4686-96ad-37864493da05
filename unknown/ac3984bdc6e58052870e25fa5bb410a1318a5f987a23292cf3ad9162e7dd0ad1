import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'skeleton_loader_factory.dart';

/// CustomShimmer is maintained for backward compatibility
/// New code should use SkeletonLoaderFactory instead
class CustomShimmer extends StatelessWidget {
  const CustomShimmer({
    super.key,
    this.height,
    this.width,
    this.radius,
    this.margin = const EdgeInsets.all(0.5),
    this.padding,
    this.boxShadow,
    this.child,
    this.baseColor,
    this.highlightColor,
    this.mainColor,
    this.borderRadius,
    this.border,
    this.direction = ShimmerDirection.ltr,
  });

  final double? height;
  final double? width;
  final double? radius;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final List<BoxShadow>? boxShadow;
  final Widget? child;
  final Color? baseColor;
  final Color? highlightColor;
  final Color? mainColor;
  final Border? border;
  final ShimmerDirection direction;

  @override
  Widget build(BuildContext context) {
    // Check if we should use reduced motion
    final bool reducedMotion = MediaQuery.of(context).disableAnimations;
    
    return Container(
      decoration: BoxDecoration(
          border: border,
          borderRadius: borderRadius ?? BorderRadius.circular(radius ?? 0)),
      child: Shimmer.fromColors(
        period: const Duration(milliseconds: 1500),
        enabled: !reducedMotion, // Respect reduced motion settings
        direction: direction,
        baseColor: baseColor ?? Colors.grey.shade200.withValues(alpha: 0.8),
        highlightColor: highlightColor ?? Colors.white.withValues(alpha: 0.2),
        child: Container(
          height: height,
          width: width,
          margin: margin,
          padding: padding,
          decoration: BoxDecoration(
              color: mainColor ?? Colors.grey.shade100,
              boxShadow: boxShadow,
              borderRadius: borderRadius ?? BorderRadius.circular(radius ?? 6)),
          child: child,
        ),
      ),
    );
  }
}

/// ShimmerTextField uses SkeletonLoaderFactory internally while maintaining the same API
class ShimmerTextField extends StatelessWidget {
  final double? height;
  final double? width;
  final EdgeInsetsGeometry? margin;
  
  const ShimmerTextField({
    super.key,
    this.height = 50,
    this.width = double.infinity,
    this.margin,
  });
  
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: margin ?? const EdgeInsets.all(0.5),
      child: SkeletonLoaderFactory.createTextSkeleton(
        height: height ?? 50,
        width: width ?? double.infinity,
        radius: 8.0, // Text field typically has larger radius
      ),
    );
  }
}

/// ShimmerText uses SkeletonLoaderFactory internally while maintaining the same API
class ShimmerText extends StatelessWidget {
  final double? height;
  final double? width;
  final double? radius;
  final EdgeInsetsGeometry? margin;
  
  const ShimmerText({
    super.key,
    this.height = 14,
    this.width = double.infinity,
    this.radius = 6,
    this.margin = const EdgeInsets.symmetric(vertical: 5),
  });
  
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: margin ?? const EdgeInsets.symmetric(vertical: 5),
      child: SkeletonLoaderFactory.createTextSkeleton(
        height: height ?? 14,
        width: width ?? double.infinity,
        radius: radius ?? 6,
      ),
    );
  }
}

/// ShimmerBox uses SkeletonLoaderFactory internally while maintaining the same API
class ShimmerBox extends StatelessWidget {
  final double? height;
  final double? width;
  final double? radius;
  final EdgeInsetsGeometry? margin;
  
  const ShimmerBox({
    super.key,
    this.height = 100,
    this.width = double.infinity,
    this.radius = 6,
    this.margin,
  });
  
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: margin ?? const EdgeInsets.all(0.5),
      child: SkeletonLoaderFactory.createProductSkeleton(
        height: height ?? 100,
        width: width ?? double.infinity,
        radius: radius ?? 6,
      ),
    );
  }
}

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'web_view_event.dart';
part 'web_view_state.dart';
part 'web_view_bloc.freezed.dart';

class WebViewBloc extends Bloc<WebViewEvent, WebViewState> {
  static const double webViewBreakpoint = 600.0;

  WebViewBloc() : super(const WebViewState.initial()) {
    on<WebViewEvent>((event, emit) {
      event.when(
        started: () {
          emit(const WebViewState.loading());
          // Immediately load content without delay to prevent loading states
          add(const WebViewEvent.contentLoaded());
        },
        contentLoaded: () => emit(const WebViewState.loaded()),
        contentFailed: (error) => emit(WebViewState.error(error)),
        switchToWebView: () => emit(const WebViewState.webViewMode()),
        switchToNativeView: () => emit(const WebViewState.nativeViewMode()),
        checkScreenWidth: (width) {
          if (width > webViewBreakpoint) {
            emit(const WebViewState.webViewMode());
          } else {
            emit(const WebViewState.nativeViewMode());
          }
        },
      );
    });
  }

  // Initialize the web view
  void initialize() {
    add(const WebViewEvent.started());
  }

  // Mark content as loaded
  void setContentLoaded() {
    add(const WebViewEvent.contentLoaded());
  }

  // Handle content loading errors
  void setContentError(String error) {
    add(WebViewEvent.contentFailed(error));
  }

  // Check screen width and switch view mode
  void checkScreenWidth(double width) {
    add(WebViewEvent.checkScreenWidth(width));
  }

  // Manually switch to web view
  void switchToWebView() {
    add(const WebViewEvent.switchToWebView());
  }

  // Manually switch to native view
  void switchToNativeView() {
    add(const WebViewEvent.switchToNativeView());
  }

  @override
  Future<void> close() {
    // Clean up any web view resources if needed
    return super.close();
  }
}

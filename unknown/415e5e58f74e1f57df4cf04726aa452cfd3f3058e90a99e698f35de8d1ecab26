import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:rozana/domain/entities/order_entity.dart';

part 'order_state.freezed.dart';

@freezed
class OrderState with _$OrderState {
  /// Initial state
  const factory OrderState.initial() = OrderInitial;

  /// Loading state
  const factory OrderState.loading() = OrderLoading;

  /// Order history loaded state
  const factory OrderState.orderHistoryLoaded({
    required List<OrderEntity> orders,
    @Default(false) bool isLoadingMore,
    @Default(true) bool hasMoreData,
    @Default(0) int currentPage,
    @Default('') String currentFilter,
    @Default('') String searchQuery,
  }) = OrderHistoryLoaded;

  /// Order details loaded state
  const factory OrderState.orderDetailsLoaded({
    required OrderEntity order,
  }) = OrderDetailsLoaded;

  /// Order cancelled state
  const factory OrderState.orderCancelled({
    required String orderId,
    required String message,
  }) = OrderCancelled;

  /// Error state
  const factory OrderState.error({
    required String message,
    String? orderId,
  }) = OrderError;

  /// Empty state (no orders found)
  const factory OrderState.empty({
    @Default('') String filter,
    @Default('') String searchQuery,
  }) = OrderEmpty;
}

/// Extension to add helper methods to OrderState
extension OrderStateExtension on OrderState {
  /// Check if currently loading
  bool get isLoading => this is OrderLoading;

  /// Check if has orders
  bool get hasOrders => maybeWhen(
        orderHistoryLoaded: (orders, _, __, ___, ____, _____) => orders.isNotEmpty,
        orElse: () => false,
      );

  /// Get current orders list
  List<OrderEntity> get orders => maybeWhen(
        orderHistoryLoaded: (orders, _, __, ___, ____, _____) => orders,
        orElse: () => [],
      );

  /// Get current order details
  OrderEntity? get orderDetails => maybeWhen(
        orderDetailsLoaded: (order) => order,
        orElse: () => null,
      );

  /// Check if loading more data
  bool get isLoadingMore => maybeWhen(
        orderHistoryLoaded: (_, isLoadingMore, __, ___, ____, _____) => isLoadingMore,
        orElse: () => false,
      );

  /// Check if has more data to load
  bool get hasMoreData => maybeWhen(
        orderHistoryLoaded: (_, __, hasMoreData, ___, ____, _____) => hasMoreData,
        orElse: () => false,
      );

  /// Get current page
  int get currentPage => maybeWhen(
        orderHistoryLoaded: (_, __, ___, currentPage, ____, _____) => currentPage,
        orElse: () => 0,
      );

  /// Get current filter
  String get currentFilter => maybeWhen(
        orderHistoryLoaded: (_, __, ___, ____, currentFilter, _____) => currentFilter,
        empty: (filter, _) => filter,
        orElse: () => '',
      );

  /// Get current search query
  String get searchQuery => maybeWhen(
        orderHistoryLoaded: (_, __, ___, ____, _____, searchQuery) => searchQuery,
        empty: (_, searchQuery) => searchQuery,
        orElse: () => '',
      );

  /// Check if in error state
  bool get hasError => this is OrderError;

  /// Get error message
  String? get errorMessage => maybeWhen(
        error: (message, _) => message,
        orElse: () => null,
      );
}

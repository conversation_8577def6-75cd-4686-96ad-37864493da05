import 'places_service_interface.dart';

/// Stub implementation of the Places service for non-web platforms
class PlacesServiceWeb implements PlacesServiceInterface {
  @override
  Future<bool> initPlacesService() async {
    throw UnsupportedError('PlacesServiceWeb is only available on web platforms');
  }

  @override
  Future<String> generateSessionToken() async {
    throw UnsupportedError('PlacesServiceWeb is only available on web platforms');
  }

  @override
  Future<List<Map<String, dynamic>>> getPlaceAutocomplete({
    required String input,
    String? sessionToken,
    Map<String, dynamic>? options,
  }) async {
    throw UnsupportedError('PlacesServiceWeb is only available on web platforms');
  }

  @override
  Future<Map<String, dynamic>?> getPlaceDetailsById(String placeId) async {
    throw UnsupportedError('PlacesServiceWeb is only available on web platforms');
  }
}

import 'package:flutter/material.dart';

// class CustomDatePicker {
//   static customizedDatePicker(
//       {required DateTime initialDate,
//       required DateTime firstDate,
//       required DateTime lastDate}) {
//     return showDatePicker(
//       context: Get.context!,
//       initialEntryMode: DatePickerEntryMode.calendarOnly,
//       initialDate: initialDate,
//       firstDate: firstDate,
//       lastDate: lastDate,
//       builder: (ctx, child) {
//         return Theme(
//           data: ThemeData.light().copyWith(
//             colorScheme: const ColorScheme.light(primary: kPrimaryColor),
//           ),
//           child: child!,
//         );
//       },
//     );
//   }

//   static customizedYearPicker(
//       {required DateTime initialDate,
//       required DateTime firstDate,
//       required DateTime lastDate}) {
//     return showDatePicker(
//       context: Get.context!,
//       initialEntryMode: DatePickerEntryMode.calendarOnly,
//       initialDate: initialDate,
//       firstDate: firstDate,
//       lastDate: lastDate,
//       initialDatePickerMode: DatePickerMode.year,
//       builder: (ctx, child) {
//         return Theme(
//           data: ThemeData.light().copyWith(
//             colorScheme: const ColorScheme.light(primary: kPrimaryColor),
//           ),
//           child: child!,
//         );
//       },
//     );
//   }

//   static Future<DateTime?> getExpiryDate() async {
//     return await customizedDatePicker(
//         initialDate: DateTime.now(),
//         lastDate: DateTime(DateTime.now().year + 100),
//         firstDate: DateTime(DateTime.now().year - 100));
//   }
// }

class CustomTimePicker {
  static Future<TimeOfDay?> customizedTimePicker(BuildContext context) {
    return showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      // builder: (ctx, child) {
      //   return Theme(
      //     data: Get.theme.copyWith(
      //       colorScheme: ColorScheme.fromSeed(
      //           seedColor: kPrimaryColor, brightness: Get.theme.brightness),
      //     ),
      //     child: child!,
      //   );
      // },
    );
  }
}

{"v": "5.7.5", "fr": 100, "ip": 0, "op": 500, "w": 393, "h": 415, "nm": "Comp 1", "assets": [{"id": "0", "layers": [{"ind": 1, "ty": 4, "nm": "Path", "sr": 1, "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[29.054, 4.9], [29.694, 4.72], [30.174, 4.25], [30.354, 3.6], [30.174, 2.96], [29.694, 2.48], [29.054, 2.3], [28.404, 2.48], [27.934, 2.96], [27.754, 3.6], [27.934, 4.25], [28.404, 4.72], [29.054, 4.9], [29.054, 4.9]], "i": [[0, 0], [-0.193, 0.12], [-0.12, 0.193], [0, 0.233], [0.12, 0.193], [0.2, 0.12], [0.234, 0], [0.2, -0.12], [0.12, -0.2], [0, -0.233], [-0.12, -0.2], [-0.193, -0.12], [-0.233, 0], [0, 0]], "o": [[0.234, 0], [0.2, -0.12], [0.12, -0.2], [0, -0.233], [-0.12, -0.2], [-0.193, -0.12], [-0.233, 0], [-0.193, 0.12], [-0.12, 0.193], [0, 0.233], [0.12, 0.193], [0.2, 0.12], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[29.054, 7.2], [27.244, 6.72], [25.934, 5.42], [25.454, 3.6], [25.954, 1.8], [27.274, 0.49], [29.054, 0], [30.854, 0.49], [32.164, 1.79], [32.654, 3.6], [32.164, 5.42], [30.854, 6.72], [29.054, 7.2], [29.054, 7.2]], "i": [[0, 0], [0.547, 0.32], [0.327, 0.54], [0, 0.667], [-0.333, 0.547], [-0.546, 0.327], [-0.64, 0], [-0.54, -0.327], [-0.326, -0.547], [0, -0.667], [0.327, -0.547], [0.547, -0.327], [0.66, 0], [0, 0]], "o": [[-0.66, 0], [-0.546, -0.327], [-0.32, -0.547], [0, -0.653], [0.334, -0.547], [0.547, -0.327], [0.66, 0], [0.547, 0.32], [0.327, 0.54], [0, 0.667], [-0.326, 0.54], [-0.54, 0.32], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[37.454, 12.7], [38.094, 12.52], [38.574, 12.05], [38.754, 11.4], [38.574, 10.76], [38.094, 10.28], [37.454, 10.1], [36.804, 10.28], [36.334, 10.76], [36.154, 11.4], [36.334, 12.05], [36.804, 12.52], [37.454, 12.7], [37.454, 12.7]], "i": [[0, 0], [-0.193, 0.12], [-0.12, 0.193], [0, 0.233], [0.12, 0.193], [0.2, 0.12], [0.234, 0], [0.2, -0.12], [0.12, -0.2], [0, -0.233], [-0.12, -0.2], [-0.193, -0.12], [-0.233, 0], [0, 0]], "o": [[0.234, 0], [0.2, -0.12], [0.12, -0.2], [0, -0.233], [-0.12, -0.2], [-0.193, -0.12], [-0.233, 0], [-0.193, 0.12], [-0.12, 0.193], [0, 0.233], [0.12, 0.193], [0.2, 0.12], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[37.454, 15], [35.644, 14.52], [34.344, 13.22], [33.854, 11.4], [34.354, 9.6], [35.674, 8.29], [37.454, 7.8], [39.264, 8.29], [40.564, 9.59], [41.054, 11.4], [40.564, 13.22], [39.264, 14.52], [37.454, 15], [37.454, 15]], "i": [[0, 0], [0.547, 0.32], [0.327, 0.54], [0, 0.667], [-0.333, 0.547], [-0.546, 0.327], [-0.64, 0], [-0.546, -0.327], [-0.32, -0.547], [0, -0.667], [0.327, -0.547], [0.547, -0.327], [0.66, 0], [0, 0]], "o": [[-0.66, 0], [-0.54, -0.327], [-0.326, -0.547], [0, -0.653], [0.334, -0.547], [0.547, -0.327], [0.66, 0], [0.547, 0.32], [0.327, 0.54], [0, 0.667], [-0.32, 0.54], [-0.546, 0.32], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[27.854, 14.7], [26.054, 12.9], [38.654, 0.3], [40.454, 2.12], [27.854, 14.7], [27.854, 14.7]], "i": [[0, 0], [0.6, 0.6], [-4.2, 4.2], [-0.6, -0.607], [4.2, -4.193], [0, 0]], "o": [[-0.6, -0.6], [4.2, -4.2], [0.6, 0.607], [-4.2, 4.193], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[17.517, 12.42], [18.818, 12.08], [19.738, 11.16], [20.078, 9.86], [20.078, 5.12], [19.738, 3.82], [18.818, 2.9], [17.517, 2.56], [16.217, 2.9], [15.297, 3.82], [14.957, 5.12], [14.957, 9.86], [15.297, 11.16], [16.217, 12.08], [17.517, 12.42], [17.517, 12.42]], "i": [[0, 0], [-0.387, 0.227], [-0.227, 0.387], [0, 0.48], [0, 1.58], [0.226, 0.387], [0.386, 0.227], [0.48, 0], [0.387, -0.227], [0.227, -0.387], [0, -0.48], [0, -1.58], [-0.227, -0.387], [-0.387, -0.227], [-0.48, 0], [0, 0]], "o": [[0.48, 0], [0.386, -0.227], [0.226, -0.387], [0, -1.58], [0, -0.48], [-0.227, -0.387], [-0.387, -0.227], [-0.48, 0], [-0.387, 0.227], [-0.227, 0.387], [0, 1.58], [0, 0.48], [0.227, 0.387], [0.387, 0.227], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[17.517, 15], [14.717, 14.33], [12.847, 12.46], [12.177, 9.66], [12.177, 5.34], [12.847, 2.54], [14.717, 0.67], [17.517, 0], [20.318, 0.67], [22.188, 2.54], [22.858, 5.34], [22.858, 9.66], [22.188, 12.46], [20.318, 14.33], [17.517, 15], [17.517, 15]], "i": [[0, 0], [0.8, 0.447], [0.447, 0.8], [0, 1.067], [0, 1.44], [-0.447, 0.8], [-0.8, 0.447], [-1.067, 0], [-0.8, -0.447], [-0.447, -0.8], [0, -1.067], [0, -1.44], [0.446, -0.8], [0.8, -0.447], [1.066, 0], [0, 0]], "o": [[-1.067, 0], [-0.8, -0.447], [-0.447, -0.8], [0, -1.44], [0, -1.067], [0.447, -0.8], [0.8, -0.447], [1.066, 0], [0.8, 0.447], [0.446, 0.8], [0, 1.44], [0, 1.067], [-0.447, 0.8], [-0.8, 0.447], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[4.68, 15], [2.63, 14.55], [0.99, 13.3], [0, 11.44], [2.62, 10.74], [3.08, 11.62], [3.86, 12.2], [4.78, 12.4], [5.91, 12.1], [6.73, 11.29], [7.04, 10.16], [6.73, 9.03], [5.91, 8.22], [4.78, 7.92], [3.54, 8.18], [2.82, 8.66], [0.52, 7.96], [0.96, 0.3], [8.82, 0.3], [8.82, 2.84], [2.46, 2.84], [3.42, 1.94], [3.12, 6.88], [2.66, 6.32], [3.89, 5.68], [5.18, 5.48], [7.57, 6.09], [9.19, 7.75], [9.78, 10.16], [9.07, 12.62], [7.2, 14.36], [4.68, 15], [4.68, 15]], "i": [[0, 0], [0.64, 0.3], [0.46, 0.533], [0.2, 0.707], [-0.873, 0.233], [-0.22, -0.247], [-0.293, -0.14], [-0.313, 0], [-0.34, 0.2], [-0.2, 0.34], [0, 0.413], [0.207, 0.34], [0.347, 0.2], [0.413, 0], [0.32, -0.173], [0.16, -0.153], [0.767, 0.233], [-0.147, 2.553], [-2.62, 0], [0, -0.847], [2.12, 0], [-0.32, 0.3], [0.1, -1.647], [0.153, 0.187], [-0.46, 0.133], [-0.4, 0], [-0.687, -0.407], [-0.387, -0.707], [0, -0.907], [0.473, -0.733], [0.773, -0.433], [0.907, 0], [0, 0]], "o": [[-0.727, 0], [-0.633, -0.3], [-0.46, -0.533], [0.873, -0.233], [0.087, 0.34], [0.227, 0.247], [0.3, 0.133], [0.413, 0], [0.347, -0.2], [0.207, -0.34], [0, -0.413], [-0.2, -0.34], [-0.34, -0.2], [-0.507, 0], [-0.32, 0.167], [-0.767, -0.233], [0.147, -2.553], [2.62, 0], [0, 0.847], [-2.12, 0], [0.32, -0.3], [-0.1, 1.647], [-0.153, -0.187], [0.36, -0.293], [0.46, -0.133], [0.907, 0], [0.693, 0.4], [0.393, 0.7], [0, 0.907], [-0.473, 0.727], [-0.773, 0.427], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.157, 0.251]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 0, "k": [102.468, 20.8]}, "a": {"a": 0, "k": [20.527, 7.5]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}, {"ind": 2, "ty": 4, "nm": "Path", "sr": 1, "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[305.196, 7.12], [305.196, 3.52], [306.976, 3.52], [306.976, 7.12], [305.196, 7.12], [305.196, 7.12]], "i": [[0, 0], [0, 1.2], [-0.593, 0], [0, -1.2], [0.593, 0], [0, 0]], "o": [[0, -1.2], [0.593, 0], [0, 1.2], [-0.593, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[301.836, 7.12], [301.836, 3.52], [303.616, 3.52], [303.616, 7.12], [301.836, 7.12], [301.836, 7.12]], "i": [[0, 0], [0, 1.2], [-0.593, 0], [0, -1.2], [0.593, 0], [0, 0]], "o": [[0, -1.2], [0.593, 0], [0, 1.2], [-0.593, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[296.454, 14.32], [296.454, 3.52], [298.234, 3.52], [298.234, 14.32], [296.454, 14.32], [296.454, 14.32]], "i": [[0, 0], [0, 3.6], [-0.593, 0], [0, -3.6], [0.593, 0], [0, 0]], "o": [[0, -3.6], [0.593, 0], [0, 3.6], [-0.593, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[296.454, 17.92], [296.454, 16.08], [298.234, 16.08], [298.234, 17.92], [296.454, 17.92], [296.454, 17.92]], "i": [[0, 0], [0, 0.613], [-0.593, 0], [0, -0.613], [0.593, 0], [0, 0]], "o": [[0, -0.613], [0.593, 0], [0, 0.613], [-0.593, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[294.359, 6.9], [292.479, 6.9], [292.479, 10.12], [289.899, 13.64], [289.139, 12.26], [290.859, 9.88], [290.859, 6.9], [286.199, 6.9], [286.199, 10.94], [288.379, 14.26], [289.939, 15.06], [292.519, 17.88], [291.819, 19.8], [290.259, 19.14], [290.819, 17.9], [289.099, 16.26], [287.399, 15.38], [284.579, 11.24], [284.579, 6.9], [282.999, 6.9], [282.999, 5.48], [294.359, 5.48], [294.359, 6.9], [294.359, 6.9]], "i": [[0, 0], [0.627, 0], [0, -1.073], [1.8, -0.52], [0.253, 0.46], [0, 1.46], [0, 0.993], [1.553, 0], [0, -1.347], [-1.9, -0.98], [-0.52, -0.267], [0, -1.12], [0.44, -0.58], [0.52, 0.22], [0, 0.44], [1.42, 0.74], [0.567, 0.293], [0, 2.24], [0, 1.447], [0.527, 0], [0, 0.473], [-3.787, 0], [0, -0.473], [0, 0]], "o": [[-0.627, 0], [0, 1.073], [0, 2.14], [-0.253, -0.46], [1.34, -0.4], [0, -0.993], [-1.553, 0], [0, 1.347], [0, 1.98], [0.52, 0.267], [2.02, 1.04], [0, 0.68], [-0.52, -0.22], [0.36, -0.4], [0, -0.64], [-0.567, -0.293], [-2.32, -1.2], [0, -1.447], [-0.527, 0], [0, -0.473], [3.787, 0], [0, 0.473], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[269.445, 6.9], [269.445, 5.48], [283.285, 5.48], [283.285, 6.9], [281.225, 6.9], [281.225, 17.92], [279.605, 17.92], [279.605, 10.76], [279.365, 10.76], [276.645, 14.06], [275.045, 13.58], [275.705, 11.4], [273.985, 10.76], [271.965, 12.84], [275.485, 16.96], [274.545, 18.2], [270.345, 12.68], [273.825, 9.32], [276.525, 10.34], [279.365, 9.36], [279.605, 9.36], [279.605, 6.9], [269.445, 6.9], [269.445, 6.9]], "i": [[0, 0], [0, 0.473], [-4.613, 0], [0, -0.473], [0.687, 0], [0, -3.673], [0.54, 0], [0, 2.387], [0.08, 0], [0.42, -2.46], [0.533, 0.16], [-0.32, 0.6], [0.72, 0], [0, -1.26], [-2.46, -1.4], [0.313, -0.413], [0, 2.28], [-2.04, 0], [-0.68, -0.62], [-1.18, 0], [-0.08, 0], [0, 0.82], [3.387, 0], [0, 0]], "o": [[0, -0.473], [4.613, 0], [0, 0.473], [-0.687, 0], [0, 3.673], [-0.54, 0], [0, -2.387], [-0.08, 0], [-1.36, 0], [-0.533, -0.16], [0.12, -0.86], [-0.44, -0.4], [-1.26, 0], [0, 1.52], [-0.313, 0.413], [-2.62, -1.54], [0, -2.22], [1.14, 0], [0.72, -0.66], [0.08, 0], [0, -0.82], [-3.387, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[267.669, 6.9], [267.669, 17.92], [266.049, 17.92], [266.049, 6.9], [264.269, 6.9], [264.269, 5.48], [265.869, 5.48], [265.329, 3.38], [270.189, 0], [281.409, 5.62], [279.629, 5.62], [270.589, 1.44], [266.949, 3.68], [267.549, 5.48], [269.729, 5.48], [269.729, 6.9], [267.669, 6.9], [267.669, 6.9]], "i": [[0, 0], [0, -3.673], [0.54, 0], [0, 3.673], [0.593, 0], [0, 0.473], [-0.533, 0], [0, 0.86], [-2.88, 0], [-2.26, -3.1], [0.593, 0], [3.42, 0], [0, -1.5], [-0.34, -0.46], [-0.727, 0], [0, -0.473], [0.687, 0], [0, 0]], "o": [[0, 3.673], [-0.54, 0], [0, -3.673], [-0.593, 0], [0, -0.473], [0.533, 0], [-0.32, -0.6], [0, -2.2], [5, 0], [-0.593, 0], [-2.32, -2.66], [-2.24, 0], [0, 0.78], [0.727, 0], [0, 0.473], [-0.687, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[252.471, 5.62], [249.611, 1.46], [247.951, 1.78], [247.511, 0.36], [249.511, 0], [253.951, 5.62], [252.471, 5.62], [252.471, 5.62]], "i": [[0, 0], [1.36, 0], [0.48, -0.18], [0.147, 0.473], [-0.68, 0], [-1.38, -4.48], [0.493, 0], [0, 0]], "o": [[-1.1, -3.56], [-0.56, 0], [-0.147, -0.473], [0.56, -0.22], [2.34, 0], [-0.493, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[260.573, 6.9], [254.073, 6.9], [254.073, 10.76], [256.413, 9.92], [259.433, 13.12], [257.753, 17.08], [256.353, 16.2], [257.833, 13.1], [256.233, 11.32], [254.073, 12.8], [254.073, 17.92], [252.453, 17.92], [252.453, 14.52], [249.573, 15.56], [245.833, 12.1], [249.953, 8.64], [251.753, 8.84], [251.613, 10.3], [249.993, 10.1], [247.453, 12.16], [249.573, 14.12], [252.453, 12.58], [252.453, 6.9], [244.913, 6.9], [244.913, 5.48], [260.573, 5.48], [260.573, 6.9], [260.573, 6.9]], "i": [[0, 0], [2.167, 0], [0, -1.287], [-0.98, 0], [0, -1.98], [1.18, -1.3], [0.467, 0.293], [0, 1.22], [1.02, 0], [0.5, -0.9], [0, -1.707], [0.54, 0], [0, 1.133], [1.2, 0], [0, 2.3], [-2.6, 0], [-0.5, -0.12], [0.047, -0.487], [0.6, 0], [0, -1.34], [-1.2, 0], [-0.6, 0.86], [0, 1.893], [2.513, 0], [0, 0.473], [-5.22, 0], [0, -0.473], [0, 0]], "o": [[-2.167, 0], [0, 1.287], [0.56, -0.5], [1.9, 0], [0, 1.4], [-0.467, -0.293], [0.88, -0.9], [0, -1.16], [-0.84, 0], [0, 1.707], [-0.54, 0], [0, -1.133], [-0.72, 0.64], [-2.04, 0], [0, -2.18], [0.68, 0], [-0.047, 0.487], [-0.46, -0.12], [-1.64, 0], [0, 1.32], [1.22, 0], [0, -1.893], [-2.513, 0], [0, -0.473], [5.22, 0], [0, 0.473], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[241.506, 6.9], [236.746, 6.9], [236.746, 10.78], [238.826, 13.28], [241.506, 12], [241.506, 6.9], [241.506, 6.9]], "i": [[0, 0], [1.587, 0], [0, -1.293], [-1.34, 0], [-0.68, 0.72], [0, 1.7], [0, 0]], "o": [[-1.587, 0], [0, 1.293], [0, 1.72], [1.02, 0], [0, -1.7], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[243.126, 6.9], [243.126, 17.92], [241.506, 17.92], [241.506, 13.9], [238.866, 14.7], [235.126, 10.72], [235.126, 6.9], [233.546, 6.9], [233.546, 5.48], [245.206, 5.48], [245.206, 6.9], [243.126, 6.9], [243.126, 6.9]], "i": [[0, 0], [0, -3.673], [0.54, 0], [0, 1.34], [1.04, 0], [0, 2.66], [0, 1.273], [0.527, 0], [0, 0.473], [-3.887, 0], [0, -0.473], [0.693, 0], [0, 0]], "o": [[0, 3.673], [-0.54, 0], [0, -1.34], [-0.66, 0.48], [-2.38, 0], [0, -1.273], [-0.527, 0], [0, -0.473], [3.887, 0], [0, 0.473], [-0.693, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[222.597, 13.6], [219.197, 16.68], [213.677, 11.04], [215.097, 10.46], [219.057, 15.22], [221.017, 13.42], [219.617, 11.22], [217.837, 11.52], [217.517, 10.1], [220.677, 8.14], [219.017, 6.7], [216.397, 7.5], [215.877, 6.1], [219.077, 5.28], [222.277, 8.16], [220.997, 10.62], [221.037, 10.66], [222.997, 10.88], [224.977, 10.64], [224.977, 6.9], [223.517, 6.9], [223.517, 5.48], [233.837, 5.48], [233.837, 6.9], [231.777, 6.9], [231.777, 17.92], [230.157, 17.92], [230.157, 6.9], [226.597, 6.9], [226.597, 17.92], [224.977, 17.92], [224.977, 12.12], [223.317, 12.3], [222.237, 12.22], [222.597, 13.6], [222.597, 13.6]], "i": [[0, 0], [1.96, 0], [1.54, 4.24], [-0.473, 0.193], [-1.74, 0], [0, 1.14], [0.78, 0.56], [0.68, -0.06], [0.107, 0.473], [0, 1.1], [1.08, 0], [0.86, -0.52], [0.173, 0.467], [-1.24, 0], [0, -1.54], [0.9, -0.58], [-0.013, -0.013], [-0.62, 0], [-0.7, 0.18], [0, 1.247], [0.487, 0], [0, 0.473], [-3.44, 0], [0, -0.473], [0.687, 0], [0, -3.673], [0.54, 0], [0, 3.673], [1.187, 0], [0, -3.673], [0.54, 0], [0, 1.933], [0.56, 0], [0.38, 0.06], [0, -0.48], [0, 0]], "o": [[0, 1.96], [-2.38, 0], [0.473, -0.193], [1.08, 3.12], [1.18, 0], [0, -0.92], [-0.5, 0.14], [-0.107, -0.473], [2.5, -0.32], [0, -0.88], [-0.9, 0], [-0.173, -0.467], [0.92, -0.52], [2.28, 0], [0, 1.04], [0.013, 0.013], [0.64, 0.16], [0.62, 0], [0, -1.247], [-0.487, 0], [0, -0.473], [3.44, 0], [0, 0.473], [-0.687, 0], [0, 3.673], [-0.54, 0], [0, -3.673], [-1.187, 0], [0, 3.673], [-0.54, 0], [0, -1.933], [-0.54, 0.14], [-0.32, 0], [0.24, 0.42], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[201, 5.62], [199.8, 2.72], [202.8, 0], [204.86, 0.48], [204.44, 1.7], [203, 1.38], [201.4, 2.96], [202.64, 5.62], [201, 5.62], [201, 5.62]], "i": [[0, 0], [0, 1.1], [-1.74, 0], [-0.52, -0.3], [0.14, -0.407], [0.54, 0], [0, -0.98], [-0.78, -0.9], [0.547, 0], [0, 0]], "o": [[-0.64, -0.72], [0, -1.84], [0.84, 0], [-0.14, 0.407], [-0.48, -0.18], [-1.04, 0], [0, 0.9], [-0.547, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[196.863, 6.9], [196.863, 10.68], [198.783, 13.3], [201.243, 12.18], [201.243, 6.9], [196.863, 6.9], [196.863, 6.9]], "i": [[0, 0], [0, -1.26], [-1.24, 0], [-0.68, 0.64], [0, 1.76], [1.46, 0], [0, 0]], "o": [[0, 1.26], [0, 2], [0.88, 0], [0, -1.76], [-1.46, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[202.863, 6.9], [202.863, 10.76], [205.163, 9.94], [208.223, 13.14], [206.523, 17.1], [205.143, 16.22], [206.623, 13.14], [205.083, 11.32], [202.863, 12.6], [202.863, 17.92], [201.243, 17.92], [201.243, 13.92], [198.723, 14.72], [195.243, 10.72], [195.243, 6.9], [193.663, 6.9], [193.663, 5.48], [209.383, 5.48], [209.383, 6.9], [202.863, 6.9], [202.863, 6.9]], "i": [[0, 0], [0, -1.287], [-0.9, 0], [0, -2.06], [1.16, -1.3], [0.46, 0.293], [0, 1.12], [0.96, 0], [0.56, -0.78], [0, -1.773], [0.54, 0], [0, 1.333], [0.98, 0], [0, 2.52], [0, 1.273], [0.527, 0], [0, 0.473], [-5.24, 0], [0, -0.473], [2.173, 0], [0, 0]], "o": [[0, 1.287], [0.66, -0.54], [1.88, 0], [0, 1.42], [-0.46, -0.293], [0.88, -0.92], [0, -1.2], [-0.94, 0], [0, 1.773], [-0.54, 0], [0, -1.333], [-0.64, 0.5], [-2.48, 0], [0, -1.273], [-0.527, 0], [0, -0.473], [5.24, 0], [0, 0.473], [-2.173, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[188.128, 11.6], [190.268, 11.32], [190.268, 6.9], [185.968, 6.9], [186.288, 8.92], [185.668, 11.2], [188.128, 11.6], [188.128, 11.6]], "i": [[0, 0], [-0.7, 0.2], [0, 1.473], [1.433, 0], [0, -0.8], [0.42, -0.64], [-0.9, 0], [0, 0]], "o": [[0.76, 0], [0, -1.473], [-1.433, 0], [0.22, 0.58], [0, 0.88], [0.82, 0.3], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[185.888, 18.2], [181.548, 12.9], [180.988, 11.12], [182.068, 10.04], [183.708, 11], [184.668, 8.72], [184.328, 6.9], [180.148, 6.9], [180.148, 5.48], [193.948, 5.48], [193.948, 6.9], [191.888, 6.9], [191.888, 17.92], [190.268, 17.92], [190.268, 12.8], [188.148, 13.04], [184.708, 12.26], [183.188, 13.02], [187.108, 17.12], [185.888, 18.2], [185.888, 18.2]], "i": [[0, 0], [0.72, 1.32], [0, 0.5], [-0.68, 0], [-0.52, -0.6], [0, 0.88], [0.18, 0.42], [1.393, 0], [0, 0.473], [-4.6, 0], [0, -0.473], [0.687, 0], [0, -3.673], [0.54, 0], [0, 1.707], [0.74, 0], [1.12, 0.58], [0.6, -0.2], [-1.6, -1.48], [0.407, -0.36], [0, 0]], "o": [[-2, -1.96], [-0.38, -0.68], [0, -0.7], [0.58, 0], [0.72, -0.5], [0, -0.74], [-1.393, 0], [0, -0.473], [4.6, 0], [0, 0.473], [-0.687, 0], [0, 3.673], [-0.54, 0], [0, -1.707], [-0.68, 0.16], [-1.06, 0], [-0.4, 0.3], [0.96, 1.3], [-0.407, 0.36], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[178.372, 6.9], [178.372, 17.92], [176.752, 17.92], [176.752, 6.9], [174.972, 6.9], [174.972, 5.48], [176.572, 5.48], [176.032, 3.38], [180.892, 0], [192.112, 5.62], [190.332, 5.62], [181.292, 1.44], [177.652, 3.68], [178.252, 5.48], [180.432, 5.48], [180.432, 6.9], [178.372, 6.9], [178.372, 6.9]], "i": [[0, 0], [0, -3.673], [0.54, 0], [0, 3.673], [0.593, 0], [0, 0.473], [-0.533, 0], [0, 0.86], [-2.88, 0], [-2.26, -3.1], [0.593, 0], [3.42, 0], [0, -1.5], [-0.34, -0.46], [-0.727, 0], [0, -0.473], [0.687, 0], [0, 0]], "o": [[0, 3.673], [-0.54, 0], [0, -3.673], [-0.593, 0], [0, -0.473], [0.533, 0], [-0.32, -0.6], [0, -2.2], [5, 0], [-0.593, 0], [-2.32, -2.66], [-2.24, 0], [0, 0.78], [0.727, 0], [0, 0.473], [-0.687, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[166.602, 20.16], [166.712, 19.33], [167.342, 19.17], [167.612, 18.65], [167.632, 17.92], [166.602, 17.92], [166.602, 15.66], [168.562, 15.66], [168.562, 18.22], [168.042, 19.8], [166.602, 20.16], [166.602, 20.16]], "i": [[0, 0], [-0.037, 0.277], [-0.14, 0.127], [-0.04, 0.22], [0.026, 0.267], [0.343, 0], [0, 0.753], [-0.653, 0], [0, -0.853], [0.346, -0.353], [0.62, 0.113], [0, 0]], "o": [[0.037, -0.277], [0.28, 0.02], [0.14, -0.127], [0.04, -0.22], [-0.343, 0], [0, -0.753], [0.653, 0], [0, 0.853], [0, 0.7], [-0.34, 0.353], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[165.683, 6.9], [163.323, 6.9], [163.323, 10.76], [161.663, 10.76], [157.843, 13.58], [160.983, 16.44], [164.323, 15.22], [165.003, 16.52], [160.963, 17.92], [156.243, 13.56], [161.443, 9.36], [161.703, 9.36], [161.703, 6.9], [155.323, 6.9], [155.323, 5.48], [165.683, 5.48], [165.683, 6.9], [165.683, 6.9]], "i": [[0, 0], [0.787, 0], [0, -1.287], [0.553, 0], [0, -2], [-1.74, 0], [-1.16, 0.82], [-0.227, -0.433], [1.72, 0], [0, 2.7], [-3.34, 0], [-0.087, 0], [0, 0.82], [2.127, 0], [0, 0.473], [-3.453, 0], [0, -0.473], [0, 0]], "o": [[-0.787, 0], [0, 1.287], [-0.553, 0], [-2.46, 0], [0, 1.88], [1.24, 0], [0.227, 0.433], [-1.06, 0.88], [-2.76, 0], [0, -2.8], [0.087, 0], [0, -0.82], [-2.127, 0], [0, -0.473], [3.453, 0], [0, 0.473], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[152.736, 23.4], [148.216, 19.06], [146.316, 20.52], [147.876, 21.84], [149.496, 21.36], [150.096, 22.64], [147.916, 23.28], [144.816, 20.48], [148.236, 17.7], [154.096, 22.68], [152.736, 23.4], [152.736, 23.4]], "i": [[0, 0], [1.88, 0], [0, -0.94], [-0.86, 0], [-0.54, 0.36], [-0.2, -0.427], [0.84, 0], [0, 1.68], [-2.12, 0], [-1.52, -3.22], [0.453, -0.24], [0, 0]], "o": [[-1.5, -3.18], [-1.2, 0], [0, 0.92], [0.58, 0], [0.2, 0.427], [-0.62, 0.46], [-1.94, 0], [0, -1.76], [2.72, 0], [-0.453, 0.24], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[147.62, 16.52], [152.8, 11.76], [151.12, 9.54], [149.78, 10.86], [151.84, 12.74], [151.22, 13.96], [148.24, 10.86], [150.4, 8.32], [150.4, 6.9], [141.3, 6.9], [141.3, 5.48], [155.6, 5.48], [155.6, 6.9], [152, 6.9], [152, 8.34], [154.36, 11.92], [147.62, 17.92], [143.18, 14.86], [143.88, 13.12], [142.16, 10.78], [145.38, 8.26], [147.06, 8.44], [146.94, 9.76], [145.6, 9.62], [143.78, 10.92], [145.2, 12.32], [146.56, 12.12], [147.46, 12.18], [147.34, 13.6], [146.7, 13.56], [144.8, 14.96], [147.62, 16.52], [147.62, 16.52]], "i": [[0, 0], [0, 2.9], [1.16, 0], [0, -0.76], [-1.3, -0.38], [0.207, -0.407], [0, 1.5], [-1.48, 0.24], [0, 0.473], [3.033, 0], [0, 0.473], [-4.767, 0], [0, -0.473], [1.2, 0], [0, -0.48], [0, -2.12], [4.26, 0], [0, 1.82], [-0.5, 0.48], [0, 1.06], [-2.02, 0], [-0.38, -0.1], [0.04, -0.44], [0.5, 0], [0, -0.86], [-0.78, -0.2], [-0.5, 0], [-0.24, -0.04], [0.04, -0.473], [0.18, 0], [0, -0.88], [-1.74, 0], [0, 0]], "o": [[2.84, 0], [0, -1.38], [-0.84, 0], [0, 0.94], [-0.207, 0.407], [-2.02, -0.52], [0, -1.3], [0, -0.473], [-3.033, 0], [0, -0.473], [4.767, 0], [0, 0.473], [-1.2, 0], [0, 0.48], [1.48, 0.3], [0, 3.08], [-2.94, 0], [0, -0.6], [-1.08, -0.48], [0, -1.64], [0.7, 0], [-0.04, 0.44], [-0.44, -0.08], [-1.16, 0], [0, 0.78], [0.42, -0.14], [0.34, 0], [-0.04, 0.473], [-0.26, -0.04], [-1.32, 0], [0, 1.06], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[135.52, 6.9], [135.52, 17.92], [133.9, 17.92], [133.9, 6.9], [132.12, 6.9], [132.12, 5.48], [133.76, 5.48], [127.92, 1.44], [125.14, 3.58], [125.84, 5.62], [124.16, 5.62], [123.52, 3.36], [127.62, 0], [135.44, 5.48], [137.58, 5.48], [137.58, 6.9], [135.52, 6.9], [135.52, 6.9]], "i": [[0, 0], [0, -3.673], [0.54, 0], [0, 3.673], [0.593, 0], [0, 0.473], [-0.547, 0], [2.18, 0], [0, -1.32], [-0.34, -0.52], [0.56, 0], [0, 0.86], [-2.52, 0], [-1.9, -3.44], [-0.713, 0], [0, -0.473], [0.687, 0], [0, 0]], "o": [[0, 3.673], [-0.54, 0], [0, -3.673], [-0.593, 0], [0, -0.473], [0.547, 0], [-1.74, -2.7], [-1.74, 0], [0, 0.88], [-0.56, 0], [-0.38, -0.7], [0, -2.02], [3.42, 0], [0.713, 0], [0, 0.473], [-0.687, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[132.429, 6.9], [125.929, 6.9], [125.929, 10.76], [128.269, 9.92], [131.289, 13.12], [129.609, 17.08], [128.209, 16.2], [129.689, 13.1], [128.089, 11.32], [125.929, 12.8], [125.929, 17.92], [124.309, 17.92], [124.309, 14.52], [121.429, 15.56], [117.689, 12.1], [121.809, 8.64], [123.609, 8.84], [123.469, 10.3], [121.849, 10.1], [119.309, 12.16], [121.429, 14.12], [124.309, 12.58], [124.309, 6.9], [116.769, 6.9], [116.769, 5.48], [132.429, 5.48], [132.429, 6.9], [132.429, 6.9]], "i": [[0, 0], [2.167, 0], [0, -1.287], [-0.98, 0], [0, -1.98], [1.18, -1.3], [0.467, 0.293], [0, 1.22], [1.02, 0], [0.5, -0.9], [0, -1.707], [0.54, 0], [0, 1.133], [1.2, 0], [0, 2.3], [-2.6, 0], [-0.5, -0.12], [0.047, -0.487], [0.6, 0], [0, -1.34], [-1.2, 0], [-0.6, 0.86], [0, 1.893], [2.513, 0], [0, 0.473], [-5.22, 0], [0, -0.473], [0, 0]], "o": [[-2.167, 0], [0, 1.287], [0.56, -0.5], [1.9, 0], [0, 1.4], [-0.467, -0.293], [0.88, -0.9], [0, -1.16], [-0.84, 0], [0, 1.707], [-0.54, 0], [0, -1.133], [-0.72, 0.64], [-2.04, 0], [0, -2.18], [0.68, 0], [-0.047, 0.487], [-0.46, -0.12], [-1.64, 0], [0, 1.32], [1.22, 0], [0, -1.893], [-2.513, 0], [0, -0.473], [5.22, 0], [0, 0.473], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[113.073, 6.9], [106.573, 6.9], [106.573, 10.76], [108.913, 9.92], [111.933, 13.12], [110.253, 17.08], [108.853, 16.2], [110.333, 13.1], [108.733, 11.32], [106.573, 12.8], [106.573, 17.92], [104.953, 17.92], [104.953, 14.52], [102.073, 15.56], [98.333, 12.1], [102.453, 8.64], [104.253, 8.84], [104.113, 10.3], [102.493, 10.1], [99.953, 12.16], [102.073, 14.12], [104.953, 12.58], [104.953, 6.9], [97.413, 6.9], [97.413, 5.48], [113.073, 5.48], [113.073, 6.9], [113.073, 6.9]], "i": [[0, 0], [2.167, 0], [0, -1.287], [-0.98, 0], [0, -1.98], [1.18, -1.3], [0.467, 0.293], [0, 1.22], [1.02, 0], [0.5, -0.9], [0, -1.707], [0.54, 0], [0, 1.133], [1.2, 0], [0, 2.3], [-2.6, 0], [-0.5, -0.12], [0.047, -0.487], [0.6, 0], [0, -1.34], [-1.2, 0], [-0.6, 0.86], [0, 1.893], [2.513, 0], [0, 0.473], [-5.22, 0], [0, -0.473], [0, 0]], "o": [[-2.167, 0], [0, 1.287], [0.56, -0.5], [1.9, 0], [0, 1.4], [-0.467, -0.293], [0.88, -0.9], [0, -1.16], [-0.84, 0], [0, 1.707], [-0.54, 0], [0, -1.133], [-0.72, 0.64], [-2.04, 0], [0, -2.18], [0.68, 0], [-0.047, 0.487], [-0.46, -0.12], [-1.64, 0], [0, 1.32], [1.22, 0], [0, -1.893], [-2.513, 0], [0, -0.473], [5.22, 0], [0, 0.473], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[95.627, 6.9], [95.627, 17.92], [94.007, 17.92], [94.007, 11.36], [91.227, 11.36], [88.527, 13.3], [90.967, 17.18], [89.827, 18.2], [86.907, 13.1], [90.947, 9.94], [94.007, 9.94], [94.007, 6.9], [86.007, 6.9], [86.007, 5.48], [97.687, 5.48], [97.687, 6.9], [95.627, 6.9], [95.627, 6.9]], "i": [[0, 0], [0, -3.673], [0.54, 0], [0, 2.187], [0.927, 0], [0, -1.42], [-1.6, -1.52], [0.38, -0.34], [0, 1.78], [-2.46, 0], [-1.02, 0], [0, 1.013], [2.667, 0], [0, 0.473], [-3.893, 0], [0, -0.473], [0.687, 0], [0, 0]], "o": [[0, 3.673], [-0.54, 0], [0, -2.187], [-0.927, 0], [-1.68, 0], [0, 1.3], [-0.38, 0.34], [-1.88, -1.82], [0, -2.16], [1.02, 0], [0, -1.013], [-2.667, 0], [0, -0.473], [3.893, 0], [0, 0.473], [-0.687, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[28.206, 2.48], [29.305, 1.32], [30.406, 2.48], [29.305, 3.64], [28.206, 2.48], [28.206, 2.48]], "i": [[0, 0], [-0.6, 0], [0, -0.64], [0.6, 0], [0, 0.64], [0, 0]], "o": [[0, -0.64], [0.6, 0], [0, 0.64], [-0.6, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[35.043, 6.9], [33.163, 6.9], [33.163, 10.12], [30.583, 13.64], [29.823, 12.26], [31.543, 9.88], [31.543, 6.9], [26.883, 6.9], [26.883, 10.94], [29.063, 14.26], [30.623, 15.06], [33.203, 17.88], [32.503, 19.8], [30.943, 19.14], [31.503, 17.9], [29.783, 16.26], [28.083, 15.38], [25.263, 11.24], [25.263, 6.9], [23.683, 6.9], [23.683, 5.48], [35.043, 5.48], [35.043, 6.9], [35.043, 6.9]], "i": [[0, 0], [0.627, 0], [0, -1.073], [1.8, -0.52], [0.253, 0.46], [0, 1.46], [0, 0.993], [1.553, 0], [0, -1.347], [-1.9, -0.98], [-0.52, -0.267], [0, -1.12], [0.44, -0.58], [0.52, 0.22], [0, 0.44], [1.42, 0.74], [0.567, 0.293], [0, 2.24], [0, 1.447], [0.527, 0], [0, 0.473], [-3.787, 0], [0, -0.473], [0, 0]], "o": [[-0.627, 0], [0, 1.073], [0, 2.14], [-0.253, -0.46], [1.34, -0.4], [0, -0.993], [-1.553, 0], [0, 1.347], [0, 1.98], [0.52, 0.267], [2.02, 1.04], [0, 0.68], [-0.52, -0.22], [0.36, -0.4], [0, -0.64], [-0.567, -0.293], [-2.32, -1.2], [0, -1.447], [-0.527, 0], [0, -0.473], [3.787, 0], [0, 0.473], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[21.907, 6.9], [21.907, 17.92], [20.287, 17.92], [20.287, 6.9], [18.507, 6.9], [18.507, 5.48], [23.967, 5.48], [23.967, 6.9], [21.907, 6.9], [21.907, 6.9]], "i": [[0, 0], [0, -3.673], [0.54, 0], [0, 3.673], [0.593, 0], [0, 0.473], [-1.82, 0], [0, -0.473], [0.687, 0], [0, 0]], "o": [[0, 3.673], [-0.54, 0], [0, -3.673], [-0.593, 0], [0, -0.473], [1.82, 0], [0, 0.473], [-0.687, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[15.1, 6.9], [10.34, 6.9], [10.34, 10.78], [12.42, 13.28], [15.1, 12], [15.1, 6.9], [15.1, 6.9]], "i": [[0, 0], [1.587, 0], [0, -1.293], [-1.34, 0], [-0.68, 0.72], [0, 1.7], [0, 0]], "o": [[-1.587, 0], [0, 1.293], [0, 1.72], [1.02, 0], [0, -1.7], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[16.72, 6.9], [16.72, 17.92], [15.1, 17.92], [15.1, 13.9], [12.46, 14.7], [8.72, 10.72], [8.72, 6.9], [7.14, 6.9], [7.14, 5.48], [18.8, 5.48], [18.8, 6.9], [16.72, 6.9], [16.72, 6.9]], "i": [[0, 0], [0, -3.673], [0.54, 0], [0, 1.34], [1.04, 0], [0, 2.66], [0, 1.273], [0.527, 0], [0, 0.473], [-3.887, 0], [0, -0.473], [0.693, 0], [0, 0]], "o": [[0, 3.673], [-0.54, 0], [0, -1.34], [-0.66, 0.48], [-2.38, 0], [0, -1.273], [-0.527, 0], [0, -0.473], [3.887, 0], [0, 0.473], [-0.693, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[3.36, 7.12], [3.36, 3.52], [5.14, 3.52], [5.14, 7.12], [3.36, 7.12], [3.36, 7.12]], "i": [[0, 0], [0, 1.2], [-0.593, 0], [0, -1.2], [0.593, 0], [0, 0]], "o": [[0, -1.2], [0.593, 0], [0, 1.2], [-0.593, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[0, 7.12], [0, 3.52], [1.78, 3.52], [1.78, 7.12], [0, 7.12], [0, 7.12]], "i": [[0, 0], [0, 1.2], [-0.593, 0], [0, -1.2], [0.593, 0], [0, 0]], "o": [[0, -1.2], [0.593, 0], [0, 1.2], [-0.593, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.047, 0.016, 0.137]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 0, "k": [195.688, 21.78]}, "a": {"a": 0, "k": [153.488, 11.7]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}, {"ind": 3, "ty": 4, "nm": "Rectangle", "sr": 1, "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "Rectangle", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [393, 40]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [196.5, 20]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "1", "layers": [{"ind": 4, "ty": 4, "nm": "!: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [4, 12]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "!", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[260.127, 49.629], [259.878, 50.83], [259.204, 51.797], [258.237, 52.456], [257.05, 52.705], [255.849, 52.456], [254.868, 51.797], [254.209, 50.83], [253.974, 49.629], [254.209, 48.428], [254.868, 47.447], [255.849, 46.773], [257.05, 46.524], [258.237, 46.773], [259.204, 47.447], [259.878, 48.428], [260.127, 49.629], [260.127, 49.629]], "i": [[0, 0], [0.166, -0.371], [0.283, -0.273], [0.371, -0.156], [0.42, 0], [0.371, 0.166], [0.283, 0.283], [0.166, 0.372], [0, 0.43], [-0.157, 0.381], [-0.274, 0.273], [-0.371, 0.166], [-0.429, 0], [-0.371, -0.166], [-0.274, -0.284], [-0.167, -0.381], [0, -0.42], [0, 0]], "o": [[0, 0.43], [-0.167, 0.372], [-0.274, 0.283], [-0.371, 0.166], [-0.429, 0], [-0.371, -0.156], [-0.274, -0.273], [-0.157, -0.371], [0, -0.42], [0.166, -0.381], [0.283, -0.284], [0.371, -0.166], [0.42, 0], [0.371, 0.166], [0.283, 0.273], [0.166, 0.381], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-257.505, -41.206]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[261.093, 29.707], [259.892, 45.118], [254.472, 45.118], [253.916, 29.707], [261.093, 29.707], [261.093, 29.707]], "i": [[0, 0], [0.4, -5.137], [1.807, 0], [0.185, 5.137], [-2.392, 0], [0, 0]], "o": [[-0.4, 5.137], [-1.807, 0], [-0.185, -5.137], [2.392, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-257.505, -41.206]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 120, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 126, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 128, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 130, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "2", "layers": [{"ind": 5, "ty": 4, "nm": "O: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [10, 10]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "O", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[244.886, 40.635], [244.696, 39.419], [244.169, 38.365], [243.29, 37.618], [242.103, 37.325], [240.873, 37.574], [239.935, 38.262], [239.349, 39.273], [239.144, 40.489], [239.32, 41.719], [239.847, 42.832], [240.711, 43.638], [241.927, 43.946], [243.158, 43.682], [244.095, 42.95], [244.681, 41.88], [244.886, 40.635], [244.886, 40.635]], "i": [[0, 0], [0.127, 0.391], [0.234, 0.302], [0.351, 0.185], [0.449, 0], [0.361, -0.166], [0.264, -0.293], [0.137, -0.391], [0, -0.43], [-0.117, -0.41], [-0.234, -0.332], [-0.341, -0.205], [-0.459, 0], [-0.362, 0.176], [-0.254, 0.302], [-0.136, 0.401], [0, 0.43], [0, 0]], "o": [[0, -0.42], [-0.117, -0.4], [-0.235, -0.313], [-0.342, -0.196], [-0.459, 0], [-0.362, 0.166], [-0.254, 0.283], [-0.137, 0.381], [0, 0.41], [0.117, 0.41], [0.235, 0.332], [0.352, 0.205], [0.459, 0], [0.371, -0.186], [0.254, -0.313], [0.137, -0.4], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-242.089, -40.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[251.39, 40.254], [251.083, 42.832], [250.189, 45.147], [248.783, 47.139], [246.922, 48.677], [244.681, 49.688], [242.103, 50.039], [239.584, 49.703], [237.357, 48.765], [235.467, 47.3], [234.032, 45.396], [233.109, 43.14], [232.787, 40.635], [233.094, 38.145], [233.973, 35.83], [235.379, 33.838], [237.211, 32.271], [239.422, 31.246], [241.927, 30.879], [245.795, 31.538], [248.783, 33.428], [250.702, 36.387], [251.39, 40.254], [251.39, 40.254]], "i": [[0, 0], [0.205, -0.83], [0.391, -0.723], [0.556, -0.605], [0.694, -0.43], [0.811, -0.244], [0.908, 0], [0.8, 0.224], [0.693, 0.4], [0.567, 0.567], [0.4, 0.693], [0.225, 0.801], [0, 0.869], [-0.205, 0.81], [-0.381, 0.723], [-0.546, 0.606], [-0.674, 0.439], [-0.791, 0.244], [-0.879, 0], [-1.163, -0.439], [-0.821, -0.82], [-0.449, -1.162], [0, -1.425], [0, 0]], "o": [[0, 0.889], [-0.205, 0.821], [-0.381, 0.723], [-0.547, 0.596], [-0.683, 0.43], [-0.81, 0.234], [-0.879, 0], [-0.791, -0.225], [-0.693, -0.41], [-0.556, -0.576], [-0.391, -0.703], [-0.215, -0.801], [0, -0.849], [0.205, -0.82], [0.391, -0.722], [0.547, -0.605], [0.683, -0.44], [0.791, -0.245], [1.416, 0], [1.171, 0.44], [0.83, 0.811], [0.459, 1.152], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-242.089, -40.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 115, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 121, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 123, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 125, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "3", "layers": [{"ind": 6, "ty": 4, "nm": "L: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [7, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "L", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[231.096, 43.711], [230.481, 50.215], [218.147, 51.182], [218.616, 30.264], [226.233, 30.264], [225.149, 43.711], [231.096, 43.711], [231.096, 43.711]], "i": [[0, 0], [0.205, -2.168], [4.111, -0.322], [-0.156, 6.973], [-2.539, 0], [0.361, -4.482], [-1.982, 0], [0, 0]], "o": [[-0.205, 2.168], [-4.111, 0.322], [0.156, -6.973], [2.539, 0], [-0.361, 4.482], [1.982, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-224.622, -40.723]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 110, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 116, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 118, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 120, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "4", "layers": [{"ind": 7, "ty": 4, "nm": "H: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "H", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[207.999, 50.889], [200.674, 51.241], [200.616, 45.059], [197.247, 45.059], [197.042, 51.475], [190.479, 51.475], [191.036, 30.879], [198.008, 31.202], [197.569, 41.719], [200.821, 41.719], [200.85, 30.704], [207.53, 30.879], [207.999, 50.889], [207.999, 50.889]], "i": [[0, 0], [2.442, -0.117], [0.019, 2.061], [1.123, 0], [0.068, -2.139], [2.188, 0], [-0.186, 6.865], [-2.324, -0.108], [0.146, -3.506], [-1.084, 0], [-0.01, 3.672], [-2.227, -0.058], [-0.156, -6.67], [0, 0]], "o": [[-2.442, 0.117], [-0.019, -2.061], [-1.123, 0], [-0.068, 2.139], [-2.188, 0], [0.186, -6.865], [2.324, 0.108], [-0.146, 3.506], [1.084, 0], [0.01, -3.672], [2.227, 0.058], [0.156, 6.67], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-199.239, -41.09]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 105, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 111, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 113, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 115, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "5", "layers": [{"ind": 8, "ty": 4, "nm": "K: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [10, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "K", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[189.038, 32.549], [182.563, 40.254], [188.51, 50.332], [181.713, 51.534], [177.26, 45.498], [177.026, 51.241], [170.932, 51.475], [171.401, 30.498], [178.227, 30.879], [177.7, 36.914], [181.948, 30.791], [189.038, 32.549], [189.038, 32.549]], "i": [[0, 0], [2.158, -2.568], [-1.982, -3.359], [2.266, -0.401], [1.484, 2.012], [0.078, -1.914], [2.031, -0.078], [-0.156, 6.992], [-2.275, -0.127], [0.176, -2.012], [-1.416, 2.041], [-2.363, -0.586], [0, 0]], "o": [[-2.158, 2.568], [1.982, 3.359], [-2.266, 0.401], [-1.484, -2.012], [-0.078, 1.914], [-2.031, 0.078], [0.156, -6.992], [2.275, 0.127], [-0.176, 2.012], [1.416, -2.041], [2.363, 0.586], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-179.985, -41.016]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 100, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 106, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 108, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 110, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "6", "layers": [{"ind": 9, "ty": 4, "nm": "E: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "E", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[162.709, 38.877], [162.621, 38.042], [162.328, 37.251], [161.815, 36.68], [161.009, 36.446], [159.969, 36.768], [159.208, 37.559], [158.68, 38.584], [158.373, 39.61], [162.709, 39.2], [162.709, 38.877], [162.709, 38.877]], "i": [[0, 0], [0.058, 0.284], [0.136, 0.235], [0.215, 0.147], [0.323, 0], [0.303, -0.215], [0.214, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.39, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.136, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-160.145, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[168.568, 39.639], [168.392, 42.188], [163.412, 42.715], [158.461, 43.418], [159.647, 45.103], [161.595, 45.733], [162.87, 45.513], [164.261, 44.971], [165.58, 44.253], [166.664, 43.565], [165.961, 50.215], [164.715, 50.962], [163.338, 51.46], [161.888, 51.739], [160.453, 51.827], [157.801, 51.431], [155.619, 50.332], [153.92, 48.648], [152.704, 46.538], [151.971, 44.121], [151.722, 41.543], [151.971, 38.819], [152.718, 36.197], [153.993, 33.868], [155.795, 31.978], [158.109, 30.718], [160.98, 30.264], [163.353, 30.63], [165.272, 31.626], [166.737, 33.135], [167.762, 35.069], [168.363, 37.281], [168.568, 39.639], [168.568, 39.639]], "i": [[0, 0], [0.117, -0.85], [1.65, -0.244], [1.65, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.234, -2.217], [0.45, -0.205], [0.479, -0.127], [0.489, -0.059], [0.468, 0], [0.801, 0.264], [0.654, 0.469], [0.488, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.166, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.684, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.557, -0.429], [-0.41, -0.586], [-0.263, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.651, 0.235], [0.253, 0.703], [0.547, 0.42], [0.391, 0], [0.468, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.234, 2.217], [-0.381, 0.293], [-0.439, 0.205], [-0.478, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.8, -0.264], [-0.645, -0.478], [-0.489, -0.645], [-0.323, -0.761], [-0.166, -0.849], [0, -0.908], [0.166, -0.908], [0.342, -0.84], [0.517, -0.723], [0.683, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.566, 0.42], [0.42, 0.586], [0.274, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-160.145, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 95, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 101, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 103, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 105, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "7", "layers": [{"ind": 10, "ty": 4, "nm": "D: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "D", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[139.837, 45.469], [141.433, 44.942], [142.693, 43.946], [143.499, 42.598], [143.792, 40.987], [143.616, 39.434], [143.045, 38.174], [142.004, 37.325], [140.422, 36.973], [139.837, 45.469], [139.837, 45.469]], "i": [[0, 0], [-0.478, 0.263], [-0.352, 0.39], [-0.186, 0.498], [0, 0.566], [0.117, 0.479], [0.263, 0.352], [0.43, 0.205], [0.625, 0.02], [0.195, -2.832], [0, 0]], "o": [[0.585, -0.088], [0.488, -0.274], [0.352, -0.401], [0.195, -0.508], [0, -0.557], [-0.117, -0.488], [-0.264, -0.361], [-0.429, -0.215], [-0.195, 2.832], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-141.448, -40.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[149.797, 39.844], [149.27, 43.521], [147.791, 46.407], [145.549, 48.531], [142.693, 49.981], [139.382, 50.801], [135.794, 51.065], [134.446, 51.036], [133.098, 50.918], [133.508, 31.846], [136.819, 31.128], [140.217, 30.909], [144.07, 31.495], [147.102, 33.223], [149.08, 36.021], [149.797, 39.844], [149.797, 39.844]], "i": [[0, 0], [0.352, -1.084], [0.634, -0.84], [0.87, -0.586], [1.035, -0.381], [1.172, -0.176], [1.23, 0], [0.439, 0.019], [0.459, 0.049], [-0.137, 6.357], [-1.133, 0.137], [-1.123, 0], [-1.172, -0.391], [-0.849, -0.762], [-0.469, -1.113], [0, -1.445], [0, 0]], "o": [[0, 1.367], [-0.351, 1.084], [-0.625, 0.83], [-0.869, 0.586], [-1.035, 0.371], [-1.162, 0.176], [-0.459, 0], [-0.44, -0.03], [0.137, -6.357], [1.075, -0.342], [1.142, -0.146], [1.397, 0], [1.172, 0.39], [0.85, 0.752], [0.478, 1.103], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-141.448, -40.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 90, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 96, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 98, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 100, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "8", "layers": [{"ind": 11, "ty": 4, "nm": "R: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "R", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[116.753, 37.823], [116.578, 36.695], [116.08, 35.816], [115.259, 35.23], [114.117, 35.01], [113.545, 35.054], [113.003, 35.157], [112.769, 40.606], [113.121, 40.606], [114.395, 40.445], [115.567, 39.947], [116.417, 39.082], [116.753, 37.823], [116.753, 37.823]], "i": [[0, 0], [0.117, 0.342], [0.224, 0.244], [0.332, 0.137], [0.439, 0], [0.186, -0.029], [0.176, -0.049], [0.078, -1.816], [-0.117, 0], [-0.43, 0.107], [-0.342, 0.224], [-0.215, 0.352], [0, 0.488], [0, 0]], "o": [[0, -0.41], [-0.108, -0.342], [-0.215, -0.254], [-0.322, -0.147], [-0.196, 0], [-0.185, 0.02], [-0.078, 1.816], [0.117, 0], [0.42, 0], [0.44, -0.108], [0.352, -0.225], [0.224, -0.351], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-114.674, -40.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[123.316, 37.354], [123.111, 39.434], [122.496, 41.133], [121.397, 42.569], [119.771, 43.829], [123.257, 50.039], [116.666, 51.329], [114.41, 45.088], [112.593, 45.147], [112.33, 51.241], [106.031, 51.241], [106.163, 44.59], [106.324, 37.94], [106.382, 34.6], [106.5, 31.26], [108.521, 30.63], [110.513, 30.249], [112.549, 30.059], [114.673, 30], [117.896, 30.469], [120.665, 31.846], [122.584, 34.146], [123.316, 37.354], [123.316, 37.354]], "i": [[0, 0], [0.137, -0.625], [0.283, -0.517], [0.449, -0.44], [0.635, -0.401], [-1.162, -2.07], [2.197, -0.43], [0.752, 2.08], [0.606, -0.02], [0.088, -2.031], [2.1, 0], [-0.039, 2.207], [-0.059, 2.226], [-0.019, 1.104], [-0.059, 1.123], [-0.664, 0.166], [-0.664, 0.088], [-0.683, 0.029], [-0.722, 0], [-1.035, -0.312], [-0.801, -0.615], [-0.479, -0.918], [0, -1.221], [0, 0]], "o": [[0, 0.762], [-0.127, 0.615], [-0.284, 0.518], [-0.449, 0.439], [1.162, 2.07], [-2.197, 0.43], [-0.752, -2.08], [-0.606, 0.02], [-0.088, 2.031], [-2.1, 0], [0.049, -2.227], [0.048, -2.207], [0.019, -1.123], [0.02, -1.104], [0.683, -0.254], [0.664, -0.166], [0.674, -0.097], [0.694, -0.039], [1.114, 0], [1.045, 0.303], [0.8, 0.615], [0.488, 0.918], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-114.674, -40.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 85, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 91, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 93, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 95, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "9", "layers": [{"ind": 12, "ty": 4, "nm": "A: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [10, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "A", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[95.669, 43.36], [94.555, 38.262], [93.471, 43.36], [95.669, 43.36], [95.669, 43.36]], "i": [[0, 0], [0.371, 1.699], [0.361, -1.699], [-0.733, 0], [0, 0]], "o": [[-0.371, -1.699], [-0.361, 1.699], [0.733, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-94.497, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[104.34, 50.215], [97.075, 51.153], [96.196, 47.842], [92.856, 47.842], [92.124, 51.153], [84.653, 50.42], [90.483, 30.909], [98.628, 30.498], [104.34, 50.215], [104.34, 50.215]], "i": [[0, 0], [2.422, -0.313], [0.293, 1.104], [1.113, 0], [0.244, -1.104], [2.49, 0.244], [-1.943, 6.504], [-2.715, 0.137], [-1.904, -6.572], [0, 0]], "o": [[-2.422, 0.313], [-0.293, -1.104], [-1.113, 0], [-0.244, 1.104], [-2.49, -0.244], [1.943, -6.504], [2.715, -0.137], [1.904, 6.572], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-94.497, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 80, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 86, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 88, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 90, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "10", "layers": [{"ind": 13, "ty": 4, "nm": "A: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [10, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "A", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[75.389, 43.36], [74.276, 38.262], [73.192, 43.36], [75.389, 43.36], [75.389, 43.36]], "i": [[0, 0], [0.371, 1.699], [0.361, -1.699], [-0.732, 0], [0, 0]], "o": [[-0.371, -1.699], [-0.361, 1.699], [0.732, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-74.218, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[84.061, 50.215], [76.795, 51.153], [75.917, 47.842], [72.577, 47.842], [71.844, 51.153], [64.374, 50.42], [70.204, 30.909], [78.348, 30.498], [84.061, 50.215], [84.061, 50.215]], "i": [[0, 0], [2.422, -0.313], [0.293, 1.104], [1.113, 0], [0.244, -1.104], [2.49, 0.244], [-1.943, 6.504], [-2.715, 0.137], [-1.904, -6.572], [0, 0]], "o": [[-2.422, 0.313], [-0.293, -1.104], [-1.113, 0], [-0.244, 1.104], [-2.49, -0.244], [1.943, -6.504], [2.715, -0.137], [1.904, 6.572], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-74.218, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 75, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 81, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 83, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 85, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "11", "layers": [{"ind": 14, "ty": 4, "nm": "B: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "B", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[53.777, 42.686], [52.957, 42.774], [52.898, 46.114], [53.323, 46.202], [53.748, 46.231], [54.421, 46.172], [55.11, 45.909], [55.637, 45.367], [55.857, 44.473], [55.637, 43.565], [55.125, 43.023], [54.451, 42.759], [53.777, 42.686], [53.777, 42.686]], "i": [[0, 0], [0.263, -0.059], [0.02, -1.113], [-0.137, -0.03], [-0.137, 0], [-0.244, 0.039], [-0.215, 0.127], [-0.136, 0.224], [0, 0.371], [0.147, 0.234], [0.205, 0.127], [0.244, 0.039], [0.205, 0], [0, 0]], "o": [[-0.283, 0], [-0.02, 1.113], [0.147, 0.029], [0.146, 0.019], [0.205, 0], [0.245, -0.049], [0.215, -0.137], [0.147, -0.225], [0, -0.371], [-0.136, -0.235], [-0.205, -0.137], [-0.244, -0.049], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-54.788, -40.973]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[55.857, 37.911], [55.681, 37.193], [55.242, 36.695], [54.685, 36.416], [54.158, 36.329], [53.587, 36.416], [53.045, 36.621], [52.957, 39.873], [53.792, 39.815], [54.744, 39.566], [55.535, 38.98], [55.857, 37.911], [55.857, 37.911]], "i": [[0, 0], [0.117, 0.195], [0.176, 0.127], [0.205, 0.059], [0.156, 0], [0.185, -0.058], [0.175, -0.078], [0.029, -1.084], [-0.332, 0.039], [-0.303, 0.127], [-0.215, 0.254], [0, 0.449], [0, 0]], "o": [[0, -0.284], [-0.117, -0.205], [-0.166, -0.127], [-0.195, -0.058], [-0.196, 0], [-0.186, 0.059], [-0.029, 1.084], [0.224, 0], [0.332, -0.039], [0.312, -0.137], [0.215, -0.264], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-54.788, -40.973]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[63.298, 46.26], [62.844, 48.355], [61.658, 49.849], [59.929, 50.86], [57.908, 51.46], [55.784, 51.753], [53.806, 51.827], [51.917, 51.753], [49.983, 51.534], [48.079, 51.109], [46.306, 50.42], [46.277, 31.553], [48.02, 30.953], [49.895, 30.498], [51.814, 30.22], [53.66, 30.118], [55.754, 30.249], [57.805, 30.689], [59.636, 31.495], [61.116, 32.696], [62.112, 34.366], [62.478, 36.563], [62.244, 38.101], [61.57, 39.39], [60.501, 40.371], [59.08, 40.957], [60.808, 41.69], [62.141, 42.862], [62.991, 44.414], [63.298, 46.26], [63.298, 46.26]], "i": [[0, 0], [0.303, -0.586], [0.498, -0.41], [0.655, -0.264], [0.703, -0.146], [0.713, -0.049], [0.615, 0], [0.654, 0.049], [0.645, 0.107], [0.625, 0.176], [0.557, 0.274], [0.01, 6.289], [-0.615, 0.175], [-0.635, 0.118], [-0.635, 0.059], [-0.596, 0], [-0.703, -0.087], [-0.654, -0.205], [-0.566, -0.332], [-0.42, -0.479], [-0.234, -0.635], [0, -0.83], [0.156, -0.479], [0.293, -0.381], [0.419, -0.273], [0.527, -0.127], [-0.517, -0.322], [-0.361, -0.459], [-0.196, -0.576], [0, -0.654], [0, 0]], "o": [[0, 0.811], [-0.293, 0.586], [-0.498, 0.41], [-0.644, 0.254], [-0.703, 0.147], [-0.703, 0.049], [-0.605, 0], [-0.645, -0.039], [-0.645, -0.108], [-0.625, -0.186], [-0.01, -6.289], [0.547, -0.224], [0.615, -0.186], [0.645, -0.127], [0.635, -0.068], [0.693, 0], [0.713, 0.088], [0.655, 0.205], [0.567, 0.322], [0.429, 0.478], [0.244, 0.634], [0, 0.547], [-0.157, 0.478], [-0.293, 0.381], [-0.42, 0.264], [0.634, 0.166], [0.528, 0.322], [0.371, 0.459], [0.205, 0.577], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-54.788, -40.973]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 70, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 76, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 78, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 80, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "12", "layers": [{"ind": 15, "ty": 4, "nm": "K: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [10, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "K", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[37.315, 32.549], [30.841, 40.254], [36.788, 50.332], [29.991, 51.534], [25.538, 45.498], [25.304, 51.241], [19.21, 51.475], [19.679, 30.498], [26.505, 30.879], [25.978, 36.914], [30.226, 30.791], [37.315, 32.549], [37.315, 32.549]], "i": [[0, 0], [2.158, -2.568], [-1.983, -3.359], [2.266, -0.401], [1.484, 2.012], [0.078, -1.914], [2.031, -0.078], [-0.156, 6.992], [-2.275, -0.127], [0.176, -2.012], [-1.416, 2.041], [-2.363, -0.586], [0, 0]], "o": [[-2.158, 2.568], [1.983, 3.359], [-2.266, 0.401], [-1.484, -2.012], [-0.078, 1.914], [-2.031, 0.078], [0.156, -6.992], [2.275, 0.127], [-0.176, 2.012], [1.416, -2.041], [2.363, 0.586], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-28.263, -41.016]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 65, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 71, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 73, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 75, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "13", "layers": [{"ind": 16, "ty": 4, "nm": "E: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "E", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[10.986, 38.877], [10.898, 38.042], [10.606, 37.251], [10.093, 36.68], [9.287, 36.446], [8.247, 36.768], [7.485, 37.559], [6.958, 38.584], [6.65, 39.61], [10.986, 39.2], [10.986, 38.877], [10.986, 38.877]], "i": [[0, 0], [0.059, 0.284], [0.137, 0.235], [0.215, 0.147], [0.322, 0], [0.303, -0.215], [0.215, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.391, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.137, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-8.423, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[16.846, 39.639], [16.67, 42.188], [11.689, 42.715], [6.738, 43.418], [7.925, 45.103], [9.873, 45.733], [11.147, 45.513], [12.539, 44.971], [13.857, 44.253], [14.941, 43.565], [14.238, 50.215], [12.993, 50.962], [11.616, 51.46], [10.166, 51.739], [8.731, 51.827], [6.079, 51.431], [3.897, 50.332], [2.197, 48.648], [0.981, 46.538], [0.249, 44.121], [0, 41.543], [0.249, 38.819], [0.996, 36.197], [2.27, 33.868], [4.072, 31.978], [6.387, 30.718], [9.258, 30.264], [11.631, 30.63], [13.55, 31.626], [15.015, 33.135], [16.04, 35.069], [16.641, 37.281], [16.846, 39.639], [16.846, 39.639]], "i": [[0, 0], [0.117, -0.85], [1.65, -0.244], [1.65, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.234, -2.217], [0.449, -0.205], [0.478, -0.127], [0.488, -0.059], [0.469, 0], [0.801, 0.264], [0.654, 0.469], [0.488, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.166, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.684, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.557, -0.429], [-0.41, -0.586], [-0.264, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.65, 0.235], [0.254, 0.703], [0.547, 0.42], [0.391, 0], [0.469, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.234, 2.217], [-0.381, 0.293], [-0.439, 0.205], [-0.479, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.801, -0.264], [-0.645, -0.478], [-0.488, -0.645], [-0.322, -0.761], [-0.166, -0.849], [0, -0.908], [0.166, -0.908], [0.342, -0.84], [0.518, -0.723], [0.683, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.566, 0.42], [0.42, 0.586], [0.273, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-8.423, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 60, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 66, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 68, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 70, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "14", "layers": [{"ind": 17, "ty": 4, "nm": "W: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [14, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "W", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[245.692, 1.143], [242.264, 20.86], [233.944, 21.036], [232.157, 11.104], [230.37, 20.274], [222.049, 20.567], [218.592, 1.729], [225.858, 1.084], [227.323, 12.657], [229.286, 1.963], [234.999, 1.963], [237.284, 12.803], [238.456, 0.498], [245.692, 1.143], [245.692, 1.143]], "i": [[0, 0], [1.143, -6.572], [2.773, -0.059], [0.596, 3.311], [0.596, -3.057], [2.774, -0.098], [1.152, 6.279], [-2.422, 0.215], [-0.488, -3.858], [-0.654, 3.565], [-1.904, 0], [-0.762, -3.613], [-0.391, 4.102], [-2.412, -0.215], [0, 0]], "o": [[-1.143, 6.572], [-2.773, 0.059], [-0.596, -3.311], [-0.596, 3.057], [-2.774, 0.098], [-1.152, -6.279], [2.422, -0.215], [0.488, 3.858], [0.654, -3.565], [1.904, 0], [0.762, 3.613], [0.391, -4.102], [2.412, 0.215], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-232.142, -10.767]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 55, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 61, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 63, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 65, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "15", "layers": [{"ind": 18, "ty": 4, "nm": "O: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [10, 10]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "O", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[210.618, 10.635], [210.427, 9.419], [209.9, 8.365], [209.021, 7.618], [207.835, 7.325], [206.604, 7.574], [205.667, 8.262], [205.081, 9.273], [204.876, 10.489], [205.051, 11.719], [205.579, 12.832], [206.443, 13.638], [207.659, 13.946], [208.889, 13.682], [209.827, 12.95], [210.413, 11.88], [210.618, 10.635], [210.618, 10.635]], "i": [[0, 0], [0.127, 0.391], [0.234, 0.302], [0.352, 0.185], [0.449, 0], [0.361, -0.166], [0.263, -0.293], [0.136, -0.391], [0, -0.43], [-0.117, -0.41], [-0.235, -0.332], [-0.342, -0.205], [-0.459, 0], [-0.361, 0.176], [-0.254, 0.302], [-0.137, 0.401], [0, 0.43], [0, 0]], "o": [[0, -0.42], [-0.117, -0.4], [-0.234, -0.313], [-0.342, -0.196], [-0.459, 0], [-0.361, 0.166], [-0.254, 0.283], [-0.137, 0.381], [0, 0.41], [0.118, 0.41], [0.234, 0.332], [0.352, 0.205], [0.459, 0], [0.371, -0.186], [0.254, -0.313], [0.136, -0.4], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-207.82, -10.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[217.122, 10.254], [216.814, 12.832], [215.92, 15.147], [214.514, 17.139], [212.654, 18.677], [210.413, 19.688], [207.835, 20.039], [205.315, 19.703], [203.088, 18.765], [201.199, 17.3], [199.763, 15.396], [198.84, 13.14], [198.518, 10.635], [198.826, 8.145], [199.705, 5.83], [201.111, 3.838], [202.942, 2.271], [205.154, 1.246], [207.659, 0.879], [211.526, 1.538], [214.514, 3.428], [216.433, 6.387], [217.122, 10.254], [217.122, 10.254]], "i": [[0, 0], [0.205, -0.83], [0.391, -0.723], [0.557, -0.605], [0.693, -0.43], [0.81, -0.244], [0.908, 0], [0.801, 0.224], [0.694, 0.4], [0.566, 0.567], [0.401, 0.693], [0.225, 0.801], [0, 0.869], [-0.205, 0.81], [-0.381, 0.723], [-0.547, 0.606], [-0.674, 0.439], [-0.791, 0.244], [-0.879, 0], [-1.162, -0.439], [-0.82, -0.82], [-0.449, -1.162], [0, -1.425], [0, 0]], "o": [[0, 0.889], [-0.205, 0.821], [-0.38, 0.723], [-0.547, 0.596], [-0.684, 0.43], [-0.811, 0.234], [-0.879, 0], [-0.791, -0.225], [-0.693, -0.41], [-0.557, -0.576], [-0.39, -0.703], [-0.214, -0.801], [0, -0.849], [0.205, -0.82], [0.39, -0.722], [0.547, -0.605], [0.684, -0.44], [0.791, -0.245], [1.416, 0], [1.172, 0.44], [0.83, 0.811], [0.459, 1.152], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-207.82, -10.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 50, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 56, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 58, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 60, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "16", "layers": [{"ind": 19, "ty": 4, "nm": "L: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [7, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "L", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[196.828, 13.711], [196.212, 20.215], [183.878, 21.182], [184.347, 0.264], [191.964, 0.264], [190.88, 13.711], [196.828, 13.711], [196.828, 13.711]], "i": [[0, 0], [0.205, -2.168], [4.111, -0.322], [-0.156, 6.973], [-2.539, 0], [0.361, -4.482], [-1.983, 0], [0, 0]], "o": [[-0.205, 2.168], [-4.111, 0.322], [0.156, -6.973], [2.539, 0], [-0.361, 4.482], [1.983, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-190.353, -10.723]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 45, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 51, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 53, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 55, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "17", "layers": [{"ind": 20, "ty": 4, "nm": "e: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "e", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[168.295, 8.877], [168.208, 8.042], [167.915, 7.251], [167.402, 6.68], [166.596, 6.446], [165.556, 6.768], [164.795, 7.559], [164.267, 8.584], [163.96, 9.61], [168.295, 9.2], [168.295, 8.877], [168.295, 8.877]], "i": [[0, 0], [0.058, 0.284], [0.136, 0.235], [0.215, 0.147], [0.323, 0], [0.303, -0.215], [0.214, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.39, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.137, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-165.732, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[174.155, 9.639], [173.979, 12.188], [168.999, 12.715], [164.047, 13.418], [165.234, 15.103], [167.182, 15.733], [168.457, 15.513], [169.848, 14.971], [171.167, 14.253], [172.251, 13.565], [171.547, 20.215], [170.302, 20.962], [168.925, 21.46], [167.475, 21.739], [166.04, 21.827], [163.388, 21.431], [161.206, 20.332], [159.506, 18.648], [158.291, 16.538], [157.558, 14.121], [157.309, 11.543], [157.558, 8.819], [158.305, 6.197], [159.58, 3.868], [161.381, 1.978], [163.696, 0.718], [166.567, 0.264], [168.94, 0.63], [170.859, 1.626], [172.324, 3.135], [173.349, 5.069], [173.95, 7.281], [174.155, 9.639], [174.155, 9.639]], "i": [[0, 0], [0.117, -0.85], [1.65, -0.244], [1.651, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.235, -2.217], [0.45, -0.205], [0.479, -0.127], [0.488, -0.059], [0.468, 0], [0.801, 0.264], [0.654, 0.469], [0.489, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.166, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.683, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.557, -0.429], [-0.41, -0.586], [-0.263, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.651, 0.235], [0.254, 0.703], [0.547, 0.42], [0.391, 0], [0.468, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.235, 2.217], [-0.38, 0.293], [-0.439, 0.205], [-0.478, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.801, -0.264], [-0.645, -0.478], [-0.488, -0.645], [-0.323, -0.761], [-0.166, -0.849], [0, -0.908], [0.166, -0.908], [0.342, -0.84], [0.517, -0.723], [0.684, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.566, 0.42], [0.42, 0.586], [0.274, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-165.732, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 40, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 46, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 48, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 50, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "18", "layers": [{"ind": 21, "ty": 4, "nm": "N: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [10, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "N", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[154.916, 14.063], [154.872, 17.344], [154.769, 20.596], [146.332, 20.45], [147.093, 15.279], [147.416, 10.049], [147.401, 9.434], [147.342, 8.482], [147.211, 7.383], [146.991, 6.343], [146.654, 5.581], [146.185, 5.274], [145.336, 5.567], [144.764, 6.329], [144.413, 7.354], [144.208, 8.496], [144.12, 9.58], [144.105, 10.401], [143.988, 15.557], [143.871, 20.713], [136.4, 21.036], [136.957, 10.752], [137.396, 0.469], [144.31, 0.118], [144.252, 2.461], [145.13, 1.524], [146.156, 0.777], [147.298, 0.279], [148.587, 0.088], [150.77, 0.498], [152.381, 1.612], [153.509, 3.282], [154.242, 5.318], [154.667, 7.588], [154.857, 9.903], [154.916, 12.115], [154.916, 14.063], [154.916, 14.063]], "i": [[0, 0], [0.029, -1.084], [0.049, -1.094], [2.812, 0.049], [-0.215, 1.718], [0, 1.758], [0.01, 0.283], [0.03, 0.351], [0.058, 0.371], [0.097, 0.313], [0.137, 0.196], [0.186, 0], [0.234, -0.196], [0.156, -0.313], [0.088, -0.381], [0.048, -0.38], [0.019, -0.341], [0, -0.215], [0.029, -1.709], [0.049, -1.738], [2.49, -0.108], [-0.176, 3.409], [-0.107, 3.447], [-2.305, 0.117], [0.019, -0.781], [-0.312, 0.293], [-0.361, 0.205], [-0.4, 0.117], [-0.449, 0], [-0.625, -0.273], [-0.449, -0.469], [-0.302, -0.645], [-0.186, -0.723], [-0.098, -0.791], [-0.029, -0.762], [0, -0.713], [0, -0.586], [0, 0]], "o": [[0, 1.103], [-0.02, 1.074], [-2.812, -0.049], [0.293, -1.729], [0.215, -1.729], [0, -0.127], [-0.01, -0.283], [-0.029, -0.361], [-0.049, -0.381], [-0.088, -0.312], [-0.127, -0.205], [-0.332, 0], [-0.225, 0.195], [-0.146, 0.302], [-0.088, 0.381], [-0.039, 0.381], [-0.01, 0.333], [-0.049, 1.728], [-0.029, 1.699], [-2.49, 0.108], [0.195, -3.448], [0.185, -3.408], [2.305, -0.117], [-0.019, 0.781], [0.273, -0.332], [0.323, -0.293], [0.361, -0.215], [0.411, -0.127], [0.831, 0], [0.625, 0.274], [0.45, 0.468], [0.303, 0.634], [0.185, 0.722], [0.097, 0.782], [0.039, 0.761], [0, 0.713], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-145.658, -10.562]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 35, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 41, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 43, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 45, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "19", "layers": [{"ind": 22, "ty": 4, "nm": "T: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "T", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[134.578, 0.821], [134.431, 7.032], [130.066, 7.207], [129.539, 20.801], [123.357, 21.123], [122.8, 7.53], [118.435, 7.764], [118.611, 0.85], [134.578, 0.821], [134.578, 0.821]], "i": [[0, 0], [0.049, -2.07], [1.455, -0.058], [0.176, -4.531], [2.061, -0.107], [0.186, 4.531], [1.455, -0.078], [-0.059, 2.305], [-5.322, 0.01], [0, 0]], "o": [[-0.049, 2.07], [-1.455, 0.058], [-0.176, 4.531], [-2.061, 0.107], [-0.186, -4.531], [-1.455, 0.078], [0.059, -2.305], [5.322, -0.01], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-126.507, -10.972]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 30, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 36, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 38, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 40, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "20", "layers": [{"ind": 23, "ty": 4, "nm": "I: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [4, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "I", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[116.364, 0.85], [115.338, 20.801], [109.157, 21.123], [108.571, 1.26], [116.364, 0.85], [116.364, 0.85]], "i": [[0, 0], [0.342, -6.65], [2.06, -0.107], [0.195, 6.621], [-2.598, 0.137], [0, 0]], "o": [[-0.342, 6.65], [-2.06, 0.107], [-0.195, -6.621], [2.598, -0.137], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-112.468, -10.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 25, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 31, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 33, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 35, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "21", "layers": [{"ind": 24, "ty": 4, "nm": "e: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "e", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[93.032, 8.877], [92.944, 8.042], [92.651, 7.251], [92.138, 6.68], [91.333, 6.446], [90.293, 6.768], [89.531, 7.559], [89.003, 8.584], [88.696, 9.61], [93.032, 9.2], [93.032, 8.877], [93.032, 8.877]], "i": [[0, 0], [0.059, 0.284], [0.137, 0.235], [0.215, 0.147], [0.322, 0], [0.302, -0.215], [0.215, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.391, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.136, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-90.468, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[98.891, 9.639], [98.715, 12.188], [93.735, 12.715], [88.784, 13.418], [89.97, 15.103], [91.919, 15.733], [93.193, 15.513], [94.585, 14.971], [95.903, 14.253], [96.987, 13.565], [96.284, 20.215], [95.039, 20.962], [93.662, 21.46], [92.211, 21.739], [90.776, 21.827], [88.125, 21.431], [85.942, 20.332], [84.243, 18.648], [83.027, 16.538], [82.295, 14.121], [82.045, 11.543], [82.295, 8.819], [83.042, 6.197], [84.316, 3.868], [86.118, 1.978], [88.432, 0.718], [91.303, 0.264], [93.676, 0.63], [95.595, 1.626], [97.06, 3.135], [98.086, 5.069], [98.686, 7.281], [98.891, 9.639], [98.891, 9.639]], "i": [[0, 0], [0.118, -0.85], [1.65, -0.244], [1.65, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.234, -2.217], [0.449, -0.205], [0.478, -0.127], [0.489, -0.059], [0.469, 0], [0.8, 0.264], [0.654, 0.469], [0.488, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.167, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.684, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.556, -0.429], [-0.41, -0.586], [-0.264, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.65, 0.235], [0.254, 0.703], [0.547, 0.42], [0.39, 0], [0.469, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.234, 2.217], [-0.381, 0.293], [-0.44, 0.205], [-0.479, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.801, -0.264], [-0.645, -0.478], [-0.489, -0.645], [-0.322, -0.761], [-0.167, -0.849], [0, -0.908], [0.166, -0.908], [0.341, -0.84], [0.518, -0.723], [0.683, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.567, 0.42], [0.42, 0.586], [0.273, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-90.468, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 20, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 26, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 28, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 30, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "22", "layers": [{"ind": 25, "ty": 4, "nm": "C: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [8, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "C", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[79.637, 1.465], [79.11, 7.647], [78.275, 7.486], [77.44, 7.442], [75.448, 7.705], [73.778, 8.511], [72.621, 9.888], [72.196, 11.836], [72.43, 13.272], [73.119, 14.283], [74.203, 14.883], [75.624, 15.088], [76.737, 14.986], [77.879, 14.678], [78.978, 14.239], [79.96, 13.711], [79.725, 20.596], [78.495, 21.123], [77.132, 21.504], [75.741, 21.768], [74.422, 21.856], [70.628, 21.079], [67.699, 18.97], [65.809, 15.85], [65.135, 12.071], [65.809, 7.588], [67.816, 3.985], [71.097, 1.582], [75.594, 0.704], [77.645, 0.865], [79.637, 1.465], [79.637, 1.465]], "i": [[0, 0], [0.176, -2.061], [0.273, 0.029], [0.283, 0], [0.635, -0.175], [0.488, -0.361], [0.293, -0.557], [0, -0.752], [-0.156, -0.41], [-0.293, -0.274], [-0.42, -0.137], [-0.518, 0], [-0.381, 0.068], [-0.38, 0.127], [-0.351, 0.166], [-0.303, 0.186], [0.078, -2.295], [0.439, -0.156], [0.469, -0.107], [0.469, -0.059], [0.42, 0], [1.143, 0.518], [0.82, 0.889], [0.449, 1.182], [0, 1.338], [-0.449, 1.387], [-0.879, 1.015], [-1.299, 0.577], [-1.689, 0], [-0.693, -0.108], [-0.634, -0.293], [0, 0]], "o": [[-0.176, 2.061], [-0.283, -0.078], [-0.273, -0.03], [-0.693, 0], [-0.625, 0.176], [-0.479, 0.361], [-0.284, 0.547], [0, 0.547], [0.166, 0.4], [0.302, 0.263], [0.429, 0.137], [0.361, 0], [0.381, -0.078], [0.381, -0.127], [0.352, -0.166], [-0.078, 2.295], [-0.381, 0.195], [-0.44, 0.147], [-0.459, 0.117], [-0.459, 0.059], [-1.386, 0], [-1.132, -0.517], [-0.811, -0.898], [-0.449, -1.182], [0, -1.602], [0.459, -1.386], [0.889, -1.026], [1.309, -0.586], [0.674, 0], [0.693, 0.107], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-72.548, -11.28]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 15, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 21, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 23, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 25, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "23", "layers": [{"ind": 26, "ty": 4, "nm": "I: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [4, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "I", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[62.961, 0.85], [61.936, 20.801], [55.754, 21.123], [55.169, 1.26], [62.961, 0.85], [62.961, 0.85]], "i": [[0, 0], [0.342, -6.65], [2.061, -0.107], [0.195, 6.621], [-2.597, 0.137], [0, 0]], "o": [[-0.342, 6.65], [-2.061, 0.107], [-0.195, -6.621], [2.597, -0.137], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-59.065, -10.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 10, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 16, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 18, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 20, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "24", "layers": [{"ind": 27, "ty": 4, "nm": "R: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "R", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[46.227, 7.823], [46.051, 6.695], [45.553, 5.816], [44.733, 5.23], [43.59, 5.01], [43.019, 5.054], [42.477, 5.157], [42.243, 10.606], [42.594, 10.606], [43.869, 10.445], [45.041, 9.947], [45.89, 9.082], [46.227, 7.823], [46.227, 7.823]], "i": [[0, 0], [0.118, 0.342], [0.225, 0.244], [0.332, 0.137], [0.44, 0], [0.186, -0.029], [0.176, -0.049], [0.078, -1.816], [-0.117, 0], [-0.43, 0.107], [-0.342, 0.224], [-0.215, 0.352], [0, 0.488], [0, 0]], "o": [[0, -0.41], [-0.107, -0.342], [-0.215, -0.254], [-0.322, -0.147], [-0.195, 0], [-0.185, 0.02], [-0.078, 1.816], [0.117, 0], [0.42, 0], [0.439, -0.108], [0.351, -0.225], [0.225, -0.351], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-44.147, -10.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[52.79, 7.354], [52.585, 9.434], [51.969, 11.133], [50.871, 12.569], [49.245, 13.829], [52.731, 20.039], [46.139, 21.329], [43.883, 15.088], [42.067, 15.147], [41.803, 21.241], [35.504, 21.241], [35.636, 14.59], [35.797, 7.94], [35.856, 4.6], [35.973, 1.26], [37.995, 0.63], [39.987, 0.249], [42.023, 0.059], [44.147, 0], [47.37, 0.469], [50.138, 1.846], [52.057, 4.146], [52.79, 7.354], [52.79, 7.354]], "i": [[0, 0], [0.136, -0.625], [0.284, -0.517], [0.449, -0.44], [0.634, -0.401], [-1.162, -2.07], [2.197, -0.43], [0.752, 2.08], [0.605, -0.02], [0.088, -2.031], [2.1, 0], [-0.039, 2.207], [-0.058, 2.226], [-0.019, 1.104], [-0.058, 1.123], [-0.664, 0.166], [-0.664, 0.088], [-0.684, 0.029], [-0.723, 0], [-1.035, -0.312], [-0.801, -0.615], [-0.478, -0.918], [0, -1.221], [0, 0]], "o": [[0, 0.762], [-0.127, 0.615], [-0.283, 0.518], [-0.45, 0.439], [1.162, 2.07], [-2.197, 0.43], [-0.752, -2.08], [-0.605, 0.02], [-0.088, 2.031], [-2.1, 0], [0.049, -2.227], [0.049, -2.207], [0.02, -1.123], [0.02, -1.104], [0.684, -0.254], [0.664, -0.166], [0.674, -0.097], [0.693, -0.039], [1.113, 0], [1.045, 0.303], [0.801, 0.615], [0.488, 0.918], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-44.147, -10.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 5, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 11, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 13, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 15, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "25", "layers": [{"ind": 28, "ty": 4, "nm": "P: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "P", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[26.813, 7.881], [26.227, 6.402], [24.732, 5.86], [24.088, 5.918], [23.473, 6.036], [23.297, 10.313], [23.692, 10.342], [24.088, 10.342], [25.099, 10.166], [25.978, 9.668], [26.578, 8.892], [26.813, 7.881], [26.813, 7.881]], "i": [[0, 0], [0.391, 0.351], [0.615, 0], [0.225, -0.039], [0.195, -0.049], [0.059, -1.426], [-0.127, 0], [-0.137, 0], [-0.322, 0.118], [-0.254, 0.215], [-0.147, 0.293], [0, 0.371], [0, 0]], "o": [[0, -0.635], [-0.381, -0.362], [-0.205, 0], [-0.215, 0.03], [-0.059, 1.426], [0.137, 0.019], [0.127, 0], [0.352, 0], [0.332, -0.117], [0.254, -0.224], [0.156, -0.303], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-24.806, -10.84]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[33.316, 7.383], [32.892, 9.903], [31.72, 11.822], [29.991, 13.213], [27.853, 14.151], [25.509, 14.678], [23.121, 14.854], [23.121, 21.68], [16.295, 21.68], [16.31, 15], [16.353, 8.291], [16.368, 4.717], [16.441, 1.114], [20.191, 0.279], [24.059, 0], [26.314, 0.191], [28.468, 0.762], [30.387, 1.758], [31.925, 3.164], [32.936, 5.039], [33.316, 7.383], [33.316, 7.383]], "i": [[0, 0], [0.283, -0.733], [0.498, -0.547], [0.664, -0.381], [0.762, -0.244], [0.811, -0.117], [0.781, -0.01], [0, -2.275], [2.275, 0], [-0.01, 2.217], [-0.019, 2.256], [0.01, 1.192], [-0.059, 1.211], [-1.25, 0.185], [-1.328, 0], [-0.752, -0.127], [-0.684, -0.264], [-0.586, -0.4], [-0.43, -0.546], [-0.244, -0.703], [0, -0.869], [0, 0]], "o": [[0, 0.947], [-0.283, 0.732], [-0.488, 0.547], [-0.664, 0.381], [-0.752, 0.234], [-0.811, 0.108], [0, 2.275], [-2.275, 0], [0, -2.236], [0.01, -2.216], [0.02, -1.191], [-0.01, -1.191], [1.25, -0.371], [1.25, -0.186], [0.752, 0], [0.752, 0.117], [0.693, 0.264], [0.596, 0.391], [0.43, 0.547], [0.254, 0.694], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-24.806, -10.84]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 6, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 8, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 10, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "26", "layers": [{"ind": 29, "ty": 4, "nm": "!: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [4, 12]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "!", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[260.127, 49.629], [259.878, 50.83], [259.204, 51.797], [258.237, 52.456], [257.05, 52.705], [255.849, 52.456], [254.868, 51.797], [254.209, 50.83], [253.974, 49.629], [254.209, 48.428], [254.868, 47.447], [255.849, 46.773], [257.05, 46.524], [258.237, 46.773], [259.204, 47.447], [259.878, 48.428], [260.127, 49.629], [260.127, 49.629]], "i": [[0, 0], [0.166, -0.371], [0.283, -0.273], [0.371, -0.156], [0.42, 0], [0.371, 0.166], [0.283, 0.283], [0.166, 0.372], [0, 0.43], [-0.157, 0.381], [-0.274, 0.273], [-0.371, 0.166], [-0.429, 0], [-0.371, -0.166], [-0.274, -0.284], [-0.167, -0.381], [0, -0.42], [0, 0]], "o": [[0, 0.43], [-0.167, 0.372], [-0.274, 0.283], [-0.371, 0.166], [-0.429, 0], [-0.371, -0.156], [-0.274, -0.273], [-0.157, -0.371], [0, -0.42], [0.166, -0.381], [0.283, -0.284], [0.371, -0.166], [0.42, 0], [0.371, 0.166], [0.283, 0.273], [0.166, 0.381], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-257.505, -41.206]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[261.093, 29.707], [259.892, 45.118], [254.472, 45.118], [253.916, 29.707], [261.093, 29.707], [261.093, 29.707]], "i": [[0, 0], [0.4, -5.137], [1.807, 0], [0.185, 5.137], [-2.392, 0], [0, 0]], "o": [[-0.4, 5.137], [-1.807, 0], [-0.185, -5.137], [2.392, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-257.505, -41.206]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "27", "layers": [{"ind": 30, "ty": 4, "nm": "O: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [10, 10]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "O", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[244.886, 40.635], [244.696, 39.419], [244.169, 38.365], [243.29, 37.618], [242.103, 37.325], [240.873, 37.574], [239.935, 38.262], [239.349, 39.273], [239.144, 40.489], [239.32, 41.719], [239.847, 42.832], [240.711, 43.638], [241.927, 43.946], [243.158, 43.682], [244.095, 42.95], [244.681, 41.88], [244.886, 40.635], [244.886, 40.635]], "i": [[0, 0], [0.127, 0.391], [0.234, 0.302], [0.351, 0.185], [0.449, 0], [0.361, -0.166], [0.264, -0.293], [0.137, -0.391], [0, -0.43], [-0.117, -0.41], [-0.234, -0.332], [-0.341, -0.205], [-0.459, 0], [-0.362, 0.176], [-0.254, 0.302], [-0.136, 0.401], [0, 0.43], [0, 0]], "o": [[0, -0.42], [-0.117, -0.4], [-0.235, -0.313], [-0.342, -0.196], [-0.459, 0], [-0.362, 0.166], [-0.254, 0.283], [-0.137, 0.381], [0, 0.41], [0.117, 0.41], [0.235, 0.332], [0.352, 0.205], [0.459, 0], [0.371, -0.186], [0.254, -0.313], [0.137, -0.4], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-242.089, -40.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[251.39, 40.254], [251.083, 42.832], [250.189, 45.147], [248.783, 47.139], [246.922, 48.677], [244.681, 49.688], [242.103, 50.039], [239.584, 49.703], [237.357, 48.765], [235.467, 47.3], [234.032, 45.396], [233.109, 43.14], [232.787, 40.635], [233.094, 38.145], [233.973, 35.83], [235.379, 33.838], [237.211, 32.271], [239.422, 31.246], [241.927, 30.879], [245.795, 31.538], [248.783, 33.428], [250.702, 36.387], [251.39, 40.254], [251.39, 40.254]], "i": [[0, 0], [0.205, -0.83], [0.391, -0.723], [0.556, -0.605], [0.694, -0.43], [0.811, -0.244], [0.908, 0], [0.8, 0.224], [0.693, 0.4], [0.567, 0.567], [0.4, 0.693], [0.225, 0.801], [0, 0.869], [-0.205, 0.81], [-0.381, 0.723], [-0.546, 0.606], [-0.674, 0.439], [-0.791, 0.244], [-0.879, 0], [-1.163, -0.439], [-0.821, -0.82], [-0.449, -1.162], [0, -1.425], [0, 0]], "o": [[0, 0.889], [-0.205, 0.821], [-0.381, 0.723], [-0.547, 0.596], [-0.683, 0.43], [-0.81, 0.234], [-0.879, 0], [-0.791, -0.225], [-0.693, -0.41], [-0.556, -0.576], [-0.391, -0.703], [-0.215, -0.801], [0, -0.849], [0.205, -0.82], [0.391, -0.722], [0.547, -0.605], [0.683, -0.44], [0.791, -0.245], [1.416, 0], [1.171, 0.44], [0.83, 0.811], [0.459, 1.152], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-242.089, -40.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "28", "layers": [{"ind": 31, "ty": 4, "nm": "L: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [7, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "L", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[231.096, 43.711], [230.481, 50.215], [218.147, 51.182], [218.616, 30.264], [226.233, 30.264], [225.149, 43.711], [231.096, 43.711], [231.096, 43.711]], "i": [[0, 0], [0.205, -2.168], [4.111, -0.322], [-0.156, 6.973], [-2.539, 0], [0.361, -4.482], [-1.982, 0], [0, 0]], "o": [[-0.205, 2.168], [-4.111, 0.322], [0.156, -6.973], [2.539, 0], [-0.361, 4.482], [1.982, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-224.622, -40.723]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "29", "layers": [{"ind": 32, "ty": 4, "nm": "H: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "H", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[207.999, 50.889], [200.674, 51.241], [200.616, 45.059], [197.247, 45.059], [197.042, 51.475], [190.479, 51.475], [191.036, 30.879], [198.008, 31.202], [197.569, 41.719], [200.821, 41.719], [200.85, 30.704], [207.53, 30.879], [207.999, 50.889], [207.999, 50.889]], "i": [[0, 0], [2.442, -0.117], [0.019, 2.061], [1.123, 0], [0.068, -2.139], [2.188, 0], [-0.186, 6.865], [-2.324, -0.108], [0.146, -3.506], [-1.084, 0], [-0.01, 3.672], [-2.227, -0.058], [-0.156, -6.67], [0, 0]], "o": [[-2.442, 0.117], [-0.019, -2.061], [-1.123, 0], [-0.068, 2.139], [-2.188, 0], [0.186, -6.865], [2.324, 0.108], [-0.146, 3.506], [1.084, 0], [0.01, -3.672], [2.227, 0.058], [0.156, 6.67], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-199.239, -41.09]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "30", "layers": [{"ind": 33, "ty": 4, "nm": "K: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [10, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "K", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[189.038, 32.549], [182.563, 40.254], [188.51, 50.332], [181.713, 51.534], [177.26, 45.498], [177.026, 51.241], [170.932, 51.475], [171.401, 30.498], [178.227, 30.879], [177.7, 36.914], [181.948, 30.791], [189.038, 32.549], [189.038, 32.549]], "i": [[0, 0], [2.158, -2.568], [-1.982, -3.359], [2.266, -0.401], [1.484, 2.012], [0.078, -1.914], [2.031, -0.078], [-0.156, 6.992], [-2.275, -0.127], [0.176, -2.012], [-1.416, 2.041], [-2.363, -0.586], [0, 0]], "o": [[-2.158, 2.568], [1.982, 3.359], [-2.266, 0.401], [-1.484, -2.012], [-0.078, 1.914], [-2.031, 0.078], [0.156, -6.992], [2.275, 0.127], [-0.176, 2.012], [1.416, -2.041], [2.363, 0.586], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-179.985, -41.016]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "31", "layers": [{"ind": 34, "ty": 4, "nm": "E: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "E", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[162.709, 38.877], [162.621, 38.042], [162.328, 37.251], [161.815, 36.68], [161.009, 36.446], [159.969, 36.768], [159.208, 37.559], [158.68, 38.584], [158.373, 39.61], [162.709, 39.2], [162.709, 38.877], [162.709, 38.877]], "i": [[0, 0], [0.058, 0.284], [0.136, 0.235], [0.215, 0.147], [0.323, 0], [0.303, -0.215], [0.214, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.39, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.136, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-160.145, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[168.568, 39.639], [168.392, 42.188], [163.412, 42.715], [158.461, 43.418], [159.647, 45.103], [161.595, 45.733], [162.87, 45.513], [164.261, 44.971], [165.58, 44.253], [166.664, 43.565], [165.961, 50.215], [164.715, 50.962], [163.338, 51.46], [161.888, 51.739], [160.453, 51.827], [157.801, 51.431], [155.619, 50.332], [153.92, 48.648], [152.704, 46.538], [151.971, 44.121], [151.722, 41.543], [151.971, 38.819], [152.718, 36.197], [153.993, 33.868], [155.795, 31.978], [158.109, 30.718], [160.98, 30.264], [163.353, 30.63], [165.272, 31.626], [166.737, 33.135], [167.762, 35.069], [168.363, 37.281], [168.568, 39.639], [168.568, 39.639]], "i": [[0, 0], [0.117, -0.85], [1.65, -0.244], [1.65, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.234, -2.217], [0.45, -0.205], [0.479, -0.127], [0.489, -0.059], [0.468, 0], [0.801, 0.264], [0.654, 0.469], [0.488, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.166, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.684, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.557, -0.429], [-0.41, -0.586], [-0.263, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.651, 0.235], [0.253, 0.703], [0.547, 0.42], [0.391, 0], [0.468, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.234, 2.217], [-0.381, 0.293], [-0.439, 0.205], [-0.478, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.8, -0.264], [-0.645, -0.478], [-0.489, -0.645], [-0.323, -0.761], [-0.166, -0.849], [0, -0.908], [0.166, -0.908], [0.342, -0.84], [0.517, -0.723], [0.683, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.566, 0.42], [0.42, 0.586], [0.274, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-160.145, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "32", "layers": [{"ind": 35, "ty": 4, "nm": "D: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "D", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[139.837, 45.469], [141.433, 44.942], [142.693, 43.946], [143.499, 42.598], [143.792, 40.987], [143.616, 39.434], [143.045, 38.174], [142.004, 37.325], [140.422, 36.973], [139.837, 45.469], [139.837, 45.469]], "i": [[0, 0], [-0.478, 0.263], [-0.352, 0.39], [-0.186, 0.498], [0, 0.566], [0.117, 0.479], [0.263, 0.352], [0.43, 0.205], [0.625, 0.02], [0.195, -2.832], [0, 0]], "o": [[0.585, -0.088], [0.488, -0.274], [0.352, -0.401], [0.195, -0.508], [0, -0.557], [-0.117, -0.488], [-0.264, -0.361], [-0.429, -0.215], [-0.195, 2.832], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-141.448, -40.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[149.797, 39.844], [149.27, 43.521], [147.791, 46.407], [145.549, 48.531], [142.693, 49.981], [139.382, 50.801], [135.794, 51.065], [134.446, 51.036], [133.098, 50.918], [133.508, 31.846], [136.819, 31.128], [140.217, 30.909], [144.07, 31.495], [147.102, 33.223], [149.08, 36.021], [149.797, 39.844], [149.797, 39.844]], "i": [[0, 0], [0.352, -1.084], [0.634, -0.84], [0.87, -0.586], [1.035, -0.381], [1.172, -0.176], [1.23, 0], [0.439, 0.019], [0.459, 0.049], [-0.137, 6.357], [-1.133, 0.137], [-1.123, 0], [-1.172, -0.391], [-0.849, -0.762], [-0.469, -1.113], [0, -1.445], [0, 0]], "o": [[0, 1.367], [-0.351, 1.084], [-0.625, 0.83], [-0.869, 0.586], [-1.035, 0.371], [-1.162, 0.176], [-0.459, 0], [-0.44, -0.03], [0.137, -6.357], [1.075, -0.342], [1.142, -0.146], [1.397, 0], [1.172, 0.39], [0.85, 0.752], [0.478, 1.103], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-141.448, -40.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "33", "layers": [{"ind": 36, "ty": 4, "nm": "R: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "R", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[116.753, 37.823], [116.578, 36.695], [116.08, 35.816], [115.259, 35.23], [114.117, 35.01], [113.545, 35.054], [113.003, 35.157], [112.769, 40.606], [113.121, 40.606], [114.395, 40.445], [115.567, 39.947], [116.417, 39.082], [116.753, 37.823], [116.753, 37.823]], "i": [[0, 0], [0.117, 0.342], [0.224, 0.244], [0.332, 0.137], [0.439, 0], [0.186, -0.029], [0.176, -0.049], [0.078, -1.816], [-0.117, 0], [-0.43, 0.107], [-0.342, 0.224], [-0.215, 0.352], [0, 0.488], [0, 0]], "o": [[0, -0.41], [-0.108, -0.342], [-0.215, -0.254], [-0.322, -0.147], [-0.196, 0], [-0.185, 0.02], [-0.078, 1.816], [0.117, 0], [0.42, 0], [0.44, -0.108], [0.352, -0.225], [0.224, -0.351], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-114.674, -40.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[123.316, 37.354], [123.111, 39.434], [122.496, 41.133], [121.397, 42.569], [119.771, 43.829], [123.257, 50.039], [116.666, 51.329], [114.41, 45.088], [112.593, 45.147], [112.33, 51.241], [106.031, 51.241], [106.163, 44.59], [106.324, 37.94], [106.382, 34.6], [106.5, 31.26], [108.521, 30.63], [110.513, 30.249], [112.549, 30.059], [114.673, 30], [117.896, 30.469], [120.665, 31.846], [122.584, 34.146], [123.316, 37.354], [123.316, 37.354]], "i": [[0, 0], [0.137, -0.625], [0.283, -0.517], [0.449, -0.44], [0.635, -0.401], [-1.162, -2.07], [2.197, -0.43], [0.752, 2.08], [0.606, -0.02], [0.088, -2.031], [2.1, 0], [-0.039, 2.207], [-0.059, 2.226], [-0.019, 1.104], [-0.059, 1.123], [-0.664, 0.166], [-0.664, 0.088], [-0.683, 0.029], [-0.722, 0], [-1.035, -0.312], [-0.801, -0.615], [-0.479, -0.918], [0, -1.221], [0, 0]], "o": [[0, 0.762], [-0.127, 0.615], [-0.284, 0.518], [-0.449, 0.439], [1.162, 2.07], [-2.197, 0.43], [-0.752, -2.08], [-0.606, 0.02], [-0.088, 2.031], [-2.1, 0], [0.049, -2.227], [0.048, -2.207], [0.019, -1.123], [0.02, -1.104], [0.683, -0.254], [0.664, -0.166], [0.674, -0.097], [0.694, -0.039], [1.114, 0], [1.045, 0.303], [0.8, 0.615], [0.488, 0.918], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-114.674, -40.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "34", "layers": [{"ind": 37, "ty": 4, "nm": "A: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [10, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "A", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[95.669, 43.36], [94.555, 38.262], [93.471, 43.36], [95.669, 43.36], [95.669, 43.36]], "i": [[0, 0], [0.371, 1.699], [0.361, -1.699], [-0.733, 0], [0, 0]], "o": [[-0.371, -1.699], [-0.361, 1.699], [0.733, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-94.497, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[104.34, 50.215], [97.075, 51.153], [96.196, 47.842], [92.856, 47.842], [92.124, 51.153], [84.653, 50.42], [90.483, 30.909], [98.628, 30.498], [104.34, 50.215], [104.34, 50.215]], "i": [[0, 0], [2.422, -0.313], [0.293, 1.104], [1.113, 0], [0.244, -1.104], [2.49, 0.244], [-1.943, 6.504], [-2.715, 0.137], [-1.904, -6.572], [0, 0]], "o": [[-2.422, 0.313], [-0.293, -1.104], [-1.113, 0], [-0.244, 1.104], [-2.49, -0.244], [1.943, -6.504], [2.715, -0.137], [1.904, 6.572], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-94.497, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "35", "layers": [{"ind": 38, "ty": 4, "nm": "A: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [10, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "A", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[75.389, 43.36], [74.276, 38.262], [73.192, 43.36], [75.389, 43.36], [75.389, 43.36]], "i": [[0, 0], [0.371, 1.699], [0.361, -1.699], [-0.732, 0], [0, 0]], "o": [[-0.371, -1.699], [-0.361, 1.699], [0.732, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-74.218, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[84.061, 50.215], [76.795, 51.153], [75.917, 47.842], [72.577, 47.842], [71.844, 51.153], [64.374, 50.42], [70.204, 30.909], [78.348, 30.498], [84.061, 50.215], [84.061, 50.215]], "i": [[0, 0], [2.422, -0.313], [0.293, 1.104], [1.113, 0], [0.244, -1.104], [2.49, 0.244], [-1.943, 6.504], [-2.715, 0.137], [-1.904, -6.572], [0, 0]], "o": [[-2.422, 0.313], [-0.293, -1.104], [-1.113, 0], [-0.244, 1.104], [-2.49, -0.244], [1.943, -6.504], [2.715, -0.137], [1.904, 6.572], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-74.218, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "36", "layers": [{"ind": 39, "ty": 4, "nm": "B: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "B", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[53.777, 42.686], [52.957, 42.774], [52.898, 46.114], [53.323, 46.202], [53.748, 46.231], [54.421, 46.172], [55.11, 45.909], [55.637, 45.367], [55.857, 44.473], [55.637, 43.565], [55.125, 43.023], [54.451, 42.759], [53.777, 42.686], [53.777, 42.686]], "i": [[0, 0], [0.263, -0.059], [0.02, -1.113], [-0.137, -0.03], [-0.137, 0], [-0.244, 0.039], [-0.215, 0.127], [-0.136, 0.224], [0, 0.371], [0.147, 0.234], [0.205, 0.127], [0.244, 0.039], [0.205, 0], [0, 0]], "o": [[-0.283, 0], [-0.02, 1.113], [0.147, 0.029], [0.146, 0.019], [0.205, 0], [0.245, -0.049], [0.215, -0.137], [0.147, -0.225], [0, -0.371], [-0.136, -0.235], [-0.205, -0.137], [-0.244, -0.049], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-54.788, -40.973]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[55.857, 37.911], [55.681, 37.193], [55.242, 36.695], [54.685, 36.416], [54.158, 36.329], [53.587, 36.416], [53.045, 36.621], [52.957, 39.873], [53.792, 39.815], [54.744, 39.566], [55.535, 38.98], [55.857, 37.911], [55.857, 37.911]], "i": [[0, 0], [0.117, 0.195], [0.176, 0.127], [0.205, 0.059], [0.156, 0], [0.185, -0.058], [0.175, -0.078], [0.029, -1.084], [-0.332, 0.039], [-0.303, 0.127], [-0.215, 0.254], [0, 0.449], [0, 0]], "o": [[0, -0.284], [-0.117, -0.205], [-0.166, -0.127], [-0.195, -0.058], [-0.196, 0], [-0.186, 0.059], [-0.029, 1.084], [0.224, 0], [0.332, -0.039], [0.312, -0.137], [0.215, -0.264], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-54.788, -40.973]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[63.298, 46.26], [62.844, 48.355], [61.658, 49.849], [59.929, 50.86], [57.908, 51.46], [55.784, 51.753], [53.806, 51.827], [51.917, 51.753], [49.983, 51.534], [48.079, 51.109], [46.306, 50.42], [46.277, 31.553], [48.02, 30.953], [49.895, 30.498], [51.814, 30.22], [53.66, 30.118], [55.754, 30.249], [57.805, 30.689], [59.636, 31.495], [61.116, 32.696], [62.112, 34.366], [62.478, 36.563], [62.244, 38.101], [61.57, 39.39], [60.501, 40.371], [59.08, 40.957], [60.808, 41.69], [62.141, 42.862], [62.991, 44.414], [63.298, 46.26], [63.298, 46.26]], "i": [[0, 0], [0.303, -0.586], [0.498, -0.41], [0.655, -0.264], [0.703, -0.146], [0.713, -0.049], [0.615, 0], [0.654, 0.049], [0.645, 0.107], [0.625, 0.176], [0.557, 0.274], [0.01, 6.289], [-0.615, 0.175], [-0.635, 0.118], [-0.635, 0.059], [-0.596, 0], [-0.703, -0.087], [-0.654, -0.205], [-0.566, -0.332], [-0.42, -0.479], [-0.234, -0.635], [0, -0.83], [0.156, -0.479], [0.293, -0.381], [0.419, -0.273], [0.527, -0.127], [-0.517, -0.322], [-0.361, -0.459], [-0.196, -0.576], [0, -0.654], [0, 0]], "o": [[0, 0.811], [-0.293, 0.586], [-0.498, 0.41], [-0.644, 0.254], [-0.703, 0.147], [-0.703, 0.049], [-0.605, 0], [-0.645, -0.039], [-0.645, -0.108], [-0.625, -0.186], [-0.01, -6.289], [0.547, -0.224], [0.615, -0.186], [0.645, -0.127], [0.635, -0.068], [0.693, 0], [0.713, 0.088], [0.655, 0.205], [0.567, 0.322], [0.429, 0.478], [0.244, 0.634], [0, 0.547], [-0.157, 0.478], [-0.293, 0.381], [-0.42, 0.264], [0.634, 0.166], [0.528, 0.322], [0.371, 0.459], [0.205, 0.577], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-54.788, -40.973]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "37", "layers": [{"ind": 40, "ty": 4, "nm": "K: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [10, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "K", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[37.315, 32.549], [30.841, 40.254], [36.788, 50.332], [29.991, 51.534], [25.538, 45.498], [25.304, 51.241], [19.21, 51.475], [19.679, 30.498], [26.505, 30.879], [25.978, 36.914], [30.226, 30.791], [37.315, 32.549], [37.315, 32.549]], "i": [[0, 0], [2.158, -2.568], [-1.983, -3.359], [2.266, -0.401], [1.484, 2.012], [0.078, -1.914], [2.031, -0.078], [-0.156, 6.992], [-2.275, -0.127], [0.176, -2.012], [-1.416, 2.041], [-2.363, -0.586], [0, 0]], "o": [[-2.158, 2.568], [1.983, 3.359], [-2.266, 0.401], [-1.484, -2.012], [-0.078, 1.914], [-2.031, 0.078], [0.156, -6.992], [2.275, 0.127], [-0.176, 2.012], [1.416, -2.041], [2.363, 0.586], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-28.263, -41.016]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "38", "layers": [{"ind": 41, "ty": 4, "nm": "E: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "E", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[10.986, 38.877], [10.898, 38.042], [10.606, 37.251], [10.093, 36.68], [9.287, 36.446], [8.247, 36.768], [7.485, 37.559], [6.958, 38.584], [6.65, 39.61], [10.986, 39.2], [10.986, 38.877], [10.986, 38.877]], "i": [[0, 0], [0.059, 0.284], [0.137, 0.235], [0.215, 0.147], [0.322, 0], [0.303, -0.215], [0.215, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.391, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.137, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-8.423, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[16.846, 39.639], [16.67, 42.188], [11.689, 42.715], [6.738, 43.418], [7.925, 45.103], [9.873, 45.733], [11.147, 45.513], [12.539, 44.971], [13.857, 44.253], [14.941, 43.565], [14.238, 50.215], [12.993, 50.962], [11.616, 51.46], [10.166, 51.739], [8.731, 51.827], [6.079, 51.431], [3.897, 50.332], [2.197, 48.648], [0.981, 46.538], [0.249, 44.121], [0, 41.543], [0.249, 38.819], [0.996, 36.197], [2.27, 33.868], [4.072, 31.978], [6.387, 30.718], [9.258, 30.264], [11.631, 30.63], [13.55, 31.626], [15.015, 33.135], [16.04, 35.069], [16.641, 37.281], [16.846, 39.639], [16.846, 39.639]], "i": [[0, 0], [0.117, -0.85], [1.65, -0.244], [1.65, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.234, -2.217], [0.449, -0.205], [0.478, -0.127], [0.488, -0.059], [0.469, 0], [0.801, 0.264], [0.654, 0.469], [0.488, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.166, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.684, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.557, -0.429], [-0.41, -0.586], [-0.264, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.65, 0.235], [0.254, 0.703], [0.547, 0.42], [0.391, 0], [0.469, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.234, 2.217], [-0.381, 0.293], [-0.439, 0.205], [-0.479, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.801, -0.264], [-0.645, -0.478], [-0.488, -0.645], [-0.322, -0.761], [-0.166, -0.849], [0, -0.908], [0.166, -0.908], [0.342, -0.84], [0.518, -0.723], [0.683, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.566, 0.42], [0.42, 0.586], [0.273, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-8.423, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "39", "layers": [{"ind": 42, "ty": 4, "nm": "W: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [14, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "W", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[245.692, 1.143], [242.264, 20.86], [233.944, 21.036], [232.157, 11.104], [230.37, 20.274], [222.049, 20.567], [218.592, 1.729], [225.858, 1.084], [227.323, 12.657], [229.286, 1.963], [234.999, 1.963], [237.284, 12.803], [238.456, 0.498], [245.692, 1.143], [245.692, 1.143]], "i": [[0, 0], [1.143, -6.572], [2.773, -0.059], [0.596, 3.311], [0.596, -3.057], [2.774, -0.098], [1.152, 6.279], [-2.422, 0.215], [-0.488, -3.858], [-0.654, 3.565], [-1.904, 0], [-0.762, -3.613], [-0.391, 4.102], [-2.412, -0.215], [0, 0]], "o": [[-1.143, 6.572], [-2.773, 0.059], [-0.596, -3.311], [-0.596, 3.057], [-2.774, 0.098], [-1.152, -6.279], [2.422, -0.215], [0.488, 3.858], [0.654, -3.565], [1.904, 0], [0.762, 3.613], [0.391, -4.102], [2.412, 0.215], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-232.142, -10.767]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "40", "layers": [{"ind": 43, "ty": 4, "nm": "O: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [10, 10]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "O", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[210.618, 10.635], [210.427, 9.419], [209.9, 8.365], [209.021, 7.618], [207.835, 7.325], [206.604, 7.574], [205.667, 8.262], [205.081, 9.273], [204.876, 10.489], [205.051, 11.719], [205.579, 12.832], [206.443, 13.638], [207.659, 13.946], [208.889, 13.682], [209.827, 12.95], [210.413, 11.88], [210.618, 10.635], [210.618, 10.635]], "i": [[0, 0], [0.127, 0.391], [0.234, 0.302], [0.352, 0.185], [0.449, 0], [0.361, -0.166], [0.263, -0.293], [0.136, -0.391], [0, -0.43], [-0.117, -0.41], [-0.235, -0.332], [-0.342, -0.205], [-0.459, 0], [-0.361, 0.176], [-0.254, 0.302], [-0.137, 0.401], [0, 0.43], [0, 0]], "o": [[0, -0.42], [-0.117, -0.4], [-0.234, -0.313], [-0.342, -0.196], [-0.459, 0], [-0.361, 0.166], [-0.254, 0.283], [-0.137, 0.381], [0, 0.41], [0.118, 0.41], [0.234, 0.332], [0.352, 0.205], [0.459, 0], [0.371, -0.186], [0.254, -0.313], [0.136, -0.4], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-207.82, -10.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[217.122, 10.254], [216.814, 12.832], [215.92, 15.147], [214.514, 17.139], [212.654, 18.677], [210.413, 19.688], [207.835, 20.039], [205.315, 19.703], [203.088, 18.765], [201.199, 17.3], [199.763, 15.396], [198.84, 13.14], [198.518, 10.635], [198.826, 8.145], [199.705, 5.83], [201.111, 3.838], [202.942, 2.271], [205.154, 1.246], [207.659, 0.879], [211.526, 1.538], [214.514, 3.428], [216.433, 6.387], [217.122, 10.254], [217.122, 10.254]], "i": [[0, 0], [0.205, -0.83], [0.391, -0.723], [0.557, -0.605], [0.693, -0.43], [0.81, -0.244], [0.908, 0], [0.801, 0.224], [0.694, 0.4], [0.566, 0.567], [0.401, 0.693], [0.225, 0.801], [0, 0.869], [-0.205, 0.81], [-0.381, 0.723], [-0.547, 0.606], [-0.674, 0.439], [-0.791, 0.244], [-0.879, 0], [-1.162, -0.439], [-0.82, -0.82], [-0.449, -1.162], [0, -1.425], [0, 0]], "o": [[0, 0.889], [-0.205, 0.821], [-0.38, 0.723], [-0.547, 0.596], [-0.684, 0.43], [-0.811, 0.234], [-0.879, 0], [-0.791, -0.225], [-0.693, -0.41], [-0.557, -0.576], [-0.39, -0.703], [-0.214, -0.801], [0, -0.849], [0.205, -0.82], [0.39, -0.722], [0.547, -0.605], [0.684, -0.44], [0.791, -0.245], [1.416, 0], [1.172, 0.44], [0.83, 0.811], [0.459, 1.152], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-207.82, -10.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "41", "layers": [{"ind": 44, "ty": 4, "nm": "L: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [7, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "L", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[196.828, 13.711], [196.212, 20.215], [183.878, 21.182], [184.347, 0.264], [191.964, 0.264], [190.88, 13.711], [196.828, 13.711], [196.828, 13.711]], "i": [[0, 0], [0.205, -2.168], [4.111, -0.322], [-0.156, 6.973], [-2.539, 0], [0.361, -4.482], [-1.983, 0], [0, 0]], "o": [[-0.205, 2.168], [-4.111, 0.322], [0.156, -6.973], [2.539, 0], [-0.361, 4.482], [1.983, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-190.353, -10.723]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "42", "layers": [{"ind": 45, "ty": 4, "nm": "e: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "e", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[168.295, 8.877], [168.208, 8.042], [167.915, 7.251], [167.402, 6.68], [166.596, 6.446], [165.556, 6.768], [164.795, 7.559], [164.267, 8.584], [163.96, 9.61], [168.295, 9.2], [168.295, 8.877], [168.295, 8.877]], "i": [[0, 0], [0.058, 0.284], [0.136, 0.235], [0.215, 0.147], [0.323, 0], [0.303, -0.215], [0.214, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.39, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.137, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-165.732, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[174.155, 9.639], [173.979, 12.188], [168.999, 12.715], [164.047, 13.418], [165.234, 15.103], [167.182, 15.733], [168.457, 15.513], [169.848, 14.971], [171.167, 14.253], [172.251, 13.565], [171.547, 20.215], [170.302, 20.962], [168.925, 21.46], [167.475, 21.739], [166.04, 21.827], [163.388, 21.431], [161.206, 20.332], [159.506, 18.648], [158.291, 16.538], [157.558, 14.121], [157.309, 11.543], [157.558, 8.819], [158.305, 6.197], [159.58, 3.868], [161.381, 1.978], [163.696, 0.718], [166.567, 0.264], [168.94, 0.63], [170.859, 1.626], [172.324, 3.135], [173.349, 5.069], [173.95, 7.281], [174.155, 9.639], [174.155, 9.639]], "i": [[0, 0], [0.117, -0.85], [1.65, -0.244], [1.651, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.235, -2.217], [0.45, -0.205], [0.479, -0.127], [0.488, -0.059], [0.468, 0], [0.801, 0.264], [0.654, 0.469], [0.489, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.166, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.683, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.557, -0.429], [-0.41, -0.586], [-0.263, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.651, 0.235], [0.254, 0.703], [0.547, 0.42], [0.391, 0], [0.468, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.235, 2.217], [-0.38, 0.293], [-0.439, 0.205], [-0.478, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.801, -0.264], [-0.645, -0.478], [-0.488, -0.645], [-0.323, -0.761], [-0.166, -0.849], [0, -0.908], [0.166, -0.908], [0.342, -0.84], [0.517, -0.723], [0.684, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.566, 0.42], [0.42, 0.586], [0.274, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-165.732, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "43", "layers": [{"ind": 46, "ty": 4, "nm": "N: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [10, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "N", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[154.916, 14.063], [154.872, 17.344], [154.769, 20.596], [146.332, 20.45], [147.093, 15.279], [147.416, 10.049], [147.401, 9.434], [147.342, 8.482], [147.211, 7.383], [146.991, 6.343], [146.654, 5.581], [146.185, 5.274], [145.336, 5.567], [144.764, 6.329], [144.413, 7.354], [144.208, 8.496], [144.12, 9.58], [144.105, 10.401], [143.988, 15.557], [143.871, 20.713], [136.4, 21.036], [136.957, 10.752], [137.396, 0.469], [144.31, 0.118], [144.252, 2.461], [145.13, 1.524], [146.156, 0.777], [147.298, 0.279], [148.587, 0.088], [150.77, 0.498], [152.381, 1.612], [153.509, 3.282], [154.242, 5.318], [154.667, 7.588], [154.857, 9.903], [154.916, 12.115], [154.916, 14.063], [154.916, 14.063]], "i": [[0, 0], [0.029, -1.084], [0.049, -1.094], [2.812, 0.049], [-0.215, 1.718], [0, 1.758], [0.01, 0.283], [0.03, 0.351], [0.058, 0.371], [0.097, 0.313], [0.137, 0.196], [0.186, 0], [0.234, -0.196], [0.156, -0.313], [0.088, -0.381], [0.048, -0.38], [0.019, -0.341], [0, -0.215], [0.029, -1.709], [0.049, -1.738], [2.49, -0.108], [-0.176, 3.409], [-0.107, 3.447], [-2.305, 0.117], [0.019, -0.781], [-0.312, 0.293], [-0.361, 0.205], [-0.4, 0.117], [-0.449, 0], [-0.625, -0.273], [-0.449, -0.469], [-0.302, -0.645], [-0.186, -0.723], [-0.098, -0.791], [-0.029, -0.762], [0, -0.713], [0, -0.586], [0, 0]], "o": [[0, 1.103], [-0.02, 1.074], [-2.812, -0.049], [0.293, -1.729], [0.215, -1.729], [0, -0.127], [-0.01, -0.283], [-0.029, -0.361], [-0.049, -0.381], [-0.088, -0.312], [-0.127, -0.205], [-0.332, 0], [-0.225, 0.195], [-0.146, 0.302], [-0.088, 0.381], [-0.039, 0.381], [-0.01, 0.333], [-0.049, 1.728], [-0.029, 1.699], [-2.49, 0.108], [0.195, -3.448], [0.185, -3.408], [2.305, -0.117], [-0.019, 0.781], [0.273, -0.332], [0.323, -0.293], [0.361, -0.215], [0.411, -0.127], [0.831, 0], [0.625, 0.274], [0.45, 0.468], [0.303, 0.634], [0.185, 0.722], [0.097, 0.782], [0.039, 0.761], [0, 0.713], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-145.658, -10.562]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "44", "layers": [{"ind": 47, "ty": 4, "nm": "T: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "T", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[134.578, 0.821], [134.431, 7.032], [130.066, 7.207], [129.539, 20.801], [123.357, 21.123], [122.8, 7.53], [118.435, 7.764], [118.611, 0.85], [134.578, 0.821], [134.578, 0.821]], "i": [[0, 0], [0.049, -2.07], [1.455, -0.058], [0.176, -4.531], [2.061, -0.107], [0.186, 4.531], [1.455, -0.078], [-0.059, 2.305], [-5.322, 0.01], [0, 0]], "o": [[-0.049, 2.07], [-1.455, 0.058], [-0.176, 4.531], [-2.061, 0.107], [-0.186, -4.531], [-1.455, 0.078], [0.059, -2.305], [5.322, -0.01], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-126.507, -10.972]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "45", "layers": [{"ind": 48, "ty": 4, "nm": "I: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [4, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "I", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[116.364, 0.85], [115.338, 20.801], [109.157, 21.123], [108.571, 1.26], [116.364, 0.85], [116.364, 0.85]], "i": [[0, 0], [0.342, -6.65], [2.06, -0.107], [0.195, 6.621], [-2.598, 0.137], [0, 0]], "o": [[-0.342, 6.65], [-2.06, 0.107], [-0.195, -6.621], [2.598, -0.137], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-112.468, -10.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "46", "layers": [{"ind": 49, "ty": 4, "nm": "e: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "e", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[93.032, 8.877], [92.944, 8.042], [92.651, 7.251], [92.138, 6.68], [91.333, 6.446], [90.293, 6.768], [89.531, 7.559], [89.003, 8.584], [88.696, 9.61], [93.032, 9.2], [93.032, 8.877], [93.032, 8.877]], "i": [[0, 0], [0.059, 0.284], [0.137, 0.235], [0.215, 0.147], [0.322, 0], [0.302, -0.215], [0.215, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.391, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.136, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-90.468, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[98.891, 9.639], [98.715, 12.188], [93.735, 12.715], [88.784, 13.418], [89.97, 15.103], [91.919, 15.733], [93.193, 15.513], [94.585, 14.971], [95.903, 14.253], [96.987, 13.565], [96.284, 20.215], [95.039, 20.962], [93.662, 21.46], [92.211, 21.739], [90.776, 21.827], [88.125, 21.431], [85.942, 20.332], [84.243, 18.648], [83.027, 16.538], [82.295, 14.121], [82.045, 11.543], [82.295, 8.819], [83.042, 6.197], [84.316, 3.868], [86.118, 1.978], [88.432, 0.718], [91.303, 0.264], [93.676, 0.63], [95.595, 1.626], [97.06, 3.135], [98.086, 5.069], [98.686, 7.281], [98.891, 9.639], [98.891, 9.639]], "i": [[0, 0], [0.118, -0.85], [1.65, -0.244], [1.65, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.234, -2.217], [0.449, -0.205], [0.478, -0.127], [0.489, -0.059], [0.469, 0], [0.8, 0.264], [0.654, 0.469], [0.488, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.167, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.684, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.556, -0.429], [-0.41, -0.586], [-0.264, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.65, 0.235], [0.254, 0.703], [0.547, 0.42], [0.39, 0], [0.469, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.234, 2.217], [-0.381, 0.293], [-0.44, 0.205], [-0.479, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.801, -0.264], [-0.645, -0.478], [-0.489, -0.645], [-0.322, -0.761], [-0.167, -0.849], [0, -0.908], [0.166, -0.908], [0.341, -0.84], [0.518, -0.723], [0.683, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.567, 0.42], [0.42, 0.586], [0.273, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-90.468, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "47", "layers": [{"ind": 50, "ty": 4, "nm": "C: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [8, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "C", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[79.637, 1.465], [79.11, 7.647], [78.275, 7.486], [77.44, 7.442], [75.448, 7.705], [73.778, 8.511], [72.621, 9.888], [72.196, 11.836], [72.43, 13.272], [73.119, 14.283], [74.203, 14.883], [75.624, 15.088], [76.737, 14.986], [77.879, 14.678], [78.978, 14.239], [79.96, 13.711], [79.725, 20.596], [78.495, 21.123], [77.132, 21.504], [75.741, 21.768], [74.422, 21.856], [70.628, 21.079], [67.699, 18.97], [65.809, 15.85], [65.135, 12.071], [65.809, 7.588], [67.816, 3.985], [71.097, 1.582], [75.594, 0.704], [77.645, 0.865], [79.637, 1.465], [79.637, 1.465]], "i": [[0, 0], [0.176, -2.061], [0.273, 0.029], [0.283, 0], [0.635, -0.175], [0.488, -0.361], [0.293, -0.557], [0, -0.752], [-0.156, -0.41], [-0.293, -0.274], [-0.42, -0.137], [-0.518, 0], [-0.381, 0.068], [-0.38, 0.127], [-0.351, 0.166], [-0.303, 0.186], [0.078, -2.295], [0.439, -0.156], [0.469, -0.107], [0.469, -0.059], [0.42, 0], [1.143, 0.518], [0.82, 0.889], [0.449, 1.182], [0, 1.338], [-0.449, 1.387], [-0.879, 1.015], [-1.299, 0.577], [-1.689, 0], [-0.693, -0.108], [-0.634, -0.293], [0, 0]], "o": [[-0.176, 2.061], [-0.283, -0.078], [-0.273, -0.03], [-0.693, 0], [-0.625, 0.176], [-0.479, 0.361], [-0.284, 0.547], [0, 0.547], [0.166, 0.4], [0.302, 0.263], [0.429, 0.137], [0.361, 0], [0.381, -0.078], [0.381, -0.127], [0.352, -0.166], [-0.078, 2.295], [-0.381, 0.195], [-0.44, 0.147], [-0.459, 0.117], [-0.459, 0.059], [-1.386, 0], [-1.132, -0.517], [-0.811, -0.898], [-0.449, -1.182], [0, -1.602], [0.459, -1.386], [0.889, -1.026], [1.309, -0.586], [0.674, 0], [0.693, 0.107], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-72.548, -11.28]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "48", "layers": [{"ind": 51, "ty": 4, "nm": "I: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [4, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "I", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[62.961, 0.85], [61.936, 20.801], [55.754, 21.123], [55.169, 1.26], [62.961, 0.85], [62.961, 0.85]], "i": [[0, 0], [0.342, -6.65], [2.061, -0.107], [0.195, 6.621], [-2.597, 0.137], [0, 0]], "o": [[-0.342, 6.65], [-2.061, 0.107], [-0.195, -6.621], [2.597, -0.137], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-59.065, -10.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "49", "layers": [{"ind": 52, "ty": 4, "nm": "R: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "R", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[46.227, 7.823], [46.051, 6.695], [45.553, 5.816], [44.733, 5.23], [43.59, 5.01], [43.019, 5.054], [42.477, 5.157], [42.243, 10.606], [42.594, 10.606], [43.869, 10.445], [45.041, 9.947], [45.89, 9.082], [46.227, 7.823], [46.227, 7.823]], "i": [[0, 0], [0.118, 0.342], [0.225, 0.244], [0.332, 0.137], [0.44, 0], [0.186, -0.029], [0.176, -0.049], [0.078, -1.816], [-0.117, 0], [-0.43, 0.107], [-0.342, 0.224], [-0.215, 0.352], [0, 0.488], [0, 0]], "o": [[0, -0.41], [-0.107, -0.342], [-0.215, -0.254], [-0.322, -0.147], [-0.195, 0], [-0.185, 0.02], [-0.078, 1.816], [0.117, 0], [0.42, 0], [0.439, -0.108], [0.351, -0.225], [0.225, -0.351], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-44.147, -10.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[52.79, 7.354], [52.585, 9.434], [51.969, 11.133], [50.871, 12.569], [49.245, 13.829], [52.731, 20.039], [46.139, 21.329], [43.883, 15.088], [42.067, 15.147], [41.803, 21.241], [35.504, 21.241], [35.636, 14.59], [35.797, 7.94], [35.856, 4.6], [35.973, 1.26], [37.995, 0.63], [39.987, 0.249], [42.023, 0.059], [44.147, 0], [47.37, 0.469], [50.138, 1.846], [52.057, 4.146], [52.79, 7.354], [52.79, 7.354]], "i": [[0, 0], [0.136, -0.625], [0.284, -0.517], [0.449, -0.44], [0.634, -0.401], [-1.162, -2.07], [2.197, -0.43], [0.752, 2.08], [0.605, -0.02], [0.088, -2.031], [2.1, 0], [-0.039, 2.207], [-0.058, 2.226], [-0.019, 1.104], [-0.058, 1.123], [-0.664, 0.166], [-0.664, 0.088], [-0.684, 0.029], [-0.723, 0], [-1.035, -0.312], [-0.801, -0.615], [-0.478, -0.918], [0, -1.221], [0, 0]], "o": [[0, 0.762], [-0.127, 0.615], [-0.283, 0.518], [-0.45, 0.439], [1.162, 2.07], [-2.197, 0.43], [-0.752, -2.08], [-0.605, 0.02], [-0.088, 2.031], [-2.1, 0], [0.049, -2.227], [0.049, -2.207], [0.02, -1.123], [0.02, -1.104], [0.684, -0.254], [0.664, -0.166], [0.674, -0.097], [0.693, -0.039], [1.113, 0], [1.045, 0.303], [0.801, 0.615], [0.488, 0.918], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-44.147, -10.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "50", "layers": [{"ind": 53, "ty": 4, "nm": "P: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [9, 11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "P", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[26.813, 7.881], [26.227, 6.402], [24.732, 5.86], [24.088, 5.918], [23.473, 6.036], [23.297, 10.313], [23.692, 10.342], [24.088, 10.342], [25.099, 10.166], [25.978, 9.668], [26.578, 8.892], [26.813, 7.881], [26.813, 7.881]], "i": [[0, 0], [0.391, 0.351], [0.615, 0], [0.225, -0.039], [0.195, -0.049], [0.059, -1.426], [-0.127, 0], [-0.137, 0], [-0.322, 0.118], [-0.254, 0.215], [-0.147, 0.293], [0, 0.371], [0, 0]], "o": [[0, -0.635], [-0.381, -0.362], [-0.205, 0], [-0.215, 0.03], [-0.059, 1.426], [0.137, 0.019], [0.127, 0], [0.352, 0], [0.332, -0.117], [0.254, -0.224], [0.156, -0.303], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-24.806, -10.84]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[33.316, 7.383], [32.892, 9.903], [31.72, 11.822], [29.991, 13.213], [27.853, 14.151], [25.509, 14.678], [23.121, 14.854], [23.121, 21.68], [16.295, 21.68], [16.31, 15], [16.353, 8.291], [16.368, 4.717], [16.441, 1.114], [20.191, 0.279], [24.059, 0], [26.314, 0.191], [28.468, 0.762], [30.387, 1.758], [31.925, 3.164], [32.936, 5.039], [33.316, 7.383], [33.316, 7.383]], "i": [[0, 0], [0.283, -0.733], [0.498, -0.547], [0.664, -0.381], [0.762, -0.244], [0.811, -0.117], [0.781, -0.01], [0, -2.275], [2.275, 0], [-0.01, 2.217], [-0.019, 2.256], [0.01, 1.192], [-0.059, 1.211], [-1.25, 0.185], [-1.328, 0], [-0.752, -0.127], [-0.684, -0.264], [-0.586, -0.4], [-0.43, -0.546], [-0.244, -0.703], [0, -0.869], [0, 0]], "o": [[0, 0.947], [-0.283, 0.732], [-0.488, 0.547], [-0.664, 0.381], [-0.752, 0.234], [-0.811, 0.108], [0, 2.275], [-2.275, 0], [0, -2.236], [0.01, -2.216], [0.02, -1.191], [-0.01, -1.191], [1.25, -0.371], [1.25, -0.186], [0.752, 0], [0.752, 0.117], [0.693, 0.264], [0.596, 0.391], [0.43, 0.547], [0.254, 0.694], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-24.806, -10.84]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "51", "layers": [{"ind": 54, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 0, "k": [197, 190.584]}, "a": {"a": 0, "k": [197, 100.584]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0}, {"refId": "52", "w": 397, "h": 331, "ind": 55, "ty": 0, "nm": "SVG Group", "sr": 1, "ks": {"p": {"a": 0, "k": [-1, -65]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 32}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 54}, {"ind": 56, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 0, "k": [198, 207]}, "a": {"a": 0, "k": [295, 207]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0}, {"refId": "53", "w": 396, "h": 416, "ind": 57, "ty": 0, "nm": "Path", "sr": 1, "ks": {"p": {"a": 0, "k": [96, -1]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 56}, {"ind": 58, "ty": 4, "nm": "Path", "sr": 1, "ks": {"p": {"a": 0, "k": [-97, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[0, 0], [393, 0], [393, 372.5], [0, 371.5], [0, 0], [0, 0]], "i": [[0, 0], [-131, 0], [0, -124.167], [104.5, 53.5], [0, 123.833], [0, 0]], "o": [[131, 0], [0, 124.167], [-112.5, 55.5], [0, -123.833], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.851, 0.851, 0.851]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [294.5, 206.444]}, "a": {"a": 0, "k": [196.5, 206.444]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}, {"ind": 59, "ty": 4, "nm": "SvgClip - box", "sr": 1, "ks": {"p": {"a": 0, "k": [-97, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [394, 415]}, "p": {"a": 0, "k": [294, 207.5]}, "r": {"a": 0, "k": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 0}, "r": 1}], "ip": 0, "op": 501, "st": 0}]}, {"id": "54", "layers": [{"ind": 60, "ty": 4, "nm": "SvgClip", "sr": 1, "ks": {"p": {"a": 0, "k": [-97, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "td": 1, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "nm": "Rectangle", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [393, 414.501]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [196.5, 207.251]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 0, "k": [294.5, 207.251]}, "a": {"a": 0, "k": [196.5, 207.251]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}, {"refId": "51", "w": 394, "h": 415, "ind": 61, "ty": 0, "nm": "SVG", "sr": 1, "ks": {"p": {"a": 0, "k": [294, 207.5]}, "a": {"a": 0, "k": [294, 207.5]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "tt": 1}]}, {"id": "52", "layers": [{"ind": 62, "ty": 4, "nm": "Comic", "sr": 1, "ks": {"p": {"a": 0, "k": [1, 65]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[75.364, 0], [75.364, 13.081], [0, 52.252], [75.364, 0], [75.364, 0]], "i": [[0, 0], [0, -4.36], [25.121, -13.057], [-25.121, 17.417], [0, 0]], "o": [[0, 4.36], [-25.121, 13.057], [25.121, -17.417], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.18, 0.145]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [159.318, -74.458]}, "a": {"a": 0, "k": [37.682, 26.126]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[42.269, 0], [42.269, 18.809], [0, 9.755], [42.269, 0], [42.269, 0]], "i": [[0, 0], [0, -6.27], [14.09, 3.018], [-14.09, 3.252], [0, 0]], "o": [[0, 6.27], [-14.09, -3.018], [14.09, -3.252], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.18, 0.145]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [175.865, -10.858]}, "a": {"a": 0, "k": [21.135, 9.404]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[51.725, 19.984], [51.725, 26.745], [0, 0], [51.725, 19.984], [51.725, 19.984]], "i": [[0, 0], [0, -2.254], [17.242, 8.915], [-17.242, -6.661], [0, 0]], "o": [[0, 2.254], [-17.242, -8.915], [17.242, 6.661], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.18, 0.145]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [171.136, 65.904]}, "a": {"a": 0, "k": [25.863, 13.372]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[80.659, 0], [0, 37.808], [0, 26.682], [80.659, 0], [80.659, 0]], "i": [[0, 0], [26.886, -12.603], [0, 3.709], [-26.886, 8.894], [0, 0]], "o": [[-26.886, 12.603], [0, -3.709], [26.886, -8.894], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.18, 0.145]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [-156.67, 81.68]}, "a": {"a": 0, "k": [40.33, 18.904]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[71.991, 2.797], [0, 16.564], [0, 0], [71.991, 2.797], [71.991, 2.797]], "i": [[0, 0], [23.997, -4.589], [0, 5.521], [-23.997, -0.932], [0, 0]], "o": [[-23.997, 4.589], [0, -5.521], [23.997, 0.932], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.18, 0.145]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [-161.004, 27.285]}, "a": {"a": 0, "k": [35.996, 8.282]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[65.688, 16.571], [0, 8.573], [0, 0], [65.688, 16.571], [65.688, 16.571]], "i": [[0, 0], [21.896, 2.666], [0, 2.858], [-21.896, -5.524], [0, 0]], "o": [[-21.896, -2.666], [0, -2.858], [21.896, 5.524], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.18, 0.145]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [-164.156, -51.89]}, "a": {"a": 0, "k": [32.844, 8.285]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 0, "s": [197, 100.584], "o": {"x": [0.8], "y": [0]}, "i": {"x": [0.5], "y": [1.5]}}, {"t": 101, "s": [197, 100.584], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 113, "s": [197, 100.584], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 120, "s": [197, 100.584], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 140, "s": [197, 100.584], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"t": 0, "s": [163.899, 163.899], "o": {"x": [0.8], "y": [0]}, "i": {"x": [0.5], "y": [1.5]}}, {"t": 101, "s": [111.766, 111.766], "o": {"x": [0.8], "y": [0]}, "i": {"x": [0.5], "y": [1.5]}}, {"t": 113, "s": [125.73, 125.73], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 125, "s": [102.456, 102.456], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 140, "s": [109.904, 109.904], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}, {"id": "55", "layers": [{"ind": 63, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 0, "k": [-96, 1]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0}, {"ind": 64, "ty": 4, "nm": "Path's gradient fill mesh - Radial gradient: 0:0: rg-shape-mask", "sr": 1, "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "td": 1, "ao": 0, "parent": 63, "shapes": [{"ty": "gr", "nm": "Path's gradient fill mesh - Radial gradient", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [2, 2]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [295, 207]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [29500, 20700]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}, {"refId": "56", "w": 396, "h": 416, "ind": 65, "ty": 0, "nm": "Path's gradient fill mesh - Radial gradient: 0:0: rg-precomp", "sr": 1, "ks": {"p": {"a": 0, "k": [96, -1]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 63, "tt": 1}, {"ind": 66, "ty": 4, "nm": "Path's gradient fill mask - box", "sr": 1, "ks": {"p": {"a": 0, "k": [-96, 1]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [396, 416]}, "p": {"a": 0, "k": [294, 207]}, "r": {"a": 0, "k": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 0}, "r": 1}], "ip": 0, "op": 501, "st": 0}]}, {"id": "53", "layers": [{"ind": 67, "ty": 4, "nm": "Path's gradient fill mask", "sr": 1, "ks": {"p": {"a": 0, "k": [-96, 1]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "td": 1, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "nm": "Path's gradient fill mask", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[491, 126.556], [590, 249.5], [295, 414], [0, 249.5], [98, 127.057], [98, 0], [491, 0], [491, 126.556], [491, 126.556]], "i": [[0, 0], [0, -48.898], [162.924, 0], [0, 90.851], [-60.158, 30.115], [0, 42.352], [-131, 0], [0, -42.185], [0, 0]], "o": [[60.739, 30.131], [0, 90.851], [-162.924, 0], [0, -48.629], [0, -42.352], [131, 0], [0, 42.185], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 0, "k": [295, 207]}, "a": {"a": 0, "k": [295, 207]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}, {"refId": "55", "w": 396, "h": 416, "ind": 57, "ty": 0, "nm": "Path", "sr": 1, "ks": {"p": {"a": 0, "k": [294, 207]}, "a": {"a": 0, "k": [294, 207]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "tt": 1}]}, {"id": "56", "layers": [{"ind": 68, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 0, "k": [-96, 1]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0}, {"ind": 69, "ty": 4, "nm": "Path's gradient fill mesh - Radial gradient: 0:0: rg-foreground", "sr": 1, "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "parent": 68, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [2038.619, 2038.619]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "g": {"p": 2, "k": {"a": 0, "k": [0, 0.827, 0.169, 0.196, 1, 0.576, 0.004, 0.031, 0, 1, 1, 1]}}, "s": {"a": 0, "k": [0, 0]}, "e": {"a": 0, "k": [1, 0]}, "t": 2}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 0, "k": [295, 207]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [29500, 20700]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}, {"ind": 70, "ty": 4, "nm": "Path's gradient fill mesh - Radial gradient: 0:0: rg-background", "sr": 1, "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "parent": 68, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [396, 416]}, "p": {"a": 0, "k": [294, 207]}, "r": {"a": 0, "k": 0}}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "g": {"p": 1, "k": {"a": 0, "k": [0, 0.576, 0.004, 0.031, 0, 1]}}, "s": {"a": 0, "k": [0, 0]}, "e": {"a": 0, "k": [0, 0]}, "t": 2}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}]}], "layers": [{"refId": "0", "w": 393, "h": 40, "ind": 71, "ty": 0, "nm": "Offer.svg 1", "sr": 1, "ks": {"p": {"a": 0, "k": [196.5, 266.5]}, "a": {"a": 0, "k": [196.5, 20]}, "s": {"a": 1, "k": [{"t": 174, "s": [150, 150], "o": {"x": [0.5], "y": [-0.5]}, "i": {"x": [0.2], "y": [1]}}, {"t": 254, "s": [100, 100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 174, "s": [0], "o": {"x": [0.5], "y": [-0.5]}, "i": {"x": [0.2], "y": [1]}}, {"t": 214, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0}, {"ind": 72, "ty": 3, "nm": "White Heading Splash 1", "sr": 1, "ks": {"p": {"a": 0, "k": [196.397, 209.877]}, "a": {"a": 0, "k": [130.547, 26.353]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0}, {"ind": 73, "ty": 4, "nm": "White Heading Splash 1: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "parent": 72, "shapes": [{"ty": "gr", "nm": "White Heading Splash 1", "it": [{"ty": "gr", "nm": "!", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[260.127, 49.629], [259.878, 50.83], [259.204, 51.797], [258.237, 52.456], [257.05, 52.705], [255.849, 52.456], [254.868, 51.797], [254.209, 50.83], [253.974, 49.629], [254.209, 48.428], [254.868, 47.447], [255.849, 46.773], [257.05, 46.524], [258.237, 46.773], [259.204, 47.447], [259.878, 48.428], [260.127, 49.629], [260.127, 49.629]], "i": [[0, 0], [0.166, -0.371], [0.283, -0.273], [0.371, -0.156], [0.42, 0], [0.371, 0.166], [0.283, 0.283], [0.166, 0.372], [0, 0.43], [-0.157, 0.381], [-0.274, 0.273], [-0.371, 0.166], [-0.429, 0], [-0.371, -0.166], [-0.274, -0.284], [-0.167, -0.381], [0, -0.42], [0, 0]], "o": [[0, 0.43], [-0.167, 0.372], [-0.274, 0.283], [-0.371, 0.166], [-0.429, 0], [-0.371, -0.156], [-0.274, -0.273], [-0.157, -0.371], [0, -0.42], [0.166, -0.381], [0.283, -0.284], [0.371, -0.166], [0.42, 0], [0.371, 0.166], [0.283, 0.273], [0.166, 0.381], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-257.505, -41.206]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[261.093, 29.707], [259.892, 45.118], [254.472, 45.118], [253.916, 29.707], [261.093, 29.707], [261.093, 29.707]], "i": [[0, 0], [0.4, -5.137], [1.807, 0], [0.185, 5.137], [-2.392, 0], [0, 0]], "o": [[-0.4, 5.137], [-1.807, 0], [-0.185, -5.137], [2.392, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-257.505, -41.206]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 120, "s": [257.505, 49.206], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 130, "s": [257.505, 41.206], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 120, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 130, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "O", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[244.886, 40.635], [244.696, 39.419], [244.169, 38.365], [243.29, 37.618], [242.103, 37.325], [240.873, 37.574], [239.935, 38.262], [239.349, 39.273], [239.144, 40.489], [239.32, 41.719], [239.847, 42.832], [240.711, 43.638], [241.927, 43.946], [243.158, 43.682], [244.095, 42.95], [244.681, 41.88], [244.886, 40.635], [244.886, 40.635]], "i": [[0, 0], [0.127, 0.391], [0.234, 0.302], [0.351, 0.185], [0.449, 0], [0.361, -0.166], [0.264, -0.293], [0.137, -0.391], [0, -0.43], [-0.117, -0.41], [-0.234, -0.332], [-0.341, -0.205], [-0.459, 0], [-0.362, 0.176], [-0.254, 0.302], [-0.136, 0.401], [0, 0.43], [0, 0]], "o": [[0, -0.42], [-0.117, -0.4], [-0.235, -0.313], [-0.342, -0.196], [-0.459, 0], [-0.362, 0.166], [-0.254, 0.283], [-0.137, 0.381], [0, 0.41], [0.117, 0.41], [0.235, 0.332], [0.352, 0.205], [0.459, 0], [0.371, -0.186], [0.254, -0.313], [0.137, -0.4], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-242.089, -40.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[251.39, 40.254], [251.083, 42.832], [250.189, 45.147], [248.783, 47.139], [246.922, 48.677], [244.681, 49.688], [242.103, 50.039], [239.584, 49.703], [237.357, 48.765], [235.467, 47.3], [234.032, 45.396], [233.109, 43.14], [232.787, 40.635], [233.094, 38.145], [233.973, 35.83], [235.379, 33.838], [237.211, 32.271], [239.422, 31.246], [241.927, 30.879], [245.795, 31.538], [248.783, 33.428], [250.702, 36.387], [251.39, 40.254], [251.39, 40.254]], "i": [[0, 0], [0.205, -0.83], [0.391, -0.723], [0.556, -0.605], [0.694, -0.43], [0.811, -0.244], [0.908, 0], [0.8, 0.224], [0.693, 0.4], [0.567, 0.567], [0.4, 0.693], [0.225, 0.801], [0, 0.869], [-0.205, 0.81], [-0.381, 0.723], [-0.546, 0.606], [-0.674, 0.439], [-0.791, 0.244], [-0.879, 0], [-1.163, -0.439], [-0.821, -0.82], [-0.449, -1.162], [0, -1.425], [0, 0]], "o": [[0, 0.889], [-0.205, 0.821], [-0.381, 0.723], [-0.547, 0.596], [-0.683, 0.43], [-0.81, 0.234], [-0.879, 0], [-0.791, -0.225], [-0.693, -0.41], [-0.556, -0.576], [-0.391, -0.703], [-0.215, -0.801], [0, -0.849], [0.205, -0.82], [0.391, -0.722], [0.547, -0.605], [0.683, -0.44], [0.791, -0.245], [1.416, 0], [1.171, 0.44], [0.83, 0.811], [0.459, 1.152], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-242.089, -40.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 115, "s": [242.089, 48.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 125, "s": [242.089, 40.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 115, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 125, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "L", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[231.096, 43.711], [230.481, 50.215], [218.147, 51.182], [218.616, 30.264], [226.233, 30.264], [225.149, 43.711], [231.096, 43.711], [231.096, 43.711]], "i": [[0, 0], [0.205, -2.168], [4.111, -0.322], [-0.156, 6.973], [-2.539, 0], [0.361, -4.482], [-1.982, 0], [0, 0]], "o": [[-0.205, 2.168], [-4.111, 0.322], [0.156, -6.973], [2.539, 0], [-0.361, 4.482], [1.982, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-224.622, -40.723]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 110, "s": [224.622, 48.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 120, "s": [224.622, 40.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 110, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 120, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "H", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[207.999, 50.889], [200.674, 51.241], [200.616, 45.059], [197.247, 45.059], [197.042, 51.475], [190.479, 51.475], [191.036, 30.879], [198.008, 31.202], [197.569, 41.719], [200.821, 41.719], [200.85, 30.704], [207.53, 30.879], [207.999, 50.889], [207.999, 50.889]], "i": [[0, 0], [2.442, -0.117], [0.019, 2.061], [1.123, 0], [0.068, -2.139], [2.188, 0], [-0.186, 6.865], [-2.324, -0.108], [0.146, -3.506], [-1.084, 0], [-0.01, 3.672], [-2.227, -0.058], [-0.156, -6.67], [0, 0]], "o": [[-2.442, 0.117], [-0.019, -2.061], [-1.123, 0], [-0.068, 2.139], [-2.188, 0], [0.186, -6.865], [2.324, 0.108], [-0.146, 3.506], [1.084, 0], [0.01, -3.672], [2.227, 0.058], [0.156, 6.67], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-199.239, -41.09]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 105, "s": [199.239, 49.09], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 115, "s": [199.239, 41.09], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 105, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 115, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "K", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[189.038, 32.549], [182.563, 40.254], [188.51, 50.332], [181.713, 51.534], [177.26, 45.498], [177.026, 51.241], [170.932, 51.475], [171.401, 30.498], [178.227, 30.879], [177.7, 36.914], [181.948, 30.791], [189.038, 32.549], [189.038, 32.549]], "i": [[0, 0], [2.158, -2.568], [-1.982, -3.359], [2.266, -0.401], [1.484, 2.012], [0.078, -1.914], [2.031, -0.078], [-0.156, 6.992], [-2.275, -0.127], [0.176, -2.012], [-1.416, 2.041], [-2.363, -0.586], [0, 0]], "o": [[-2.158, 2.568], [1.982, 3.359], [-2.266, 0.401], [-1.484, -2.012], [-0.078, 1.914], [-2.031, 0.078], [0.156, -6.992], [2.275, 0.127], [-0.176, 2.012], [1.416, -2.041], [2.363, 0.586], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-179.985, -41.016]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 100, "s": [179.985, 49.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 110, "s": [179.985, 41.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 100, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 110, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "E", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[162.709, 38.877], [162.621, 38.042], [162.328, 37.251], [161.815, 36.68], [161.009, 36.446], [159.969, 36.768], [159.208, 37.559], [158.68, 38.584], [158.373, 39.61], [162.709, 39.2], [162.709, 38.877], [162.709, 38.877]], "i": [[0, 0], [0.058, 0.284], [0.136, 0.235], [0.215, 0.147], [0.323, 0], [0.303, -0.215], [0.214, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.39, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.136, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-160.145, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[168.568, 39.639], [168.392, 42.188], [163.412, 42.715], [158.461, 43.418], [159.647, 45.103], [161.595, 45.733], [162.87, 45.513], [164.261, 44.971], [165.58, 44.253], [166.664, 43.565], [165.961, 50.215], [164.715, 50.962], [163.338, 51.46], [161.888, 51.739], [160.453, 51.827], [157.801, 51.431], [155.619, 50.332], [153.92, 48.648], [152.704, 46.538], [151.971, 44.121], [151.722, 41.543], [151.971, 38.819], [152.718, 36.197], [153.993, 33.868], [155.795, 31.978], [158.109, 30.718], [160.98, 30.264], [163.353, 30.63], [165.272, 31.626], [166.737, 33.135], [167.762, 35.069], [168.363, 37.281], [168.568, 39.639], [168.568, 39.639]], "i": [[0, 0], [0.117, -0.85], [1.65, -0.244], [1.65, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.234, -2.217], [0.45, -0.205], [0.479, -0.127], [0.489, -0.059], [0.468, 0], [0.801, 0.264], [0.654, 0.469], [0.488, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.166, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.684, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.557, -0.429], [-0.41, -0.586], [-0.263, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.651, 0.235], [0.253, 0.703], [0.547, 0.42], [0.391, 0], [0.468, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.234, 2.217], [-0.381, 0.293], [-0.439, 0.205], [-0.478, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.8, -0.264], [-0.645, -0.478], [-0.489, -0.645], [-0.323, -0.761], [-0.166, -0.849], [0, -0.908], [0.166, -0.908], [0.342, -0.84], [0.517, -0.723], [0.683, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.566, 0.42], [0.42, 0.586], [0.274, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-160.145, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 95, "s": [160.145, 49.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 105, "s": [160.145, 41.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 95, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 105, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "D", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[139.837, 45.469], [141.433, 44.942], [142.693, 43.946], [143.499, 42.598], [143.792, 40.987], [143.616, 39.434], [143.045, 38.174], [142.004, 37.325], [140.422, 36.973], [139.837, 45.469], [139.837, 45.469]], "i": [[0, 0], [-0.478, 0.263], [-0.352, 0.39], [-0.186, 0.498], [0, 0.566], [0.117, 0.479], [0.263, 0.352], [0.43, 0.205], [0.625, 0.02], [0.195, -2.832], [0, 0]], "o": [[0.585, -0.088], [0.488, -0.274], [0.352, -0.401], [0.195, -0.508], [0, -0.557], [-0.117, -0.488], [-0.264, -0.361], [-0.429, -0.215], [-0.195, 2.832], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-141.448, -40.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[149.797, 39.844], [149.27, 43.521], [147.791, 46.407], [145.549, 48.531], [142.693, 49.981], [139.382, 50.801], [135.794, 51.065], [134.446, 51.036], [133.098, 50.918], [133.508, 31.846], [136.819, 31.128], [140.217, 30.909], [144.07, 31.495], [147.102, 33.223], [149.08, 36.021], [149.797, 39.844], [149.797, 39.844]], "i": [[0, 0], [0.352, -1.084], [0.634, -0.84], [0.87, -0.586], [1.035, -0.381], [1.172, -0.176], [1.23, 0], [0.439, 0.019], [0.459, 0.049], [-0.137, 6.357], [-1.133, 0.137], [-1.123, 0], [-1.172, -0.391], [-0.849, -0.762], [-0.469, -1.113], [0, -1.445], [0, 0]], "o": [[0, 1.367], [-0.351, 1.084], [-0.625, 0.83], [-0.869, 0.586], [-1.035, 0.371], [-1.162, 0.176], [-0.459, 0], [-0.44, -0.03], [0.137, -6.357], [1.075, -0.342], [1.142, -0.146], [1.397, 0], [1.172, 0.39], [0.85, 0.752], [0.478, 1.103], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-141.448, -40.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 90, "s": [141.448, 48.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 100, "s": [141.448, 40.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 90, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 100, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "R", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[116.753, 37.823], [116.578, 36.695], [116.08, 35.816], [115.259, 35.23], [114.117, 35.01], [113.545, 35.054], [113.003, 35.157], [112.769, 40.606], [113.121, 40.606], [114.395, 40.445], [115.567, 39.947], [116.417, 39.082], [116.753, 37.823], [116.753, 37.823]], "i": [[0, 0], [0.117, 0.342], [0.224, 0.244], [0.332, 0.137], [0.439, 0], [0.186, -0.029], [0.176, -0.049], [0.078, -1.816], [-0.117, 0], [-0.43, 0.107], [-0.342, 0.224], [-0.215, 0.352], [0, 0.488], [0, 0]], "o": [[0, -0.41], [-0.108, -0.342], [-0.215, -0.254], [-0.322, -0.147], [-0.196, 0], [-0.185, 0.02], [-0.078, 1.816], [0.117, 0], [0.42, 0], [0.44, -0.108], [0.352, -0.225], [0.224, -0.351], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-114.674, -40.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[123.316, 37.354], [123.111, 39.434], [122.496, 41.133], [121.397, 42.569], [119.771, 43.829], [123.257, 50.039], [116.666, 51.329], [114.41, 45.088], [112.593, 45.147], [112.33, 51.241], [106.031, 51.241], [106.163, 44.59], [106.324, 37.94], [106.382, 34.6], [106.5, 31.26], [108.521, 30.63], [110.513, 30.249], [112.549, 30.059], [114.673, 30], [117.896, 30.469], [120.665, 31.846], [122.584, 34.146], [123.316, 37.354], [123.316, 37.354]], "i": [[0, 0], [0.137, -0.625], [0.283, -0.517], [0.449, -0.44], [0.635, -0.401], [-1.162, -2.07], [2.197, -0.43], [0.752, 2.08], [0.606, -0.02], [0.088, -2.031], [2.1, 0], [-0.039, 2.207], [-0.059, 2.226], [-0.019, 1.104], [-0.059, 1.123], [-0.664, 0.166], [-0.664, 0.088], [-0.683, 0.029], [-0.722, 0], [-1.035, -0.312], [-0.801, -0.615], [-0.479, -0.918], [0, -1.221], [0, 0]], "o": [[0, 0.762], [-0.127, 0.615], [-0.284, 0.518], [-0.449, 0.439], [1.162, 2.07], [-2.197, 0.43], [-0.752, -2.08], [-0.606, 0.02], [-0.088, 2.031], [-2.1, 0], [0.049, -2.227], [0.048, -2.207], [0.019, -1.123], [0.02, -1.104], [0.683, -0.254], [0.664, -0.166], [0.674, -0.097], [0.694, -0.039], [1.114, 0], [1.045, 0.303], [0.8, 0.615], [0.488, 0.918], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-114.674, -40.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 85, "s": [114.674, 48.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 95, "s": [114.674, 40.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 85, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 95, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "A", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[95.669, 43.36], [94.555, 38.262], [93.471, 43.36], [95.669, 43.36], [95.669, 43.36]], "i": [[0, 0], [0.371, 1.699], [0.361, -1.699], [-0.733, 0], [0, 0]], "o": [[-0.371, -1.699], [-0.361, 1.699], [0.733, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-94.497, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[104.34, 50.215], [97.075, 51.153], [96.196, 47.842], [92.856, 47.842], [92.124, 51.153], [84.653, 50.42], [90.483, 30.909], [98.628, 30.498], [104.34, 50.215], [104.34, 50.215]], "i": [[0, 0], [2.422, -0.313], [0.293, 1.104], [1.113, 0], [0.244, -1.104], [2.49, 0.244], [-1.943, 6.504], [-2.715, 0.137], [-1.904, -6.572], [0, 0]], "o": [[-2.422, 0.313], [-0.293, -1.104], [-1.113, 0], [-0.244, 1.104], [-2.49, -0.244], [1.943, -6.504], [2.715, -0.137], [1.904, 6.572], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-94.497, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 80, "s": [94.497, 48.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 90, "s": [94.497, 40.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 80, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 90, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "A", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[75.389, 43.36], [74.276, 38.262], [73.192, 43.36], [75.389, 43.36], [75.389, 43.36]], "i": [[0, 0], [0.371, 1.699], [0.361, -1.699], [-0.732, 0], [0, 0]], "o": [[-0.371, -1.699], [-0.361, 1.699], [0.732, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-74.218, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[84.061, 50.215], [76.795, 51.153], [75.917, 47.842], [72.577, 47.842], [71.844, 51.153], [64.374, 50.42], [70.204, 30.909], [78.348, 30.498], [84.061, 50.215], [84.061, 50.215]], "i": [[0, 0], [2.422, -0.313], [0.293, 1.104], [1.113, 0], [0.244, -1.104], [2.49, 0.244], [-1.943, 6.504], [-2.715, 0.137], [-1.904, -6.572], [0, 0]], "o": [[-2.422, 0.313], [-0.293, -1.104], [-1.113, 0], [-0.244, 1.104], [-2.49, -0.244], [1.943, -6.504], [2.715, -0.137], [1.904, 6.572], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-74.218, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 75, "s": [74.218, 48.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 85, "s": [74.218, 40.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 75, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 85, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "B", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[53.777, 42.686], [52.957, 42.774], [52.898, 46.114], [53.323, 46.202], [53.748, 46.231], [54.421, 46.172], [55.11, 45.909], [55.637, 45.367], [55.857, 44.473], [55.637, 43.565], [55.125, 43.023], [54.451, 42.759], [53.777, 42.686], [53.777, 42.686]], "i": [[0, 0], [0.263, -0.059], [0.02, -1.113], [-0.137, -0.03], [-0.137, 0], [-0.244, 0.039], [-0.215, 0.127], [-0.136, 0.224], [0, 0.371], [0.147, 0.234], [0.205, 0.127], [0.244, 0.039], [0.205, 0], [0, 0]], "o": [[-0.283, 0], [-0.02, 1.113], [0.147, 0.029], [0.146, 0.019], [0.205, 0], [0.245, -0.049], [0.215, -0.137], [0.147, -0.225], [0, -0.371], [-0.136, -0.235], [-0.205, -0.137], [-0.244, -0.049], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-54.788, -40.973]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[55.857, 37.911], [55.681, 37.193], [55.242, 36.695], [54.685, 36.416], [54.158, 36.329], [53.587, 36.416], [53.045, 36.621], [52.957, 39.873], [53.792, 39.815], [54.744, 39.566], [55.535, 38.98], [55.857, 37.911], [55.857, 37.911]], "i": [[0, 0], [0.117, 0.195], [0.176, 0.127], [0.205, 0.059], [0.156, 0], [0.185, -0.058], [0.175, -0.078], [0.029, -1.084], [-0.332, 0.039], [-0.303, 0.127], [-0.215, 0.254], [0, 0.449], [0, 0]], "o": [[0, -0.284], [-0.117, -0.205], [-0.166, -0.127], [-0.195, -0.058], [-0.196, 0], [-0.186, 0.059], [-0.029, 1.084], [0.224, 0], [0.332, -0.039], [0.312, -0.137], [0.215, -0.264], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-54.788, -40.973]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[63.298, 46.26], [62.844, 48.355], [61.658, 49.849], [59.929, 50.86], [57.908, 51.46], [55.784, 51.753], [53.806, 51.827], [51.917, 51.753], [49.983, 51.534], [48.079, 51.109], [46.306, 50.42], [46.277, 31.553], [48.02, 30.953], [49.895, 30.498], [51.814, 30.22], [53.66, 30.118], [55.754, 30.249], [57.805, 30.689], [59.636, 31.495], [61.116, 32.696], [62.112, 34.366], [62.478, 36.563], [62.244, 38.101], [61.57, 39.39], [60.501, 40.371], [59.08, 40.957], [60.808, 41.69], [62.141, 42.862], [62.991, 44.414], [63.298, 46.26], [63.298, 46.26]], "i": [[0, 0], [0.303, -0.586], [0.498, -0.41], [0.655, -0.264], [0.703, -0.146], [0.713, -0.049], [0.615, 0], [0.654, 0.049], [0.645, 0.107], [0.625, 0.176], [0.557, 0.274], [0.01, 6.289], [-0.615, 0.175], [-0.635, 0.118], [-0.635, 0.059], [-0.596, 0], [-0.703, -0.087], [-0.654, -0.205], [-0.566, -0.332], [-0.42, -0.479], [-0.234, -0.635], [0, -0.83], [0.156, -0.479], [0.293, -0.381], [0.419, -0.273], [0.527, -0.127], [-0.517, -0.322], [-0.361, -0.459], [-0.196, -0.576], [0, -0.654], [0, 0]], "o": [[0, 0.811], [-0.293, 0.586], [-0.498, 0.41], [-0.644, 0.254], [-0.703, 0.147], [-0.703, 0.049], [-0.605, 0], [-0.645, -0.039], [-0.645, -0.108], [-0.625, -0.186], [-0.01, -6.289], [0.547, -0.224], [0.615, -0.186], [0.645, -0.127], [0.635, -0.068], [0.693, 0], [0.713, 0.088], [0.655, 0.205], [0.567, 0.322], [0.429, 0.478], [0.244, 0.634], [0, 0.547], [-0.157, 0.478], [-0.293, 0.381], [-0.42, 0.264], [0.634, 0.166], [0.528, 0.322], [0.371, 0.459], [0.205, 0.577], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-54.788, -40.973]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 70, "s": [54.788, 48.973], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 80, "s": [54.788, 40.973], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 70, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 80, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "K", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[37.315, 32.549], [30.841, 40.254], [36.788, 50.332], [29.991, 51.534], [25.538, 45.498], [25.304, 51.241], [19.21, 51.475], [19.679, 30.498], [26.505, 30.879], [25.978, 36.914], [30.226, 30.791], [37.315, 32.549], [37.315, 32.549]], "i": [[0, 0], [2.158, -2.568], [-1.983, -3.359], [2.266, -0.401], [1.484, 2.012], [0.078, -1.914], [2.031, -0.078], [-0.156, 6.992], [-2.275, -0.127], [0.176, -2.012], [-1.416, 2.041], [-2.363, -0.586], [0, 0]], "o": [[-2.158, 2.568], [1.983, 3.359], [-2.266, 0.401], [-1.484, -2.012], [-0.078, 1.914], [-2.031, 0.078], [0.156, -6.992], [2.275, 0.127], [-0.176, 2.012], [1.416, -2.041], [2.363, 0.586], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-28.263, -41.016]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 65, "s": [28.263, 49.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 75, "s": [28.263, 41.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 65, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 75, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "E", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[10.986, 38.877], [10.898, 38.042], [10.606, 37.251], [10.093, 36.68], [9.287, 36.446], [8.247, 36.768], [7.485, 37.559], [6.958, 38.584], [6.65, 39.61], [10.986, 39.2], [10.986, 38.877], [10.986, 38.877]], "i": [[0, 0], [0.059, 0.284], [0.137, 0.235], [0.215, 0.147], [0.322, 0], [0.303, -0.215], [0.215, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.391, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.137, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-8.423, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[16.846, 39.639], [16.67, 42.188], [11.689, 42.715], [6.738, 43.418], [7.925, 45.103], [9.873, 45.733], [11.147, 45.513], [12.539, 44.971], [13.857, 44.253], [14.941, 43.565], [14.238, 50.215], [12.993, 50.962], [11.616, 51.46], [10.166, 51.739], [8.731, 51.827], [6.079, 51.431], [3.897, 50.332], [2.197, 48.648], [0.981, 46.538], [0.249, 44.121], [0, 41.543], [0.249, 38.819], [0.996, 36.197], [2.27, 33.868], [4.072, 31.978], [6.387, 30.718], [9.258, 30.264], [11.631, 30.63], [13.55, 31.626], [15.015, 33.135], [16.04, 35.069], [16.641, 37.281], [16.846, 39.639], [16.846, 39.639]], "i": [[0, 0], [0.117, -0.85], [1.65, -0.244], [1.65, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.234, -2.217], [0.449, -0.205], [0.478, -0.127], [0.488, -0.059], [0.469, 0], [0.801, 0.264], [0.654, 0.469], [0.488, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.166, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.684, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.557, -0.429], [-0.41, -0.586], [-0.264, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.65, 0.235], [0.254, 0.703], [0.547, 0.42], [0.391, 0], [0.469, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.234, 2.217], [-0.381, 0.293], [-0.439, 0.205], [-0.479, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.801, -0.264], [-0.645, -0.478], [-0.488, -0.645], [-0.322, -0.761], [-0.166, -0.849], [0, -0.908], [0.166, -0.908], [0.342, -0.84], [0.518, -0.723], [0.683, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.566, 0.42], [0.42, 0.586], [0.273, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-8.423, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 60, "s": [8.423, 49.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 70, "s": [8.423, 41.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 60, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 70, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "W", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[245.692, 1.143], [242.264, 20.86], [233.944, 21.036], [232.157, 11.104], [230.37, 20.274], [222.049, 20.567], [218.592, 1.729], [225.858, 1.084], [227.323, 12.657], [229.286, 1.963], [234.999, 1.963], [237.284, 12.803], [238.456, 0.498], [245.692, 1.143], [245.692, 1.143]], "i": [[0, 0], [1.143, -6.572], [2.773, -0.059], [0.596, 3.311], [0.596, -3.057], [2.774, -0.098], [1.152, 6.279], [-2.422, 0.215], [-0.488, -3.858], [-0.654, 3.565], [-1.904, 0], [-0.762, -3.613], [-0.391, 4.102], [-2.412, -0.215], [0, 0]], "o": [[-1.143, 6.572], [-2.773, 0.059], [-0.596, -3.311], [-0.596, 3.057], [-2.774, 0.098], [-1.152, -6.279], [2.422, -0.215], [0.488, 3.858], [0.654, -3.565], [1.904, 0], [0.762, 3.613], [0.391, -4.102], [2.412, 0.215], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-232.142, -10.767]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 55, "s": [232.142, 18.767], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 65, "s": [232.142, 10.767], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 55, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 65, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "O", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[210.618, 10.635], [210.427, 9.419], [209.9, 8.365], [209.021, 7.618], [207.835, 7.325], [206.604, 7.574], [205.667, 8.262], [205.081, 9.273], [204.876, 10.489], [205.051, 11.719], [205.579, 12.832], [206.443, 13.638], [207.659, 13.946], [208.889, 13.682], [209.827, 12.95], [210.413, 11.88], [210.618, 10.635], [210.618, 10.635]], "i": [[0, 0], [0.127, 0.391], [0.234, 0.302], [0.352, 0.185], [0.449, 0], [0.361, -0.166], [0.263, -0.293], [0.136, -0.391], [0, -0.43], [-0.117, -0.41], [-0.235, -0.332], [-0.342, -0.205], [-0.459, 0], [-0.361, 0.176], [-0.254, 0.302], [-0.137, 0.401], [0, 0.43], [0, 0]], "o": [[0, -0.42], [-0.117, -0.4], [-0.234, -0.313], [-0.342, -0.196], [-0.459, 0], [-0.361, 0.166], [-0.254, 0.283], [-0.137, 0.381], [0, 0.41], [0.118, 0.41], [0.234, 0.332], [0.352, 0.205], [0.459, 0], [0.371, -0.186], [0.254, -0.313], [0.136, -0.4], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-207.82, -10.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[217.122, 10.254], [216.814, 12.832], [215.92, 15.147], [214.514, 17.139], [212.654, 18.677], [210.413, 19.688], [207.835, 20.039], [205.315, 19.703], [203.088, 18.765], [201.199, 17.3], [199.763, 15.396], [198.84, 13.14], [198.518, 10.635], [198.826, 8.145], [199.705, 5.83], [201.111, 3.838], [202.942, 2.271], [205.154, 1.246], [207.659, 0.879], [211.526, 1.538], [214.514, 3.428], [216.433, 6.387], [217.122, 10.254], [217.122, 10.254]], "i": [[0, 0], [0.205, -0.83], [0.391, -0.723], [0.557, -0.605], [0.693, -0.43], [0.81, -0.244], [0.908, 0], [0.801, 0.224], [0.694, 0.4], [0.566, 0.567], [0.401, 0.693], [0.225, 0.801], [0, 0.869], [-0.205, 0.81], [-0.381, 0.723], [-0.547, 0.606], [-0.674, 0.439], [-0.791, 0.244], [-0.879, 0], [-1.162, -0.439], [-0.82, -0.82], [-0.449, -1.162], [0, -1.425], [0, 0]], "o": [[0, 0.889], [-0.205, 0.821], [-0.38, 0.723], [-0.547, 0.596], [-0.684, 0.43], [-0.811, 0.234], [-0.879, 0], [-0.791, -0.225], [-0.693, -0.41], [-0.557, -0.576], [-0.39, -0.703], [-0.214, -0.801], [0, -0.849], [0.205, -0.82], [0.39, -0.722], [0.547, -0.605], [0.684, -0.44], [0.791, -0.245], [1.416, 0], [1.172, 0.44], [0.83, 0.811], [0.459, 1.152], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-207.82, -10.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 50, "s": [207.82, 18.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 60, "s": [207.82, 10.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 50, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 60, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "L", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[196.828, 13.711], [196.212, 20.215], [183.878, 21.182], [184.347, 0.264], [191.964, 0.264], [190.88, 13.711], [196.828, 13.711], [196.828, 13.711]], "i": [[0, 0], [0.205, -2.168], [4.111, -0.322], [-0.156, 6.973], [-2.539, 0], [0.361, -4.482], [-1.983, 0], [0, 0]], "o": [[-0.205, 2.168], [-4.111, 0.322], [0.156, -6.973], [2.539, 0], [-0.361, 4.482], [1.983, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-190.353, -10.723]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 45, "s": [190.353, 18.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 55, "s": [190.353, 10.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 45, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 55, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "e", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[168.295, 8.877], [168.208, 8.042], [167.915, 7.251], [167.402, 6.68], [166.596, 6.446], [165.556, 6.768], [164.795, 7.559], [164.267, 8.584], [163.96, 9.61], [168.295, 9.2], [168.295, 8.877], [168.295, 8.877]], "i": [[0, 0], [0.058, 0.284], [0.136, 0.235], [0.215, 0.147], [0.323, 0], [0.303, -0.215], [0.214, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.39, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.137, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-165.732, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[174.155, 9.639], [173.979, 12.188], [168.999, 12.715], [164.047, 13.418], [165.234, 15.103], [167.182, 15.733], [168.457, 15.513], [169.848, 14.971], [171.167, 14.253], [172.251, 13.565], [171.547, 20.215], [170.302, 20.962], [168.925, 21.46], [167.475, 21.739], [166.04, 21.827], [163.388, 21.431], [161.206, 20.332], [159.506, 18.648], [158.291, 16.538], [157.558, 14.121], [157.309, 11.543], [157.558, 8.819], [158.305, 6.197], [159.58, 3.868], [161.381, 1.978], [163.696, 0.718], [166.567, 0.264], [168.94, 0.63], [170.859, 1.626], [172.324, 3.135], [173.349, 5.069], [173.95, 7.281], [174.155, 9.639], [174.155, 9.639]], "i": [[0, 0], [0.117, -0.85], [1.65, -0.244], [1.651, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.235, -2.217], [0.45, -0.205], [0.479, -0.127], [0.488, -0.059], [0.468, 0], [0.801, 0.264], [0.654, 0.469], [0.489, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.166, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.683, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.557, -0.429], [-0.41, -0.586], [-0.263, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.651, 0.235], [0.254, 0.703], [0.547, 0.42], [0.391, 0], [0.468, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.235, 2.217], [-0.38, 0.293], [-0.439, 0.205], [-0.478, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.801, -0.264], [-0.645, -0.478], [-0.488, -0.645], [-0.323, -0.761], [-0.166, -0.849], [0, -0.908], [0.166, -0.908], [0.342, -0.84], [0.517, -0.723], [0.684, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.566, 0.42], [0.42, 0.586], [0.274, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-165.732, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 40, "s": [165.732, 19.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 50, "s": [165.732, 11.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 40, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 50, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "N", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[154.916, 14.063], [154.872, 17.344], [154.769, 20.596], [146.332, 20.45], [147.093, 15.279], [147.416, 10.049], [147.401, 9.434], [147.342, 8.482], [147.211, 7.383], [146.991, 6.343], [146.654, 5.581], [146.185, 5.274], [145.336, 5.567], [144.764, 6.329], [144.413, 7.354], [144.208, 8.496], [144.12, 9.58], [144.105, 10.401], [143.988, 15.557], [143.871, 20.713], [136.4, 21.036], [136.957, 10.752], [137.396, 0.469], [144.31, 0.118], [144.252, 2.461], [145.13, 1.524], [146.156, 0.777], [147.298, 0.279], [148.587, 0.088], [150.77, 0.498], [152.381, 1.612], [153.509, 3.282], [154.242, 5.318], [154.667, 7.588], [154.857, 9.903], [154.916, 12.115], [154.916, 14.063], [154.916, 14.063]], "i": [[0, 0], [0.029, -1.084], [0.049, -1.094], [2.812, 0.049], [-0.215, 1.718], [0, 1.758], [0.01, 0.283], [0.03, 0.351], [0.058, 0.371], [0.097, 0.313], [0.137, 0.196], [0.186, 0], [0.234, -0.196], [0.156, -0.313], [0.088, -0.381], [0.048, -0.38], [0.019, -0.341], [0, -0.215], [0.029, -1.709], [0.049, -1.738], [2.49, -0.108], [-0.176, 3.409], [-0.107, 3.447], [-2.305, 0.117], [0.019, -0.781], [-0.312, 0.293], [-0.361, 0.205], [-0.4, 0.117], [-0.449, 0], [-0.625, -0.273], [-0.449, -0.469], [-0.302, -0.645], [-0.186, -0.723], [-0.098, -0.791], [-0.029, -0.762], [0, -0.713], [0, -0.586], [0, 0]], "o": [[0, 1.103], [-0.02, 1.074], [-2.812, -0.049], [0.293, -1.729], [0.215, -1.729], [0, -0.127], [-0.01, -0.283], [-0.029, -0.361], [-0.049, -0.381], [-0.088, -0.312], [-0.127, -0.205], [-0.332, 0], [-0.225, 0.195], [-0.146, 0.302], [-0.088, 0.381], [-0.039, 0.381], [-0.01, 0.333], [-0.049, 1.728], [-0.029, 1.699], [-2.49, 0.108], [0.195, -3.448], [0.185, -3.408], [2.305, -0.117], [-0.019, 0.781], [0.273, -0.332], [0.323, -0.293], [0.361, -0.215], [0.411, -0.127], [0.831, 0], [0.625, 0.274], [0.45, 0.468], [0.303, 0.634], [0.185, 0.722], [0.097, 0.782], [0.039, 0.761], [0, 0.713], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-145.658, -10.562]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 35, "s": [145.658, 18.562], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 45, "s": [145.658, 10.562], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 35, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 45, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "T", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[134.578, 0.821], [134.431, 7.032], [130.066, 7.207], [129.539, 20.801], [123.357, 21.123], [122.8, 7.53], [118.435, 7.764], [118.611, 0.85], [134.578, 0.821], [134.578, 0.821]], "i": [[0, 0], [0.049, -2.07], [1.455, -0.058], [0.176, -4.531], [2.061, -0.107], [0.186, 4.531], [1.455, -0.078], [-0.059, 2.305], [-5.322, 0.01], [0, 0]], "o": [[-0.049, 2.07], [-1.455, 0.058], [-0.176, 4.531], [-2.061, 0.107], [-0.186, -4.531], [-1.455, 0.078], [0.059, -2.305], [5.322, -0.01], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-126.507, -10.972]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 30, "s": [126.507, 18.972], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 40, "s": [126.507, 10.972], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 30, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 40, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "I", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[116.364, 0.85], [115.338, 20.801], [109.157, 21.123], [108.571, 1.26], [116.364, 0.85], [116.364, 0.85]], "i": [[0, 0], [0.342, -6.65], [2.06, -0.107], [0.195, 6.621], [-2.598, 0.137], [0, 0]], "o": [[-0.342, 6.65], [-2.06, 0.107], [-0.195, -6.621], [2.598, -0.137], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-112.468, -10.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 25, "s": [112.468, 18.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 35, "s": [112.468, 10.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 25, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 35, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "e", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[93.032, 8.877], [92.944, 8.042], [92.651, 7.251], [92.138, 6.68], [91.333, 6.446], [90.293, 6.768], [89.531, 7.559], [89.003, 8.584], [88.696, 9.61], [93.032, 9.2], [93.032, 8.877], [93.032, 8.877]], "i": [[0, 0], [0.059, 0.284], [0.137, 0.235], [0.215, 0.147], [0.322, 0], [0.302, -0.215], [0.215, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.391, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.136, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-90.468, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[98.891, 9.639], [98.715, 12.188], [93.735, 12.715], [88.784, 13.418], [89.97, 15.103], [91.919, 15.733], [93.193, 15.513], [94.585, 14.971], [95.903, 14.253], [96.987, 13.565], [96.284, 20.215], [95.039, 20.962], [93.662, 21.46], [92.211, 21.739], [90.776, 21.827], [88.125, 21.431], [85.942, 20.332], [84.243, 18.648], [83.027, 16.538], [82.295, 14.121], [82.045, 11.543], [82.295, 8.819], [83.042, 6.197], [84.316, 3.868], [86.118, 1.978], [88.432, 0.718], [91.303, 0.264], [93.676, 0.63], [95.595, 1.626], [97.06, 3.135], [98.086, 5.069], [98.686, 7.281], [98.891, 9.639], [98.891, 9.639]], "i": [[0, 0], [0.118, -0.85], [1.65, -0.244], [1.65, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.234, -2.217], [0.449, -0.205], [0.478, -0.127], [0.489, -0.059], [0.469, 0], [0.8, 0.264], [0.654, 0.469], [0.488, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.167, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.684, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.556, -0.429], [-0.41, -0.586], [-0.264, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.65, 0.235], [0.254, 0.703], [0.547, 0.42], [0.39, 0], [0.469, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.234, 2.217], [-0.381, 0.293], [-0.44, 0.205], [-0.479, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.801, -0.264], [-0.645, -0.478], [-0.489, -0.645], [-0.322, -0.761], [-0.167, -0.849], [0, -0.908], [0.166, -0.908], [0.341, -0.84], [0.518, -0.723], [0.683, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.567, 0.42], [0.42, 0.586], [0.273, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-90.468, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 20, "s": [90.468, 19.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 30, "s": [90.468, 11.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 20, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 30, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "C", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[79.637, 1.465], [79.11, 7.647], [78.275, 7.486], [77.44, 7.442], [75.448, 7.705], [73.778, 8.511], [72.621, 9.888], [72.196, 11.836], [72.43, 13.272], [73.119, 14.283], [74.203, 14.883], [75.624, 15.088], [76.737, 14.986], [77.879, 14.678], [78.978, 14.239], [79.96, 13.711], [79.725, 20.596], [78.495, 21.123], [77.132, 21.504], [75.741, 21.768], [74.422, 21.856], [70.628, 21.079], [67.699, 18.97], [65.809, 15.85], [65.135, 12.071], [65.809, 7.588], [67.816, 3.985], [71.097, 1.582], [75.594, 0.704], [77.645, 0.865], [79.637, 1.465], [79.637, 1.465]], "i": [[0, 0], [0.176, -2.061], [0.273, 0.029], [0.283, 0], [0.635, -0.175], [0.488, -0.361], [0.293, -0.557], [0, -0.752], [-0.156, -0.41], [-0.293, -0.274], [-0.42, -0.137], [-0.518, 0], [-0.381, 0.068], [-0.38, 0.127], [-0.351, 0.166], [-0.303, 0.186], [0.078, -2.295], [0.439, -0.156], [0.469, -0.107], [0.469, -0.059], [0.42, 0], [1.143, 0.518], [0.82, 0.889], [0.449, 1.182], [0, 1.338], [-0.449, 1.387], [-0.879, 1.015], [-1.299, 0.577], [-1.689, 0], [-0.693, -0.108], [-0.634, -0.293], [0, 0]], "o": [[-0.176, 2.061], [-0.283, -0.078], [-0.273, -0.03], [-0.693, 0], [-0.625, 0.176], [-0.479, 0.361], [-0.284, 0.547], [0, 0.547], [0.166, 0.4], [0.302, 0.263], [0.429, 0.137], [0.361, 0], [0.381, -0.078], [0.381, -0.127], [0.352, -0.166], [-0.078, 2.295], [-0.381, 0.195], [-0.44, 0.147], [-0.459, 0.117], [-0.459, 0.059], [-1.386, 0], [-1.132, -0.517], [-0.811, -0.898], [-0.449, -1.182], [0, -1.602], [0.459, -1.386], [0.889, -1.026], [1.309, -0.586], [0.674, 0], [0.693, 0.107], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-72.548, -11.28]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 15, "s": [72.548, 19.28], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 25, "s": [72.548, 11.28], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 15, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 25, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "I", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[62.961, 0.85], [61.936, 20.801], [55.754, 21.123], [55.169, 1.26], [62.961, 0.85], [62.961, 0.85]], "i": [[0, 0], [0.342, -6.65], [2.061, -0.107], [0.195, 6.621], [-2.597, 0.137], [0, 0]], "o": [[-0.342, 6.65], [-2.061, 0.107], [-0.195, -6.621], [2.597, -0.137], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-59.065, -10.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 10, "s": [59.065, 18.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 20, "s": [59.065, 10.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 10, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 20, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "R", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[46.227, 7.823], [46.051, 6.695], [45.553, 5.816], [44.733, 5.23], [43.59, 5.01], [43.019, 5.054], [42.477, 5.157], [42.243, 10.606], [42.594, 10.606], [43.869, 10.445], [45.041, 9.947], [45.89, 9.082], [46.227, 7.823], [46.227, 7.823]], "i": [[0, 0], [0.118, 0.342], [0.225, 0.244], [0.332, 0.137], [0.44, 0], [0.186, -0.029], [0.176, -0.049], [0.078, -1.816], [-0.117, 0], [-0.43, 0.107], [-0.342, 0.224], [-0.215, 0.352], [0, 0.488], [0, 0]], "o": [[0, -0.41], [-0.107, -0.342], [-0.215, -0.254], [-0.322, -0.147], [-0.195, 0], [-0.185, 0.02], [-0.078, 1.816], [0.117, 0], [0.42, 0], [0.439, -0.108], [0.351, -0.225], [0.225, -0.351], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-44.147, -10.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[52.79, 7.354], [52.585, 9.434], [51.969, 11.133], [50.871, 12.569], [49.245, 13.829], [52.731, 20.039], [46.139, 21.329], [43.883, 15.088], [42.067, 15.147], [41.803, 21.241], [35.504, 21.241], [35.636, 14.59], [35.797, 7.94], [35.856, 4.6], [35.973, 1.26], [37.995, 0.63], [39.987, 0.249], [42.023, 0.059], [44.147, 0], [47.37, 0.469], [50.138, 1.846], [52.057, 4.146], [52.79, 7.354], [52.79, 7.354]], "i": [[0, 0], [0.136, -0.625], [0.284, -0.517], [0.449, -0.44], [0.634, -0.401], [-1.162, -2.07], [2.197, -0.43], [0.752, 2.08], [0.605, -0.02], [0.088, -2.031], [2.1, 0], [-0.039, 2.207], [-0.058, 2.226], [-0.019, 1.104], [-0.058, 1.123], [-0.664, 0.166], [-0.664, 0.088], [-0.684, 0.029], [-0.723, 0], [-1.035, -0.312], [-0.801, -0.615], [-0.478, -0.918], [0, -1.221], [0, 0]], "o": [[0, 0.762], [-0.127, 0.615], [-0.283, 0.518], [-0.45, 0.439], [1.162, 2.07], [-2.197, 0.43], [-0.752, -2.08], [-0.605, 0.02], [-0.088, 2.031], [-2.1, 0], [0.049, -2.227], [0.049, -2.207], [0.02, -1.123], [0.02, -1.104], [0.684, -0.254], [0.664, -0.166], [0.674, -0.097], [0.693, -0.039], [1.113, 0], [1.045, 0.303], [0.801, 0.615], [0.488, 0.918], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-44.147, -10.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 5, "s": [44.147, 18.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 15, "s": [44.147, 10.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 5, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 15, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "P", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[26.813, 7.881], [26.227, 6.402], [24.732, 5.86], [24.088, 5.918], [23.473, 6.036], [23.297, 10.313], [23.692, 10.342], [24.088, 10.342], [25.099, 10.166], [25.978, 9.668], [26.578, 8.892], [26.813, 7.881], [26.813, 7.881]], "i": [[0, 0], [0.391, 0.351], [0.615, 0], [0.225, -0.039], [0.195, -0.049], [0.059, -1.426], [-0.127, 0], [-0.137, 0], [-0.322, 0.118], [-0.254, 0.215], [-0.147, 0.293], [0, 0.371], [0, 0]], "o": [[0, -0.635], [-0.381, -0.362], [-0.205, 0], [-0.215, 0.03], [-0.059, 1.426], [0.137, 0.019], [0.127, 0], [0.352, 0], [0.332, -0.117], [0.254, -0.224], [0.156, -0.303], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-24.806, -10.84]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[33.316, 7.383], [32.892, 9.903], [31.72, 11.822], [29.991, 13.213], [27.853, 14.151], [25.509, 14.678], [23.121, 14.854], [23.121, 21.68], [16.295, 21.68], [16.31, 15], [16.353, 8.291], [16.368, 4.717], [16.441, 1.114], [20.191, 0.279], [24.059, 0], [26.314, 0.191], [28.468, 0.762], [30.387, 1.758], [31.925, 3.164], [32.936, 5.039], [33.316, 7.383], [33.316, 7.383]], "i": [[0, 0], [0.283, -0.733], [0.498, -0.547], [0.664, -0.381], [0.762, -0.244], [0.811, -0.117], [0.781, -0.01], [0, -2.275], [2.275, 0], [-0.01, 2.217], [-0.019, 2.256], [0.01, 1.192], [-0.059, 1.211], [-1.25, 0.185], [-1.328, 0], [-0.752, -0.127], [-0.684, -0.264], [-0.586, -0.4], [-0.43, -0.546], [-0.244, -0.703], [0, -0.869], [0, 0]], "o": [[0, 0.947], [-0.283, 0.732], [-0.488, 0.547], [-0.664, 0.381], [-0.752, 0.234], [-0.811, 0.108], [0, 2.275], [-2.275, 0], [0, -2.236], [0.01, -2.216], [0.02, -1.191], [-0.01, -1.191], [1.25, -0.371], [1.25, -0.186], [0.752, 0], [0.752, 0.117], [0.693, 0.264], [0.596, 0.391], [0.43, 0.547], [0.254, 0.694], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-24.806, -10.84]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 0, "s": [24.806, 20.84], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 10, "s": [24.806, 10.84], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 10, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.396, 0.349, 0.71]}, "o": {"a": 0, "k": 100}, "r": 1, "hd": true}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}, {"ind": 74, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 120, "s": [257.505, 49.206], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 130, "s": [257.505, 41.206], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "1", "w": 8, "h": 24, "ind": 75, "ty": 0, "nm": "!", "sr": 1, "ks": {"p": {"a": 0, "k": [-4, -12]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 120, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 130, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 74}, {"ind": 76, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 115, "s": [242.089, 48.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 125, "s": [242.089, 40.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "2", "w": 20, "h": 20, "ind": 77, "ty": 0, "nm": "O", "sr": 1, "ks": {"p": {"a": 0, "k": [-10, -10]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 115, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 125, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 76}, {"ind": 78, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 110, "s": [224.622, 48.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 120, "s": [224.622, 40.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "3", "w": 14, "h": 22, "ind": 79, "ty": 0, "nm": "L", "sr": 1, "ks": {"p": {"a": 0, "k": [-7, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 110, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 120, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 78}, {"ind": 80, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 105, "s": [199.239, 49.09], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 115, "s": [199.239, 41.09], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "4", "w": 18, "h": 22, "ind": 81, "ty": 0, "nm": "H", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 105, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 115, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 80}, {"ind": 82, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 100, "s": [179.985, 49.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 110, "s": [179.985, 41.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "5", "w": 20, "h": 22, "ind": 83, "ty": 0, "nm": "K", "sr": 1, "ks": {"p": {"a": 0, "k": [-10, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 100, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 110, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 82}, {"ind": 84, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 95, "s": [160.145, 49.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 105, "s": [160.145, 41.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "6", "w": 18, "h": 22, "ind": 85, "ty": 0, "nm": "E", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 95, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 105, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 84}, {"ind": 86, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 90, "s": [141.448, 48.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 100, "s": [141.448, 40.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "7", "w": 18, "h": 22, "ind": 87, "ty": 0, "nm": "D", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 90, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 100, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 86}, {"ind": 88, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 85, "s": [114.674, 48.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 95, "s": [114.674, 40.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "8", "w": 18, "h": 22, "ind": 89, "ty": 0, "nm": "R", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 85, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 95, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 88}, {"ind": 90, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 80, "s": [94.497, 48.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 90, "s": [94.497, 40.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "9", "w": 20, "h": 22, "ind": 91, "ty": 0, "nm": "A", "sr": 1, "ks": {"p": {"a": 0, "k": [-10, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 80, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 90, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 90}, {"ind": 92, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 75, "s": [74.218, 48.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 85, "s": [74.218, 40.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "10", "w": 20, "h": 22, "ind": 93, "ty": 0, "nm": "A", "sr": 1, "ks": {"p": {"a": 0, "k": [-10, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 75, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 85, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 92}, {"ind": 94, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 70, "s": [54.788, 48.973], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 80, "s": [54.788, 40.973], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "11", "w": 18, "h": 22, "ind": 95, "ty": 0, "nm": "B", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 70, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 80, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 94}, {"ind": 96, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 65, "s": [28.263, 49.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 75, "s": [28.263, 41.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "12", "w": 20, "h": 22, "ind": 97, "ty": 0, "nm": "K", "sr": 1, "ks": {"p": {"a": 0, "k": [-10, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 65, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 75, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 96}, {"ind": 98, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 60, "s": [8.423, 49.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 70, "s": [8.423, 41.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "13", "w": 18, "h": 22, "ind": 99, "ty": 0, "nm": "E", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 60, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 70, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 98}, {"ind": 100, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 55, "s": [232.142, 18.767], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 65, "s": [232.142, 10.767], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "14", "w": 28, "h": 22, "ind": 101, "ty": 0, "nm": "W", "sr": 1, "ks": {"p": {"a": 0, "k": [-14, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 55, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 65, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 100}, {"ind": 102, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 50, "s": [207.82, 18.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 60, "s": [207.82, 10.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "15", "w": 20, "h": 20, "ind": 103, "ty": 0, "nm": "O", "sr": 1, "ks": {"p": {"a": 0, "k": [-10, -10]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 50, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 60, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 102}, {"ind": 104, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 45, "s": [190.353, 18.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 55, "s": [190.353, 10.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "16", "w": 14, "h": 22, "ind": 105, "ty": 0, "nm": "L", "sr": 1, "ks": {"p": {"a": 0, "k": [-7, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 45, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 55, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 104}, {"ind": 106, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 40, "s": [165.732, 19.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 50, "s": [165.732, 11.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "17", "w": 18, "h": 22, "ind": 107, "ty": 0, "nm": "e", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 40, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 50, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 106}, {"ind": 108, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 35, "s": [145.658, 18.562], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 45, "s": [145.658, 10.562], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "18", "w": 20, "h": 22, "ind": 109, "ty": 0, "nm": "N", "sr": 1, "ks": {"p": {"a": 0, "k": [-10, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 35, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 45, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 108}, {"ind": 110, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 30, "s": [126.507, 18.972], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 40, "s": [126.507, 10.972], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "19", "w": 18, "h": 22, "ind": 111, "ty": 0, "nm": "T", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 30, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 40, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 110}, {"ind": 112, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 25, "s": [112.468, 18.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 35, "s": [112.468, 10.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "20", "w": 8, "h": 22, "ind": 113, "ty": 0, "nm": "I", "sr": 1, "ks": {"p": {"a": 0, "k": [-4, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 25, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 35, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 112}, {"ind": 114, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 20, "s": [90.468, 19.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 30, "s": [90.468, 11.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "21", "w": 18, "h": 22, "ind": 115, "ty": 0, "nm": "e", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 20, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 30, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 114}, {"ind": 116, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 15, "s": [72.548, 19.28], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 25, "s": [72.548, 11.28], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "22", "w": 16, "h": 22, "ind": 117, "ty": 0, "nm": "C", "sr": 1, "ks": {"p": {"a": 0, "k": [-8, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 15, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 25, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 116}, {"ind": 118, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 10, "s": [59.065, 18.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 20, "s": [59.065, 10.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "23", "w": 8, "h": 22, "ind": 119, "ty": 0, "nm": "I", "sr": 1, "ks": {"p": {"a": 0, "k": [-4, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 10, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 20, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 118}, {"ind": 120, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 5, "s": [44.147, 18.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 15, "s": [44.147, 10.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "24", "w": 18, "h": 22, "ind": 121, "ty": 0, "nm": "R", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 5, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 15, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 120}, {"ind": 122, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 0, "s": [24.806, 20.84], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 10, "s": [24.806, 10.84], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 72}, {"refId": "25", "w": 18, "h": 22, "ind": 123, "ty": 0, "nm": "P", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 10, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 122}, {"ind": 124, "ty": 3, "nm": "Heading 1", "sr": 1, "ks": {"p": {"a": 0, "k": [196.397, 209.877]}, "a": {"a": 0, "k": [130.547, 26.353]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0}, {"ind": 125, "ty": 4, "nm": "Heading 1: group style", "sr": 1, "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "parent": 124, "shapes": [{"ty": "gr", "nm": "Heading 1", "it": [{"ty": "gr", "nm": "!", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[260.127, 49.629], [259.878, 50.83], [259.204, 51.797], [258.237, 52.456], [257.05, 52.705], [255.849, 52.456], [254.868, 51.797], [254.209, 50.83], [253.974, 49.629], [254.209, 48.428], [254.868, 47.447], [255.849, 46.773], [257.05, 46.524], [258.237, 46.773], [259.204, 47.447], [259.878, 48.428], [260.127, 49.629], [260.127, 49.629]], "i": [[0, 0], [0.166, -0.371], [0.283, -0.273], [0.371, -0.156], [0.42, 0], [0.371, 0.166], [0.283, 0.283], [0.166, 0.372], [0, 0.43], [-0.157, 0.381], [-0.274, 0.273], [-0.371, 0.166], [-0.429, 0], [-0.371, -0.166], [-0.274, -0.284], [-0.167, -0.381], [0, -0.42], [0, 0]], "o": [[0, 0.43], [-0.167, 0.372], [-0.274, 0.283], [-0.371, 0.166], [-0.429, 0], [-0.371, -0.156], [-0.274, -0.273], [-0.157, -0.371], [0, -0.42], [0.166, -0.381], [0.283, -0.284], [0.371, -0.166], [0.42, 0], [0.371, 0.166], [0.283, 0.273], [0.166, 0.381], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-257.505, -41.206]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[261.093, 29.707], [259.892, 45.118], [254.472, 45.118], [253.916, 29.707], [261.093, 29.707], [261.093, 29.707]], "i": [[0, 0], [0.4, -5.137], [1.807, 0], [0.185, 5.137], [-2.392, 0], [0, 0]], "o": [[-0.4, 5.137], [-1.807, 0], [-0.185, -5.137], [2.392, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-257.505, -41.206]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 120, "s": [257.505, 49.206], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 130, "s": [257.505, 41.206], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 120, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 130, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "O", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[244.886, 40.635], [244.696, 39.419], [244.169, 38.365], [243.29, 37.618], [242.103, 37.325], [240.873, 37.574], [239.935, 38.262], [239.349, 39.273], [239.144, 40.489], [239.32, 41.719], [239.847, 42.832], [240.711, 43.638], [241.927, 43.946], [243.158, 43.682], [244.095, 42.95], [244.681, 41.88], [244.886, 40.635], [244.886, 40.635]], "i": [[0, 0], [0.127, 0.391], [0.234, 0.302], [0.351, 0.185], [0.449, 0], [0.361, -0.166], [0.264, -0.293], [0.137, -0.391], [0, -0.43], [-0.117, -0.41], [-0.234, -0.332], [-0.341, -0.205], [-0.459, 0], [-0.362, 0.176], [-0.254, 0.302], [-0.136, 0.401], [0, 0.43], [0, 0]], "o": [[0, -0.42], [-0.117, -0.4], [-0.235, -0.313], [-0.342, -0.196], [-0.459, 0], [-0.362, 0.166], [-0.254, 0.283], [-0.137, 0.381], [0, 0.41], [0.117, 0.41], [0.235, 0.332], [0.352, 0.205], [0.459, 0], [0.371, -0.186], [0.254, -0.313], [0.137, -0.4], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-242.089, -40.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[251.39, 40.254], [251.083, 42.832], [250.189, 45.147], [248.783, 47.139], [246.922, 48.677], [244.681, 49.688], [242.103, 50.039], [239.584, 49.703], [237.357, 48.765], [235.467, 47.3], [234.032, 45.396], [233.109, 43.14], [232.787, 40.635], [233.094, 38.145], [233.973, 35.83], [235.379, 33.838], [237.211, 32.271], [239.422, 31.246], [241.927, 30.879], [245.795, 31.538], [248.783, 33.428], [250.702, 36.387], [251.39, 40.254], [251.39, 40.254]], "i": [[0, 0], [0.205, -0.83], [0.391, -0.723], [0.556, -0.605], [0.694, -0.43], [0.811, -0.244], [0.908, 0], [0.8, 0.224], [0.693, 0.4], [0.567, 0.567], [0.4, 0.693], [0.225, 0.801], [0, 0.869], [-0.205, 0.81], [-0.381, 0.723], [-0.546, 0.606], [-0.674, 0.439], [-0.791, 0.244], [-0.879, 0], [-1.163, -0.439], [-0.821, -0.82], [-0.449, -1.162], [0, -1.425], [0, 0]], "o": [[0, 0.889], [-0.205, 0.821], [-0.381, 0.723], [-0.547, 0.596], [-0.683, 0.43], [-0.81, 0.234], [-0.879, 0], [-0.791, -0.225], [-0.693, -0.41], [-0.556, -0.576], [-0.391, -0.703], [-0.215, -0.801], [0, -0.849], [0.205, -0.82], [0.391, -0.722], [0.547, -0.605], [0.683, -0.44], [0.791, -0.245], [1.416, 0], [1.171, 0.44], [0.83, 0.811], [0.459, 1.152], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-242.089, -40.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 115, "s": [242.089, 48.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 125, "s": [242.089, 40.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 115, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 125, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "L", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[231.096, 43.711], [230.481, 50.215], [218.147, 51.182], [218.616, 30.264], [226.233, 30.264], [225.149, 43.711], [231.096, 43.711], [231.096, 43.711]], "i": [[0, 0], [0.205, -2.168], [4.111, -0.322], [-0.156, 6.973], [-2.539, 0], [0.361, -4.482], [-1.982, 0], [0, 0]], "o": [[-0.205, 2.168], [-4.111, 0.322], [0.156, -6.973], [2.539, 0], [-0.361, 4.482], [1.982, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-224.622, -40.723]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 110, "s": [224.622, 48.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 120, "s": [224.622, 40.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 110, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 120, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "H", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[207.999, 50.889], [200.674, 51.241], [200.616, 45.059], [197.247, 45.059], [197.042, 51.475], [190.479, 51.475], [191.036, 30.879], [198.008, 31.202], [197.569, 41.719], [200.821, 41.719], [200.85, 30.704], [207.53, 30.879], [207.999, 50.889], [207.999, 50.889]], "i": [[0, 0], [2.442, -0.117], [0.019, 2.061], [1.123, 0], [0.068, -2.139], [2.188, 0], [-0.186, 6.865], [-2.324, -0.108], [0.146, -3.506], [-1.084, 0], [-0.01, 3.672], [-2.227, -0.058], [-0.156, -6.67], [0, 0]], "o": [[-2.442, 0.117], [-0.019, -2.061], [-1.123, 0], [-0.068, 2.139], [-2.188, 0], [0.186, -6.865], [2.324, 0.108], [-0.146, 3.506], [1.084, 0], [0.01, -3.672], [2.227, 0.058], [0.156, 6.67], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-199.239, -41.09]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 105, "s": [199.239, 49.09], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 115, "s": [199.239, 41.09], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 105, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 115, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "K", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[189.038, 32.549], [182.563, 40.254], [188.51, 50.332], [181.713, 51.534], [177.26, 45.498], [177.026, 51.241], [170.932, 51.475], [171.401, 30.498], [178.227, 30.879], [177.7, 36.914], [181.948, 30.791], [189.038, 32.549], [189.038, 32.549]], "i": [[0, 0], [2.158, -2.568], [-1.982, -3.359], [2.266, -0.401], [1.484, 2.012], [0.078, -1.914], [2.031, -0.078], [-0.156, 6.992], [-2.275, -0.127], [0.176, -2.012], [-1.416, 2.041], [-2.363, -0.586], [0, 0]], "o": [[-2.158, 2.568], [1.982, 3.359], [-2.266, 0.401], [-1.484, -2.012], [-0.078, 1.914], [-2.031, 0.078], [0.156, -6.992], [2.275, 0.127], [-0.176, 2.012], [1.416, -2.041], [2.363, 0.586], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-179.985, -41.016]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 100, "s": [179.985, 49.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 110, "s": [179.985, 41.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 100, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 110, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "E", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[162.709, 38.877], [162.621, 38.042], [162.328, 37.251], [161.815, 36.68], [161.009, 36.446], [159.969, 36.768], [159.208, 37.559], [158.68, 38.584], [158.373, 39.61], [162.709, 39.2], [162.709, 38.877], [162.709, 38.877]], "i": [[0, 0], [0.058, 0.284], [0.136, 0.235], [0.215, 0.147], [0.323, 0], [0.303, -0.215], [0.214, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.39, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.136, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-160.145, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[168.568, 39.639], [168.392, 42.188], [163.412, 42.715], [158.461, 43.418], [159.647, 45.103], [161.595, 45.733], [162.87, 45.513], [164.261, 44.971], [165.58, 44.253], [166.664, 43.565], [165.961, 50.215], [164.715, 50.962], [163.338, 51.46], [161.888, 51.739], [160.453, 51.827], [157.801, 51.431], [155.619, 50.332], [153.92, 48.648], [152.704, 46.538], [151.971, 44.121], [151.722, 41.543], [151.971, 38.819], [152.718, 36.197], [153.993, 33.868], [155.795, 31.978], [158.109, 30.718], [160.98, 30.264], [163.353, 30.63], [165.272, 31.626], [166.737, 33.135], [167.762, 35.069], [168.363, 37.281], [168.568, 39.639], [168.568, 39.639]], "i": [[0, 0], [0.117, -0.85], [1.65, -0.244], [1.65, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.234, -2.217], [0.45, -0.205], [0.479, -0.127], [0.489, -0.059], [0.468, 0], [0.801, 0.264], [0.654, 0.469], [0.488, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.166, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.684, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.557, -0.429], [-0.41, -0.586], [-0.263, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.651, 0.235], [0.253, 0.703], [0.547, 0.42], [0.391, 0], [0.468, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.234, 2.217], [-0.381, 0.293], [-0.439, 0.205], [-0.478, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.8, -0.264], [-0.645, -0.478], [-0.489, -0.645], [-0.323, -0.761], [-0.166, -0.849], [0, -0.908], [0.166, -0.908], [0.342, -0.84], [0.517, -0.723], [0.683, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.566, 0.42], [0.42, 0.586], [0.274, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-160.145, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 95, "s": [160.145, 49.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 105, "s": [160.145, 41.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 95, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 105, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "D", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[139.837, 45.469], [141.433, 44.942], [142.693, 43.946], [143.499, 42.598], [143.792, 40.987], [143.616, 39.434], [143.045, 38.174], [142.004, 37.325], [140.422, 36.973], [139.837, 45.469], [139.837, 45.469]], "i": [[0, 0], [-0.478, 0.263], [-0.352, 0.39], [-0.186, 0.498], [0, 0.566], [0.117, 0.479], [0.263, 0.352], [0.43, 0.205], [0.625, 0.02], [0.195, -2.832], [0, 0]], "o": [[0.585, -0.088], [0.488, -0.274], [0.352, -0.401], [0.195, -0.508], [0, -0.557], [-0.117, -0.488], [-0.264, -0.361], [-0.429, -0.215], [-0.195, 2.832], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-141.448, -40.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[149.797, 39.844], [149.27, 43.521], [147.791, 46.407], [145.549, 48.531], [142.693, 49.981], [139.382, 50.801], [135.794, 51.065], [134.446, 51.036], [133.098, 50.918], [133.508, 31.846], [136.819, 31.128], [140.217, 30.909], [144.07, 31.495], [147.102, 33.223], [149.08, 36.021], [149.797, 39.844], [149.797, 39.844]], "i": [[0, 0], [0.352, -1.084], [0.634, -0.84], [0.87, -0.586], [1.035, -0.381], [1.172, -0.176], [1.23, 0], [0.439, 0.019], [0.459, 0.049], [-0.137, 6.357], [-1.133, 0.137], [-1.123, 0], [-1.172, -0.391], [-0.849, -0.762], [-0.469, -1.113], [0, -1.445], [0, 0]], "o": [[0, 1.367], [-0.351, 1.084], [-0.625, 0.83], [-0.869, 0.586], [-1.035, 0.371], [-1.162, 0.176], [-0.459, 0], [-0.44, -0.03], [0.137, -6.357], [1.075, -0.342], [1.142, -0.146], [1.397, 0], [1.172, 0.39], [0.85, 0.752], [0.478, 1.103], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-141.448, -40.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 90, "s": [141.448, 48.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 100, "s": [141.448, 40.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 90, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 100, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "R", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[116.753, 37.823], [116.578, 36.695], [116.08, 35.816], [115.259, 35.23], [114.117, 35.01], [113.545, 35.054], [113.003, 35.157], [112.769, 40.606], [113.121, 40.606], [114.395, 40.445], [115.567, 39.947], [116.417, 39.082], [116.753, 37.823], [116.753, 37.823]], "i": [[0, 0], [0.117, 0.342], [0.224, 0.244], [0.332, 0.137], [0.439, 0], [0.186, -0.029], [0.176, -0.049], [0.078, -1.816], [-0.117, 0], [-0.43, 0.107], [-0.342, 0.224], [-0.215, 0.352], [0, 0.488], [0, 0]], "o": [[0, -0.41], [-0.108, -0.342], [-0.215, -0.254], [-0.322, -0.147], [-0.196, 0], [-0.185, 0.02], [-0.078, 1.816], [0.117, 0], [0.42, 0], [0.44, -0.108], [0.352, -0.225], [0.224, -0.351], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-114.674, -40.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[123.316, 37.354], [123.111, 39.434], [122.496, 41.133], [121.397, 42.569], [119.771, 43.829], [123.257, 50.039], [116.666, 51.329], [114.41, 45.088], [112.593, 45.147], [112.33, 51.241], [106.031, 51.241], [106.163, 44.59], [106.324, 37.94], [106.382, 34.6], [106.5, 31.26], [108.521, 30.63], [110.513, 30.249], [112.549, 30.059], [114.673, 30], [117.896, 30.469], [120.665, 31.846], [122.584, 34.146], [123.316, 37.354], [123.316, 37.354]], "i": [[0, 0], [0.137, -0.625], [0.283, -0.517], [0.449, -0.44], [0.635, -0.401], [-1.162, -2.07], [2.197, -0.43], [0.752, 2.08], [0.606, -0.02], [0.088, -2.031], [2.1, 0], [-0.039, 2.207], [-0.059, 2.226], [-0.019, 1.104], [-0.059, 1.123], [-0.664, 0.166], [-0.664, 0.088], [-0.683, 0.029], [-0.722, 0], [-1.035, -0.312], [-0.801, -0.615], [-0.479, -0.918], [0, -1.221], [0, 0]], "o": [[0, 0.762], [-0.127, 0.615], [-0.284, 0.518], [-0.449, 0.439], [1.162, 2.07], [-2.197, 0.43], [-0.752, -2.08], [-0.606, 0.02], [-0.088, 2.031], [-2.1, 0], [0.049, -2.227], [0.048, -2.207], [0.019, -1.123], [0.02, -1.104], [0.683, -0.254], [0.664, -0.166], [0.674, -0.097], [0.694, -0.039], [1.114, 0], [1.045, 0.303], [0.8, 0.615], [0.488, 0.918], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-114.674, -40.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 85, "s": [114.674, 48.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 95, "s": [114.674, 40.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 85, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 95, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "A", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[95.669, 43.36], [94.555, 38.262], [93.471, 43.36], [95.669, 43.36], [95.669, 43.36]], "i": [[0, 0], [0.371, 1.699], [0.361, -1.699], [-0.733, 0], [0, 0]], "o": [[-0.371, -1.699], [-0.361, 1.699], [0.733, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-94.497, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[104.34, 50.215], [97.075, 51.153], [96.196, 47.842], [92.856, 47.842], [92.124, 51.153], [84.653, 50.42], [90.483, 30.909], [98.628, 30.498], [104.34, 50.215], [104.34, 50.215]], "i": [[0, 0], [2.422, -0.313], [0.293, 1.104], [1.113, 0], [0.244, -1.104], [2.49, 0.244], [-1.943, 6.504], [-2.715, 0.137], [-1.904, -6.572], [0, 0]], "o": [[-2.422, 0.313], [-0.293, -1.104], [-1.113, 0], [-0.244, 1.104], [-2.49, -0.244], [1.943, -6.504], [2.715, -0.137], [1.904, 6.572], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-94.497, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 80, "s": [94.497, 48.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 90, "s": [94.497, 40.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 80, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 90, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "A", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[75.389, 43.36], [74.276, 38.262], [73.192, 43.36], [75.389, 43.36], [75.389, 43.36]], "i": [[0, 0], [0.371, 1.699], [0.361, -1.699], [-0.732, 0], [0, 0]], "o": [[-0.371, -1.699], [-0.361, 1.699], [0.732, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-74.218, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[84.061, 50.215], [76.795, 51.153], [75.917, 47.842], [72.577, 47.842], [71.844, 51.153], [64.374, 50.42], [70.204, 30.909], [78.348, 30.498], [84.061, 50.215], [84.061, 50.215]], "i": [[0, 0], [2.422, -0.313], [0.293, 1.104], [1.113, 0], [0.244, -1.104], [2.49, 0.244], [-1.943, 6.504], [-2.715, 0.137], [-1.904, -6.572], [0, 0]], "o": [[-2.422, 0.313], [-0.293, -1.104], [-1.113, 0], [-0.244, 1.104], [-2.49, -0.244], [1.943, -6.504], [2.715, -0.137], [1.904, 6.572], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-74.218, -40.826]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 75, "s": [74.218, 48.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 85, "s": [74.218, 40.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 75, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 85, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "B", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[53.777, 42.686], [52.957, 42.774], [52.898, 46.114], [53.323, 46.202], [53.748, 46.231], [54.421, 46.172], [55.11, 45.909], [55.637, 45.367], [55.857, 44.473], [55.637, 43.565], [55.125, 43.023], [54.451, 42.759], [53.777, 42.686], [53.777, 42.686]], "i": [[0, 0], [0.263, -0.059], [0.02, -1.113], [-0.137, -0.03], [-0.137, 0], [-0.244, 0.039], [-0.215, 0.127], [-0.136, 0.224], [0, 0.371], [0.147, 0.234], [0.205, 0.127], [0.244, 0.039], [0.205, 0], [0, 0]], "o": [[-0.283, 0], [-0.02, 1.113], [0.147, 0.029], [0.146, 0.019], [0.205, 0], [0.245, -0.049], [0.215, -0.137], [0.147, -0.225], [0, -0.371], [-0.136, -0.235], [-0.205, -0.137], [-0.244, -0.049], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-54.788, -40.973]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[55.857, 37.911], [55.681, 37.193], [55.242, 36.695], [54.685, 36.416], [54.158, 36.329], [53.587, 36.416], [53.045, 36.621], [52.957, 39.873], [53.792, 39.815], [54.744, 39.566], [55.535, 38.98], [55.857, 37.911], [55.857, 37.911]], "i": [[0, 0], [0.117, 0.195], [0.176, 0.127], [0.205, 0.059], [0.156, 0], [0.185, -0.058], [0.175, -0.078], [0.029, -1.084], [-0.332, 0.039], [-0.303, 0.127], [-0.215, 0.254], [0, 0.449], [0, 0]], "o": [[0, -0.284], [-0.117, -0.205], [-0.166, -0.127], [-0.195, -0.058], [-0.196, 0], [-0.186, 0.059], [-0.029, 1.084], [0.224, 0], [0.332, -0.039], [0.312, -0.137], [0.215, -0.264], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-54.788, -40.973]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[63.298, 46.26], [62.844, 48.355], [61.658, 49.849], [59.929, 50.86], [57.908, 51.46], [55.784, 51.753], [53.806, 51.827], [51.917, 51.753], [49.983, 51.534], [48.079, 51.109], [46.306, 50.42], [46.277, 31.553], [48.02, 30.953], [49.895, 30.498], [51.814, 30.22], [53.66, 30.118], [55.754, 30.249], [57.805, 30.689], [59.636, 31.495], [61.116, 32.696], [62.112, 34.366], [62.478, 36.563], [62.244, 38.101], [61.57, 39.39], [60.501, 40.371], [59.08, 40.957], [60.808, 41.69], [62.141, 42.862], [62.991, 44.414], [63.298, 46.26], [63.298, 46.26]], "i": [[0, 0], [0.303, -0.586], [0.498, -0.41], [0.655, -0.264], [0.703, -0.146], [0.713, -0.049], [0.615, 0], [0.654, 0.049], [0.645, 0.107], [0.625, 0.176], [0.557, 0.274], [0.01, 6.289], [-0.615, 0.175], [-0.635, 0.118], [-0.635, 0.059], [-0.596, 0], [-0.703, -0.087], [-0.654, -0.205], [-0.566, -0.332], [-0.42, -0.479], [-0.234, -0.635], [0, -0.83], [0.156, -0.479], [0.293, -0.381], [0.419, -0.273], [0.527, -0.127], [-0.517, -0.322], [-0.361, -0.459], [-0.196, -0.576], [0, -0.654], [0, 0]], "o": [[0, 0.811], [-0.293, 0.586], [-0.498, 0.41], [-0.644, 0.254], [-0.703, 0.147], [-0.703, 0.049], [-0.605, 0], [-0.645, -0.039], [-0.645, -0.108], [-0.625, -0.186], [-0.01, -6.289], [0.547, -0.224], [0.615, -0.186], [0.645, -0.127], [0.635, -0.068], [0.693, 0], [0.713, 0.088], [0.655, 0.205], [0.567, 0.322], [0.429, 0.478], [0.244, 0.634], [0, 0.547], [-0.157, 0.478], [-0.293, 0.381], [-0.42, 0.264], [0.634, 0.166], [0.528, 0.322], [0.371, 0.459], [0.205, 0.577], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-54.788, -40.973]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 70, "s": [54.788, 48.973], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 80, "s": [54.788, 40.973], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 70, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 80, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "K", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[37.315, 32.549], [30.841, 40.254], [36.788, 50.332], [29.991, 51.534], [25.538, 45.498], [25.304, 51.241], [19.21, 51.475], [19.679, 30.498], [26.505, 30.879], [25.978, 36.914], [30.226, 30.791], [37.315, 32.549], [37.315, 32.549]], "i": [[0, 0], [2.158, -2.568], [-1.983, -3.359], [2.266, -0.401], [1.484, 2.012], [0.078, -1.914], [2.031, -0.078], [-0.156, 6.992], [-2.275, -0.127], [0.176, -2.012], [-1.416, 2.041], [-2.363, -0.586], [0, 0]], "o": [[-2.158, 2.568], [1.983, 3.359], [-2.266, 0.401], [-1.484, -2.012], [-0.078, 1.914], [-2.031, 0.078], [0.156, -6.992], [2.275, 0.127], [-0.176, 2.012], [1.416, -2.041], [2.363, 0.586], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-28.263, -41.016]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 65, "s": [28.263, 49.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 75, "s": [28.263, 41.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 65, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 75, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "E", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[10.986, 38.877], [10.898, 38.042], [10.606, 37.251], [10.093, 36.68], [9.287, 36.446], [8.247, 36.768], [7.485, 37.559], [6.958, 38.584], [6.65, 39.61], [10.986, 39.2], [10.986, 38.877], [10.986, 38.877]], "i": [[0, 0], [0.059, 0.284], [0.137, 0.235], [0.215, 0.147], [0.322, 0], [0.303, -0.215], [0.215, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.391, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.137, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-8.423, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[16.846, 39.639], [16.67, 42.188], [11.689, 42.715], [6.738, 43.418], [7.925, 45.103], [9.873, 45.733], [11.147, 45.513], [12.539, 44.971], [13.857, 44.253], [14.941, 43.565], [14.238, 50.215], [12.993, 50.962], [11.616, 51.46], [10.166, 51.739], [8.731, 51.827], [6.079, 51.431], [3.897, 50.332], [2.197, 48.648], [0.981, 46.538], [0.249, 44.121], [0, 41.543], [0.249, 38.819], [0.996, 36.197], [2.27, 33.868], [4.072, 31.978], [6.387, 30.718], [9.258, 30.264], [11.631, 30.63], [13.55, 31.626], [15.015, 33.135], [16.04, 35.069], [16.641, 37.281], [16.846, 39.639], [16.846, 39.639]], "i": [[0, 0], [0.117, -0.85], [1.65, -0.244], [1.65, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.234, -2.217], [0.449, -0.205], [0.478, -0.127], [0.488, -0.059], [0.469, 0], [0.801, 0.264], [0.654, 0.469], [0.488, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.166, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.684, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.557, -0.429], [-0.41, -0.586], [-0.264, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.65, 0.235], [0.254, 0.703], [0.547, 0.42], [0.391, 0], [0.469, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.234, 2.217], [-0.381, 0.293], [-0.439, 0.205], [-0.479, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.801, -0.264], [-0.645, -0.478], [-0.488, -0.645], [-0.322, -0.761], [-0.166, -0.849], [0, -0.908], [0.166, -0.908], [0.342, -0.84], [0.518, -0.723], [0.683, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.566, 0.42], [0.42, 0.586], [0.273, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-8.423, -41.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 60, "s": [8.423, 49.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 70, "s": [8.423, 41.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 60, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 70, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "W", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[245.692, 1.143], [242.264, 20.86], [233.944, 21.036], [232.157, 11.104], [230.37, 20.274], [222.049, 20.567], [218.592, 1.729], [225.858, 1.084], [227.323, 12.657], [229.286, 1.963], [234.999, 1.963], [237.284, 12.803], [238.456, 0.498], [245.692, 1.143], [245.692, 1.143]], "i": [[0, 0], [1.143, -6.572], [2.773, -0.059], [0.596, 3.311], [0.596, -3.057], [2.774, -0.098], [1.152, 6.279], [-2.422, 0.215], [-0.488, -3.858], [-0.654, 3.565], [-1.904, 0], [-0.762, -3.613], [-0.391, 4.102], [-2.412, -0.215], [0, 0]], "o": [[-1.143, 6.572], [-2.773, 0.059], [-0.596, -3.311], [-0.596, 3.057], [-2.774, 0.098], [-1.152, -6.279], [2.422, -0.215], [0.488, 3.858], [0.654, -3.565], [1.904, 0], [0.762, 3.613], [0.391, -4.102], [2.412, 0.215], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-232.142, -10.767]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 55, "s": [232.142, 18.767], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 65, "s": [232.142, 10.767], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 55, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 65, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "O", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[210.618, 10.635], [210.427, 9.419], [209.9, 8.365], [209.021, 7.618], [207.835, 7.325], [206.604, 7.574], [205.667, 8.262], [205.081, 9.273], [204.876, 10.489], [205.051, 11.719], [205.579, 12.832], [206.443, 13.638], [207.659, 13.946], [208.889, 13.682], [209.827, 12.95], [210.413, 11.88], [210.618, 10.635], [210.618, 10.635]], "i": [[0, 0], [0.127, 0.391], [0.234, 0.302], [0.352, 0.185], [0.449, 0], [0.361, -0.166], [0.263, -0.293], [0.136, -0.391], [0, -0.43], [-0.117, -0.41], [-0.235, -0.332], [-0.342, -0.205], [-0.459, 0], [-0.361, 0.176], [-0.254, 0.302], [-0.137, 0.401], [0, 0.43], [0, 0]], "o": [[0, -0.42], [-0.117, -0.4], [-0.234, -0.313], [-0.342, -0.196], [-0.459, 0], [-0.361, 0.166], [-0.254, 0.283], [-0.137, 0.381], [0, 0.41], [0.118, 0.41], [0.234, 0.332], [0.352, 0.205], [0.459, 0], [0.371, -0.186], [0.254, -0.313], [0.136, -0.4], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-207.82, -10.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[217.122, 10.254], [216.814, 12.832], [215.92, 15.147], [214.514, 17.139], [212.654, 18.677], [210.413, 19.688], [207.835, 20.039], [205.315, 19.703], [203.088, 18.765], [201.199, 17.3], [199.763, 15.396], [198.84, 13.14], [198.518, 10.635], [198.826, 8.145], [199.705, 5.83], [201.111, 3.838], [202.942, 2.271], [205.154, 1.246], [207.659, 0.879], [211.526, 1.538], [214.514, 3.428], [216.433, 6.387], [217.122, 10.254], [217.122, 10.254]], "i": [[0, 0], [0.205, -0.83], [0.391, -0.723], [0.557, -0.605], [0.693, -0.43], [0.81, -0.244], [0.908, 0], [0.801, 0.224], [0.694, 0.4], [0.566, 0.567], [0.401, 0.693], [0.225, 0.801], [0, 0.869], [-0.205, 0.81], [-0.381, 0.723], [-0.547, 0.606], [-0.674, 0.439], [-0.791, 0.244], [-0.879, 0], [-1.162, -0.439], [-0.82, -0.82], [-0.449, -1.162], [0, -1.425], [0, 0]], "o": [[0, 0.889], [-0.205, 0.821], [-0.38, 0.723], [-0.547, 0.596], [-0.684, 0.43], [-0.811, 0.234], [-0.879, 0], [-0.791, -0.225], [-0.693, -0.41], [-0.557, -0.576], [-0.39, -0.703], [-0.214, -0.801], [0, -0.849], [0.205, -0.82], [0.39, -0.722], [0.547, -0.605], [0.684, -0.44], [0.791, -0.245], [1.416, 0], [1.172, 0.44], [0.83, 0.811], [0.459, 1.152], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-207.82, -10.459]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 50, "s": [207.82, 18.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 60, "s": [207.82, 10.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 50, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 60, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "L", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[196.828, 13.711], [196.212, 20.215], [183.878, 21.182], [184.347, 0.264], [191.964, 0.264], [190.88, 13.711], [196.828, 13.711], [196.828, 13.711]], "i": [[0, 0], [0.205, -2.168], [4.111, -0.322], [-0.156, 6.973], [-2.539, 0], [0.361, -4.482], [-1.983, 0], [0, 0]], "o": [[-0.205, 2.168], [-4.111, 0.322], [0.156, -6.973], [2.539, 0], [-0.361, 4.482], [1.983, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-190.353, -10.723]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 45, "s": [190.353, 18.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 55, "s": [190.353, 10.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 45, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 55, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "e", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[168.295, 8.877], [168.208, 8.042], [167.915, 7.251], [167.402, 6.68], [166.596, 6.446], [165.556, 6.768], [164.795, 7.559], [164.267, 8.584], [163.96, 9.61], [168.295, 9.2], [168.295, 8.877], [168.295, 8.877]], "i": [[0, 0], [0.058, 0.284], [0.136, 0.235], [0.215, 0.147], [0.323, 0], [0.303, -0.215], [0.214, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.39, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.137, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-165.732, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[174.155, 9.639], [173.979, 12.188], [168.999, 12.715], [164.047, 13.418], [165.234, 15.103], [167.182, 15.733], [168.457, 15.513], [169.848, 14.971], [171.167, 14.253], [172.251, 13.565], [171.547, 20.215], [170.302, 20.962], [168.925, 21.46], [167.475, 21.739], [166.04, 21.827], [163.388, 21.431], [161.206, 20.332], [159.506, 18.648], [158.291, 16.538], [157.558, 14.121], [157.309, 11.543], [157.558, 8.819], [158.305, 6.197], [159.58, 3.868], [161.381, 1.978], [163.696, 0.718], [166.567, 0.264], [168.94, 0.63], [170.859, 1.626], [172.324, 3.135], [173.349, 5.069], [173.95, 7.281], [174.155, 9.639], [174.155, 9.639]], "i": [[0, 0], [0.117, -0.85], [1.65, -0.244], [1.651, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.235, -2.217], [0.45, -0.205], [0.479, -0.127], [0.488, -0.059], [0.468, 0], [0.801, 0.264], [0.654, 0.469], [0.489, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.166, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.683, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.557, -0.429], [-0.41, -0.586], [-0.263, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.651, 0.235], [0.254, 0.703], [0.547, 0.42], [0.391, 0], [0.468, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.235, 2.217], [-0.38, 0.293], [-0.439, 0.205], [-0.478, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.801, -0.264], [-0.645, -0.478], [-0.488, -0.645], [-0.323, -0.761], [-0.166, -0.849], [0, -0.908], [0.166, -0.908], [0.342, -0.84], [0.517, -0.723], [0.684, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.566, 0.42], [0.42, 0.586], [0.274, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-165.732, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 40, "s": [165.732, 19.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 50, "s": [165.732, 11.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 40, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 50, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "N", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[154.916, 14.063], [154.872, 17.344], [154.769, 20.596], [146.332, 20.45], [147.093, 15.279], [147.416, 10.049], [147.401, 9.434], [147.342, 8.482], [147.211, 7.383], [146.991, 6.343], [146.654, 5.581], [146.185, 5.274], [145.336, 5.567], [144.764, 6.329], [144.413, 7.354], [144.208, 8.496], [144.12, 9.58], [144.105, 10.401], [143.988, 15.557], [143.871, 20.713], [136.4, 21.036], [136.957, 10.752], [137.396, 0.469], [144.31, 0.118], [144.252, 2.461], [145.13, 1.524], [146.156, 0.777], [147.298, 0.279], [148.587, 0.088], [150.77, 0.498], [152.381, 1.612], [153.509, 3.282], [154.242, 5.318], [154.667, 7.588], [154.857, 9.903], [154.916, 12.115], [154.916, 14.063], [154.916, 14.063]], "i": [[0, 0], [0.029, -1.084], [0.049, -1.094], [2.812, 0.049], [-0.215, 1.718], [0, 1.758], [0.01, 0.283], [0.03, 0.351], [0.058, 0.371], [0.097, 0.313], [0.137, 0.196], [0.186, 0], [0.234, -0.196], [0.156, -0.313], [0.088, -0.381], [0.048, -0.38], [0.019, -0.341], [0, -0.215], [0.029, -1.709], [0.049, -1.738], [2.49, -0.108], [-0.176, 3.409], [-0.107, 3.447], [-2.305, 0.117], [0.019, -0.781], [-0.312, 0.293], [-0.361, 0.205], [-0.4, 0.117], [-0.449, 0], [-0.625, -0.273], [-0.449, -0.469], [-0.302, -0.645], [-0.186, -0.723], [-0.098, -0.791], [-0.029, -0.762], [0, -0.713], [0, -0.586], [0, 0]], "o": [[0, 1.103], [-0.02, 1.074], [-2.812, -0.049], [0.293, -1.729], [0.215, -1.729], [0, -0.127], [-0.01, -0.283], [-0.029, -0.361], [-0.049, -0.381], [-0.088, -0.312], [-0.127, -0.205], [-0.332, 0], [-0.225, 0.195], [-0.146, 0.302], [-0.088, 0.381], [-0.039, 0.381], [-0.01, 0.333], [-0.049, 1.728], [-0.029, 1.699], [-2.49, 0.108], [0.195, -3.448], [0.185, -3.408], [2.305, -0.117], [-0.019, 0.781], [0.273, -0.332], [0.323, -0.293], [0.361, -0.215], [0.411, -0.127], [0.831, 0], [0.625, 0.274], [0.45, 0.468], [0.303, 0.634], [0.185, 0.722], [0.097, 0.782], [0.039, 0.761], [0, 0.713], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-145.658, -10.562]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 35, "s": [145.658, 18.562], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 45, "s": [145.658, 10.562], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 35, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 45, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "T", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[134.578, 0.821], [134.431, 7.032], [130.066, 7.207], [129.539, 20.801], [123.357, 21.123], [122.8, 7.53], [118.435, 7.764], [118.611, 0.85], [134.578, 0.821], [134.578, 0.821]], "i": [[0, 0], [0.049, -2.07], [1.455, -0.058], [0.176, -4.531], [2.061, -0.107], [0.186, 4.531], [1.455, -0.078], [-0.059, 2.305], [-5.322, 0.01], [0, 0]], "o": [[-0.049, 2.07], [-1.455, 0.058], [-0.176, 4.531], [-2.061, 0.107], [-0.186, -4.531], [-1.455, 0.078], [0.059, -2.305], [5.322, -0.01], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-126.507, -10.972]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 30, "s": [126.507, 18.972], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 40, "s": [126.507, 10.972], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 30, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 40, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "I", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[116.364, 0.85], [115.338, 20.801], [109.157, 21.123], [108.571, 1.26], [116.364, 0.85], [116.364, 0.85]], "i": [[0, 0], [0.342, -6.65], [2.06, -0.107], [0.195, 6.621], [-2.598, 0.137], [0, 0]], "o": [[-0.342, 6.65], [-2.06, 0.107], [-0.195, -6.621], [2.598, -0.137], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-112.468, -10.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 25, "s": [112.468, 18.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 35, "s": [112.468, 10.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 25, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 35, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "e", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[93.032, 8.877], [92.944, 8.042], [92.651, 7.251], [92.138, 6.68], [91.333, 6.446], [90.293, 6.768], [89.531, 7.559], [89.003, 8.584], [88.696, 9.61], [93.032, 9.2], [93.032, 8.877], [93.032, 8.877]], "i": [[0, 0], [0.059, 0.284], [0.137, 0.235], [0.215, 0.147], [0.322, 0], [0.302, -0.215], [0.215, -0.322], [0.137, -0.371], [0.068, -0.313], [-1.445, 0.137], [0, 0.108], [0, 0]], "o": [[0, -0.273], [-0.059, -0.293], [-0.127, -0.234], [-0.215, -0.156], [-0.391, 0], [-0.293, 0.205], [-0.215, 0.312], [-0.136, 0.371], [1.445, -0.137], [0, -0.108], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-90.468, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[98.891, 9.639], [98.715, 12.188], [93.735, 12.715], [88.784, 13.418], [89.97, 15.103], [91.919, 15.733], [93.193, 15.513], [94.585, 14.971], [95.903, 14.253], [96.987, 13.565], [96.284, 20.215], [95.039, 20.962], [93.662, 21.46], [92.211, 21.739], [90.776, 21.827], [88.125, 21.431], [85.942, 20.332], [84.243, 18.648], [83.027, 16.538], [82.295, 14.121], [82.045, 11.543], [82.295, 8.819], [83.042, 6.197], [84.316, 3.868], [86.118, 1.978], [88.432, 0.718], [91.303, 0.264], [93.676, 0.63], [95.595, 1.626], [97.06, 3.135], [98.086, 5.069], [98.686, 7.281], [98.891, 9.639], [98.891, 9.639]], "i": [[0, 0], [0.118, -0.85], [1.65, -0.244], [1.65, -0.234], [-0.537, -0.42], [-0.752, 0], [-0.459, 0.147], [-0.459, 0.215], [-0.42, 0.254], [-0.303, 0.205], [0.234, -2.217], [0.449, -0.205], [0.478, -0.127], [0.489, -0.059], [0.469, 0], [0.8, 0.264], [0.654, 0.469], [0.488, 0.644], [0.322, 0.762], [0.166, 0.85], [0, 0.87], [-0.167, 0.908], [-0.332, 0.84], [-0.508, 0.712], [-0.684, 0.537], [-0.859, 0.303], [-1.045, 0], [-0.713, -0.244], [-0.556, -0.429], [-0.41, -0.586], [-0.264, -0.703], [-0.127, -0.782], [0, -0.801], [0, 0]], "o": [[0, 0.85], [-1.67, 0.107], [-1.65, 0.235], [0.254, 0.703], [0.547, 0.42], [0.39, 0], [0.469, -0.146], [0.459, -0.225], [0.42, -0.254], [-0.234, 2.217], [-0.381, 0.293], [-0.44, 0.205], [-0.479, 0.127], [-0.488, 0.058], [-0.967, 0], [-0.801, -0.264], [-0.645, -0.478], [-0.489, -0.645], [-0.322, -0.761], [-0.167, -0.849], [0, -0.908], [0.166, -0.908], [0.341, -0.84], [0.518, -0.723], [0.683, -0.537], [0.869, -0.303], [0.869, 0], [0.723, 0.235], [0.567, 0.42], [0.42, 0.586], [0.273, 0.693], [0.137, 0.771], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-90.468, -11.046]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 20, "s": [90.468, 19.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 30, "s": [90.468, 11.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 20, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 30, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "C", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[79.637, 1.465], [79.11, 7.647], [78.275, 7.486], [77.44, 7.442], [75.448, 7.705], [73.778, 8.511], [72.621, 9.888], [72.196, 11.836], [72.43, 13.272], [73.119, 14.283], [74.203, 14.883], [75.624, 15.088], [76.737, 14.986], [77.879, 14.678], [78.978, 14.239], [79.96, 13.711], [79.725, 20.596], [78.495, 21.123], [77.132, 21.504], [75.741, 21.768], [74.422, 21.856], [70.628, 21.079], [67.699, 18.97], [65.809, 15.85], [65.135, 12.071], [65.809, 7.588], [67.816, 3.985], [71.097, 1.582], [75.594, 0.704], [77.645, 0.865], [79.637, 1.465], [79.637, 1.465]], "i": [[0, 0], [0.176, -2.061], [0.273, 0.029], [0.283, 0], [0.635, -0.175], [0.488, -0.361], [0.293, -0.557], [0, -0.752], [-0.156, -0.41], [-0.293, -0.274], [-0.42, -0.137], [-0.518, 0], [-0.381, 0.068], [-0.38, 0.127], [-0.351, 0.166], [-0.303, 0.186], [0.078, -2.295], [0.439, -0.156], [0.469, -0.107], [0.469, -0.059], [0.42, 0], [1.143, 0.518], [0.82, 0.889], [0.449, 1.182], [0, 1.338], [-0.449, 1.387], [-0.879, 1.015], [-1.299, 0.577], [-1.689, 0], [-0.693, -0.108], [-0.634, -0.293], [0, 0]], "o": [[-0.176, 2.061], [-0.283, -0.078], [-0.273, -0.03], [-0.693, 0], [-0.625, 0.176], [-0.479, 0.361], [-0.284, 0.547], [0, 0.547], [0.166, 0.4], [0.302, 0.263], [0.429, 0.137], [0.361, 0], [0.381, -0.078], [0.381, -0.127], [0.352, -0.166], [-0.078, 2.295], [-0.381, 0.195], [-0.44, 0.147], [-0.459, 0.117], [-0.459, 0.059], [-1.386, 0], [-1.132, -0.517], [-0.811, -0.898], [-0.449, -1.182], [0, -1.602], [0.459, -1.386], [0.889, -1.026], [1.309, -0.586], [0.674, 0], [0.693, 0.107], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-72.548, -11.28]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 15, "s": [72.548, 19.28], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 25, "s": [72.548, 11.28], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 15, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 25, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "I", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[62.961, 0.85], [61.936, 20.801], [55.754, 21.123], [55.169, 1.26], [62.961, 0.85], [62.961, 0.85]], "i": [[0, 0], [0.342, -6.65], [2.061, -0.107], [0.195, 6.621], [-2.597, 0.137], [0, 0]], "o": [[-0.342, 6.65], [-2.061, 0.107], [-0.195, -6.621], [2.597, -0.137], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-59.065, -10.987]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 10, "s": [59.065, 18.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 20, "s": [59.065, 10.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 10, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 20, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "R", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[46.227, 7.823], [46.051, 6.695], [45.553, 5.816], [44.733, 5.23], [43.59, 5.01], [43.019, 5.054], [42.477, 5.157], [42.243, 10.606], [42.594, 10.606], [43.869, 10.445], [45.041, 9.947], [45.89, 9.082], [46.227, 7.823], [46.227, 7.823]], "i": [[0, 0], [0.118, 0.342], [0.225, 0.244], [0.332, 0.137], [0.44, 0], [0.186, -0.029], [0.176, -0.049], [0.078, -1.816], [-0.117, 0], [-0.43, 0.107], [-0.342, 0.224], [-0.215, 0.352], [0, 0.488], [0, 0]], "o": [[0, -0.41], [-0.107, -0.342], [-0.215, -0.254], [-0.322, -0.147], [-0.195, 0], [-0.185, 0.02], [-0.078, 1.816], [0.117, 0], [0.42, 0], [0.439, -0.108], [0.351, -0.225], [0.225, -0.351], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-44.147, -10.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[52.79, 7.354], [52.585, 9.434], [51.969, 11.133], [50.871, 12.569], [49.245, 13.829], [52.731, 20.039], [46.139, 21.329], [43.883, 15.088], [42.067, 15.147], [41.803, 21.241], [35.504, 21.241], [35.636, 14.59], [35.797, 7.94], [35.856, 4.6], [35.973, 1.26], [37.995, 0.63], [39.987, 0.249], [42.023, 0.059], [44.147, 0], [47.37, 0.469], [50.138, 1.846], [52.057, 4.146], [52.79, 7.354], [52.79, 7.354]], "i": [[0, 0], [0.136, -0.625], [0.284, -0.517], [0.449, -0.44], [0.634, -0.401], [-1.162, -2.07], [2.197, -0.43], [0.752, 2.08], [0.605, -0.02], [0.088, -2.031], [2.1, 0], [-0.039, 2.207], [-0.058, 2.226], [-0.019, 1.104], [-0.058, 1.123], [-0.664, 0.166], [-0.664, 0.088], [-0.684, 0.029], [-0.723, 0], [-1.035, -0.312], [-0.801, -0.615], [-0.478, -0.918], [0, -1.221], [0, 0]], "o": [[0, 0.762], [-0.127, 0.615], [-0.283, 0.518], [-0.45, 0.439], [1.162, 2.07], [-2.197, 0.43], [-0.752, -2.08], [-0.605, 0.02], [-0.088, 2.031], [-2.1, 0], [0.049, -2.227], [0.049, -2.207], [0.02, -1.123], [0.02, -1.104], [0.684, -0.254], [0.664, -0.166], [0.674, -0.097], [0.693, -0.039], [1.113, 0], [1.045, 0.303], [0.801, 0.615], [0.488, 0.918], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-44.147, -10.664]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 5, "s": [44.147, 18.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 15, "s": [44.147, 10.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 5, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 15, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "P", "it": [{"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[26.813, 7.881], [26.227, 6.402], [24.732, 5.86], [24.088, 5.918], [23.473, 6.036], [23.297, 10.313], [23.692, 10.342], [24.088, 10.342], [25.099, 10.166], [25.978, 9.668], [26.578, 8.892], [26.813, 7.881], [26.813, 7.881]], "i": [[0, 0], [0.391, 0.351], [0.615, 0], [0.225, -0.039], [0.195, -0.049], [0.059, -1.426], [-0.127, 0], [-0.137, 0], [-0.322, 0.118], [-0.254, 0.215], [-0.147, 0.293], [0, 0.371], [0, 0]], "o": [[0, -0.635], [-0.381, -0.362], [-0.205, 0], [-0.215, 0.03], [-0.059, 1.426], [0.137, 0.019], [0.127, 0], [0.352, 0], [0.332, -0.117], [0.254, -0.224], [0.156, -0.303], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-24.806, -10.84]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "Path", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[33.316, 7.383], [32.892, 9.903], [31.72, 11.822], [29.991, 13.213], [27.853, 14.151], [25.509, 14.678], [23.121, 14.854], [23.121, 21.68], [16.295, 21.68], [16.31, 15], [16.353, 8.291], [16.368, 4.717], [16.441, 1.114], [20.191, 0.279], [24.059, 0], [26.314, 0.191], [28.468, 0.762], [30.387, 1.758], [31.925, 3.164], [32.936, 5.039], [33.316, 7.383], [33.316, 7.383]], "i": [[0, 0], [0.283, -0.733], [0.498, -0.547], [0.664, -0.381], [0.762, -0.244], [0.811, -0.117], [0.781, -0.01], [0, -2.275], [2.275, 0], [-0.01, 2.217], [-0.019, 2.256], [0.01, 1.192], [-0.059, 1.211], [-1.25, 0.185], [-1.328, 0], [-0.752, -0.127], [-0.684, -0.264], [-0.586, -0.4], [-0.43, -0.546], [-0.244, -0.703], [0, -0.869], [0, 0]], "o": [[0, 0.947], [-0.283, 0.732], [-0.488, 0.547], [-0.664, 0.381], [-0.752, 0.234], [-0.811, 0.108], [0, 2.275], [-2.275, 0], [0, -2.236], [0.01, -2.216], [0.02, -1.191], [-0.01, -1.191], [1.25, -0.371], [1.25, -0.186], [0.752, 0], [0.752, 0.117], [0.693, 0.264], [0.596, 0.391], [0.43, 0.547], [0.254, 0.694], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-24.806, -10.84]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 0, "s": [24.806, 20.84], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 10, "s": [24.806, 10.84], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 10, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.996, 0.855, 0]}, "o": {"a": 0, "k": 100}, "r": 1, "hd": true}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}, {"ind": 126, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 120, "s": [257.505, 49.206], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 130, "s": [257.505, 41.206], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "26", "w": 8, "h": 24, "ind": 127, "ty": 0, "nm": "!", "sr": 1, "ks": {"p": {"a": 0, "k": [-4, -12]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 120, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 130, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 126}, {"ind": 128, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 115, "s": [242.089, 48.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 125, "s": [242.089, 40.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "27", "w": 20, "h": 20, "ind": 129, "ty": 0, "nm": "O", "sr": 1, "ks": {"p": {"a": 0, "k": [-10, -10]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 115, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 125, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 128}, {"ind": 130, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 110, "s": [224.622, 48.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 120, "s": [224.622, 40.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "28", "w": 14, "h": 22, "ind": 131, "ty": 0, "nm": "L", "sr": 1, "ks": {"p": {"a": 0, "k": [-7, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 110, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 120, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 130}, {"ind": 132, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 105, "s": [199.239, 49.09], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 115, "s": [199.239, 41.09], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "29", "w": 18, "h": 22, "ind": 133, "ty": 0, "nm": "H", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 105, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 115, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 132}, {"ind": 134, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 100, "s": [179.985, 49.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 110, "s": [179.985, 41.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "30", "w": 20, "h": 22, "ind": 135, "ty": 0, "nm": "K", "sr": 1, "ks": {"p": {"a": 0, "k": [-10, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 100, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 110, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 134}, {"ind": 136, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 95, "s": [160.145, 49.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 105, "s": [160.145, 41.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "31", "w": 18, "h": 22, "ind": 137, "ty": 0, "nm": "E", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 95, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 105, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 136}, {"ind": 138, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 90, "s": [141.448, 48.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 100, "s": [141.448, 40.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "32", "w": 18, "h": 22, "ind": 139, "ty": 0, "nm": "D", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 90, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 100, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 138}, {"ind": 140, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 85, "s": [114.674, 48.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 95, "s": [114.674, 40.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "33", "w": 18, "h": 22, "ind": 141, "ty": 0, "nm": "R", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 85, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 95, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 140}, {"ind": 142, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 80, "s": [94.497, 48.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 90, "s": [94.497, 40.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "34", "w": 20, "h": 22, "ind": 143, "ty": 0, "nm": "A", "sr": 1, "ks": {"p": {"a": 0, "k": [-10, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 80, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 90, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 142}, {"ind": 144, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 75, "s": [74.218, 48.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 85, "s": [74.218, 40.826], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "35", "w": 20, "h": 22, "ind": 145, "ty": 0, "nm": "A", "sr": 1, "ks": {"p": {"a": 0, "k": [-10, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 75, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 85, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 144}, {"ind": 146, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 70, "s": [54.788, 48.973], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 80, "s": [54.788, 40.973], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "36", "w": 18, "h": 22, "ind": 147, "ty": 0, "nm": "B", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 70, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 80, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 146}, {"ind": 148, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 65, "s": [28.263, 49.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 75, "s": [28.263, 41.016], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "37", "w": 20, "h": 22, "ind": 149, "ty": 0, "nm": "K", "sr": 1, "ks": {"p": {"a": 0, "k": [-10, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 65, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 75, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 148}, {"ind": 150, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 60, "s": [8.423, 49.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 70, "s": [8.423, 41.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "38", "w": 18, "h": 22, "ind": 151, "ty": 0, "nm": "E", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 60, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 70, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 150}, {"ind": 152, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 55, "s": [232.142, 18.767], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 65, "s": [232.142, 10.767], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "39", "w": 28, "h": 22, "ind": 153, "ty": 0, "nm": "W", "sr": 1, "ks": {"p": {"a": 0, "k": [-14, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 55, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 65, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 152}, {"ind": 154, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 50, "s": [207.82, 18.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 60, "s": [207.82, 10.459], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "40", "w": 20, "h": 20, "ind": 155, "ty": 0, "nm": "O", "sr": 1, "ks": {"p": {"a": 0, "k": [-10, -10]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 50, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 60, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 154}, {"ind": 156, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 45, "s": [190.353, 18.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 55, "s": [190.353, 10.723], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "41", "w": 14, "h": 22, "ind": 157, "ty": 0, "nm": "L", "sr": 1, "ks": {"p": {"a": 0, "k": [-7, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 45, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 55, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 156}, {"ind": 158, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 40, "s": [165.732, 19.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 50, "s": [165.732, 11.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "42", "w": 18, "h": 22, "ind": 159, "ty": 0, "nm": "e", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 40, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 50, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 158}, {"ind": 160, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 35, "s": [145.658, 18.562], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 45, "s": [145.658, 10.562], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "43", "w": 20, "h": 22, "ind": 161, "ty": 0, "nm": "N", "sr": 1, "ks": {"p": {"a": 0, "k": [-10, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 35, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 45, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 160}, {"ind": 162, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 30, "s": [126.507, 18.972], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 40, "s": [126.507, 10.972], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "44", "w": 18, "h": 22, "ind": 163, "ty": 0, "nm": "T", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 30, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 40, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 162}, {"ind": 164, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 25, "s": [112.468, 18.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 35, "s": [112.468, 10.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "45", "w": 8, "h": 22, "ind": 165, "ty": 0, "nm": "I", "sr": 1, "ks": {"p": {"a": 0, "k": [-4, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 25, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 35, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 164}, {"ind": 166, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 20, "s": [90.468, 19.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 30, "s": [90.468, 11.046], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "46", "w": 18, "h": 22, "ind": 167, "ty": 0, "nm": "e", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 20, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 30, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 166}, {"ind": 168, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 15, "s": [72.548, 19.28], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 25, "s": [72.548, 11.28], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "47", "w": 16, "h": 22, "ind": 169, "ty": 0, "nm": "C", "sr": 1, "ks": {"p": {"a": 0, "k": [-8, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 15, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 25, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 168}, {"ind": 170, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 10, "s": [59.065, 18.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 20, "s": [59.065, 10.987], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "48", "w": 8, "h": 22, "ind": 171, "ty": 0, "nm": "I", "sr": 1, "ks": {"p": {"a": 0, "k": [-4, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 10, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 20, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 170}, {"ind": 172, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 5, "s": [44.147, 18.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 15, "s": [44.147, 10.664], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "49", "w": 18, "h": 22, "ind": 173, "ty": 0, "nm": "R", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 5, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 15, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 172}, {"ind": 174, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 1, "k": [{"t": 0, "s": [24.806, 20.84], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 10, "s": [24.806, 10.84], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 124}, {"refId": "50", "w": 18, "h": 22, "ind": 175, "ty": 0, "nm": "P", "sr": 1, "ks": {"p": {"a": 0, "k": [-9, -11]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 10, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 174}, {"ind": 176, "ty": 4, "nm": "Curved line.json 1", "sr": 1, "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "nm": "S", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-435.476, 333.506], [-234.26, 164.785], [-345.199, 248.115], [-96.081, 89.164], [-184.019, 138.544], [12.494, 112.267]], "i": [[0, 0], [-110.666, -136.418], [53.251, 64.192], [-76.809, -131.407], [48.454, 68.552], [-105.221, -184.612]], "o": [[-146.266, -181.688], [59.001, 72.731], [-102.137, -123.121], [40.792, 69.789], [-54.054, -76.475], [0, 0]]}}}, {"ty": "tm", "s": {"a": 1, "k": [{"t": 142, "s": [0], "i": {"x": [0.2], "y": [1]}, "o": {"x": [0.2], "y": [0]}}, {"t": 220, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "e": {"a": 1, "k": [{"t": 120, "s": [0], "i": {"x": [0.2], "y": [1]}, "o": {"x": [0.2], "y": [0]}}, {"t": 198, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "o": {"a": 0, "k": 0}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 18}, "lc": 2, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [529.064, 582.844]}, "a": {"a": 0, "k": [-234.666, 176.392]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": -41}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-435.476, 333.506], [-234.26, 164.785], [-345.199, 248.115], [-96.081, 89.164], [-184.019, 138.544], [12.494, 112.267]], "i": [[0, 0], [-110.666, -136.418], [53.251, 64.192], [-76.809, -131.407], [48.454, 68.552], [-105.221, -184.612]], "o": [[-146.266, -181.688], [59.001, 72.731], [-102.137, -123.121], [40.792, 69.789], [-54.054, -76.475], [0, 0]]}}}, {"ty": "tm", "s": {"a": 1, "k": [{"t": 140, "s": [0], "i": {"x": [0.2], "y": [1]}, "o": {"x": [0.21], "y": [0]}}, {"t": 242, "s": [20], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "e": {"a": 1, "k": [{"t": 120, "s": [0], "i": {"x": [0.2], "y": [1]}, "o": {"x": [0.21], "y": [0]}}, {"t": 222, "s": [20], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "o": {"a": 1, "k": [{"t": 120, "s": [0], "i": {"x": [0.2], "y": [1]}, "o": {"x": [0.21], "y": [0]}}, {"t": 222, "s": [20], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 10}, "lc": 2, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [529.064, 582.844]}, "a": {"a": 0, "k": [-234.666, 176.392]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": -41}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 0, "k": [351.625, 183.33]}, "a": {"a": 0, "k": [529.064, 582.844]}, "s": {"a": 0, "k": [11.407, 11.407]}, "r": {"a": 0, "k": 21.513}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}, {"ind": 177, "ty": 4, "nm": "Curved line.json 2", "sr": 1, "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "nm": "S", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-435.476, 333.506], [-234.26, 164.785], [-345.199, 248.115], [-96.081, 89.164], [-184.019, 138.544], [12.494, 112.267]], "i": [[0, 0], [-110.666, -136.418], [53.251, 64.192], [-76.809, -131.407], [48.454, 68.552], [-105.221, -184.612]], "o": [[-146.266, -181.688], [59.001, 72.731], [-102.137, -123.121], [40.792, 69.789], [-54.054, -76.475], [0, 0]]}}}, {"ty": "tm", "s": {"a": 1, "k": [{"t": 142, "s": [0], "i": {"x": [0.2], "y": [1]}, "o": {"x": [0.2], "y": [0]}}, {"t": 220, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "e": {"a": 1, "k": [{"t": 120, "s": [0], "i": {"x": [0.2], "y": [1]}, "o": {"x": [0.2], "y": [0]}}, {"t": 198, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "o": {"a": 0, "k": 0}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 18}, "lc": 2, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [529.064, 582.844]}, "a": {"a": 0, "k": [-234.666, 176.392]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": -41}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-435.476, 333.506], [-234.26, 164.785], [-345.199, 248.115], [-96.081, 89.164], [-184.019, 138.544], [12.494, 112.267]], "i": [[0, 0], [-110.666, -136.418], [53.251, 64.192], [-76.809, -131.407], [48.454, 68.552], [-105.221, -184.612]], "o": [[-146.266, -181.688], [59.001, 72.731], [-102.137, -123.121], [40.792, 69.789], [-54.054, -76.475], [0, 0]]}}}, {"ty": "tm", "s": {"a": 1, "k": [{"t": 140, "s": [0], "i": {"x": [0.2], "y": [1]}, "o": {"x": [0.21], "y": [0]}}, {"t": 242, "s": [20], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "e": {"a": 1, "k": [{"t": 120, "s": [0], "i": {"x": [0.2], "y": [1]}, "o": {"x": [0.21], "y": [0]}}, {"t": 222, "s": [20], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "o": {"a": 1, "k": [{"t": 120, "s": [0], "i": {"x": [0.2], "y": [1]}, "o": {"x": [0.21], "y": [0]}}, {"t": 222, "s": [20], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}]}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 10}, "lc": 2, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [529.064, 582.844]}, "a": {"a": 0, "k": [-234.666, 176.392]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": -41}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "p": {"a": 0, "k": [38.305, 252.726]}, "a": {"a": 0, "k": [529.064, 582.844]}, "s": {"a": 0, "k": [11.407, 11.407]}, "r": {"a": 0, "k": -158.487}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 501, "st": 0}, {"ind": 178, "ty": 3, "nm": "", "sr": 1, "ks": {"p": {"a": 0, "k": [196.5, 207.251]}, "a": {"a": 0, "k": [294.5, 207.251]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0}, {"refId": "54", "w": 394, "h": 415, "ind": 61, "ty": 0, "nm": "SVG", "sr": 1, "ks": {"p": {"a": 0, "k": [97, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 501, "st": 0, "parent": 178}], "markers": []}
class AddressModel {
  String? id;
  String? name;
  String? phone;
  String? fullAddress;
  String? addressLine1;
  String? addressLine2;
  String? city;
  String? state;
  String? pincode;
  String? landmark;
  num? latitude;
  num? longitude;
  String? addressType;
  bool? isDefault;
  bool? isServicable;

  AddressModel({
    this.id,
    this.name,
    this.phone,
    this.fullAddress,
    this.addressLine1,
    this.addressLine2,
    this.city,
    this.state,
    this.pincode,
    this.landmark,
    this.latitude,
    this.longitude,
    this.addressType,
    this.isDefault,
    this.isServicable,
  });

  AddressModel copyWith({
    String? id,
    String? name,
    String? phone,
    String? fullAddress,
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? state,
    String? pincode,
    String? landmark,
    num? latitude,
    num? longitude,
    String? addressType,
    bool? isDefault,
    bool? isServicable,
  }) {
    return AddressModel(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      fullAddress: fullAddress ?? this.fullAddress,
      addressLine1: addressLine1 ?? this.addressLine1,
      addressLine2: addressLine2 ?? this.addressLine2,
      city: city ?? this.city,
      state: state ?? this.state,
      pincode: pincode ?? this.pincode,
      landmark: landmark ?? this.landmark,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      addressType: addressType ?? this.addressType,
      isDefault: isDefault ?? this.isDefault,
      isServicable: isServicable ?? this.isServicable,
    );
  }

  AddressModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    name = json['name']?.toString() ?? json['full_name']?.toString();
    phone = json['phone']?.toString() ?? json['phone_number']?.toString();
    fullAddress = json['fullAddress']?.toString();
    addressLine1 =
        json['addressLine1']?.toString() ?? json['address_line1']?.toString();
    addressLine2 =
        json['addressLine2']?.toString() ?? json['address_line2']?.toString();
    city = json['city']?.toString();
    state = json['state']?.toString();
    pincode = json['pincode']?.toString() ?? json['postal_code']?.toString();
    landmark = json['landmark']?.toString();
    if (json['latitude'] is num) {
      latitude = json['latitude'];
    } else if (json['latitude'] != null) {
      var numb = num.tryParse(json['latitude']!.toString());
      if (numb is num) {
        latitude = numb;
      }
    }
    if (json['longitude'] is num) {
      longitude = json['longitude'];
    } else if (json['longitude'] != null) {
      var numb = num.tryParse(json['longitude']!.toString());
      if (numb is num) {
        longitude = numb;
      }
    }
    addressType =
        json['addressType']?.toString() ?? json['type_of_address']?.toString();
    if (json['isDefault'] is bool) {
      isDefault = json['isDefault'];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (id is String) {
      data['id'] = id;
    }
    if (name is String) {
      data['name'] = name;
    }
    if (phone is String) {
      data['phone'] = phone;
    }
    if (fullAddress is String) {
      data['fullAddress'] = fullAddress;
    }
    if (addressLine1 is String) {
      data['addressLine1'] = addressLine1;
    }
    if (addressLine2 is String) {
      data['addressLine2'] = addressLine2;
    }
    if (city is String) {
      data['city'] = city;
    }
    if (state is String) {
      data['state'] = state;
    }
    if (pincode is String) {
      data['pincode'] = pincode;
    }
    if (landmark is String) {
      data['landmark'] = landmark;
    }
    if (latitude is num) {
      data['latitude'] = latitude;
    } else if (latitude != null) {
      var numb = num.tryParse(latitude.toString());
      if (numb is num) {
        data['latitude'] = numb;
      }
    }
    if (longitude is num) {
      data['longitude'] = longitude;
    } else if (longitude != null) {
      var numb = num.tryParse(longitude.toString());
      if (numb is num) {
        data['longitude'] = numb;
      }
    }
    if (addressType is String) {
      data['addressType'] = addressType;
    }
    if (isDefault is bool) {
      data['isDefault'] = isDefault;
    }
    return data;
  }
}

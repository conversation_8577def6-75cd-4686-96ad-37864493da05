class HomeBanner {
  final String id;
  final String imageUrl;
  final String? collectionId; // To link to a product category on tap
  final String? name; // To link to a product category on tap

  HomeBanner({
    required this.id,
    required this.imageUrl,
    this.collectionId,
    this.name,
  });

  factory HomeBanner.fromJson(Map<String, dynamic> json) {
    return HomeBanner(
      id: json['id'] as String? ?? '',
      imageUrl: json['imageUrl'] as String? ?? '',
      collectionId: json['collectionId'] as String?,
      name: json['name'] as String?,
    );
  }
}

import 'package:rozana/data/services/data_loading_manager.dart';
import 'package:rozana/domain/repositories/home_repository_interface.dart';
import 'package:rozana/domain/entities/banner_entity.dart';
import 'package:rozana/domain/entities/category_entity.dart';
import 'package:rozana/domain/entities/product_entity.dart';

class HomeRepositoryImpl implements IHomeRepository {
  final DataLoadingManager _dataManager;

  // Constructor dependency injection
  HomeRepositoryImpl({required DataLoadingManager dataManager})
      : _dataManager = dataManager;

  @override
  Future<List<CategoryEntity>> getCategories({
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
    String query = '',
    String? level,
  }) async {
    // DataLoadingManager now directly returns CategoryEntity objects
    return await _dataManager.loadCategories(
        page: page, pageSize: pageSize, refresh: refresh, query: query);
  }

  @override
  Future<List<CategoryEntity>> getSubCategories({
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
    String query = '',
    String? level,
  }) async {
    // DataLoadingManager now directly returns CategoryEntity objects
    return await _dataManager.loadSubCategories(
        page: page, pageSize: pageSize, refresh: refresh, query: query);
  }

  @override
  Future<List<ProductEntity>> getProducts({
    int page = 0,
    int pageSize = 9,
    bool refresh = false,
    String query = '',
    String? sectionType,
  }) async {
    // Use loadDynamicProducts when sectionType is provided
    if (sectionType != null && sectionType.isNotEmpty) {
      return await _dataManager.loadDynamicProducts(
        page: page,
        pageSize: pageSize,
        refresh: refresh,
        query: query,
        sectionType: sectionType,
      );
    }

    // Otherwise use the regular loadFeaturedProducts
    return await _dataManager.loadFeaturedProducts(
      page: page,
      pageSize: pageSize,
      refresh: refresh,
      query: query,
    );
  }

  @override
  Future<List<BannerEntity>> getBanners() async {
    // DataLoadingManager now directly returns BannerEntity objects
    return await _dataManager.getBanners();
  }
}

Future fetchDummy(dynamic model) =>
    Future.delayed(Duration(seconds: 1), () => model);

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/widgets/app_card.dart';
import 'package:rozana/widgets/custom_button.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/skeleton_loader_factory.dart';
import 'package:rozana/domain/entities/order_entity.dart';
import '../../bloc/order_bloc.dart';
import '../widgets/order_status_chip.dart';
import '../../../../core/utils/helpers.dart';

class OrderDetailsScreen extends StatefulWidget {
  final String orderId;
  final bool fromOrderSuccess;

  const OrderDetailsScreen({
    super.key,
    required this.orderId,
    this.fromOrderSuccess = false,
  });

  @override
  State<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  @override
  void initState() {
    super.initState();
    // Load order details
    context.read<OrderBloc>().add(OrderEvent.loadOrderDetails(widget.orderId));
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: !widget.fromOrderSuccess,
        onPopInvoked: (_) {
          if (widget.fromOrderSuccess) {
            context.go(RouteNames.home);
          }
        },
        child: Scaffold(
          backgroundColor: AppColors.background,
          appBar: AppBar(
            title: const CustomText(
              'Order Details',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.neutral600,
            ),
            backgroundColor: AppColors.surface,
            elevation: 1,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: AppColors.neutral600),
              onPressed: () {
                if (widget.fromOrderSuccess) {
                  context.go(RouteNames.home);
                } else {
                  context.pop();
                }
              },
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.share, color: AppColors.neutral600),
                onPressed: () => _shareOrderDetails(),
              ),
            ],
          ),
          body: BlocConsumer<OrderBloc, OrderState>(
            listener: (context, state) {
              state.maybeWhen(
                error: (message, orderId) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(message),
                      backgroundColor: AppColors.error,
                    ),
                  );
                },
                orderCancelled: (orderId, message) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(message),
                      backgroundColor: AppColors.success,
                    ),
                  );
                  context.read<OrderBloc>().add(
                        OrderEvent.loadOrderDetails(widget.orderId),
                      );
                },
                orElse: () {},
              );
            },
            builder: (context, state) {
              return state.when(
                initial: () => _buildSkeletonLoader(),
                loading: () => _buildSkeletonLoader(),
                orderDetailsLoaded: (order) => _buildOrderDetails(order),
                error: (message, orderId) => _buildErrorState(message),
                orderHistoryLoaded: (_, __, ___, ____, _____, ______) =>
                    _buildSkeletonLoader(),
                empty: (_, __) => _buildErrorState('Order not found'),
                orderCancelled: (_, __) => _buildSkeletonLoader(),
              );
            },
          ),
        ));
  }

  Widget _buildOrderDetails(OrderEntity order) {
    return RefreshIndicator(
      onRefresh: () async {
        getIt<OrderBloc>().add(OrderEvent.loadOrderDetails(order.id));
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order header
            _buildOrderHeader(order),
            const SizedBox(height: 16),

            // Order timeline
            _buildOrderTimeline(order),
            const SizedBox(height: 16),

            // Order items
            _buildOrderItems(order),
            const SizedBox(height: 16),

            // Delivery address (only show if address exists)
            if (order.deliveryAddress != null &&
                order.deliveryAddress!.phone != null) ...[
              _buildDeliveryAddress(order),
              const SizedBox(height: 16),
            ],

            // Payment details
            _buildPaymentDetails(order),
            const SizedBox(height: 16),

            // Order summary
            _buildOrderSummary(order),
            const SizedBox(height: 24),

            // Action buttons
            _buildActionButtons(order),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderHeader(OrderEntity order) {
    return AppCard(
      backgroundColor: AppColors.surface,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: CustomText(
                  'Order #${order.id}',
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.neutral600,
                ),
              ),
              OrderStatusChip(status: order.status),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16,
                color: AppColors.textSecondary,
              ),
              const SizedBox(width: 4),
              CustomText(
                'Ordered on ${order.formattedOrderDate}',
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ],
          ),
          if (order.estimatedDeliveryTime != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.local_shipping,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 4),
                CustomText(
                  'Estimated delivery: ${formatDeliveryDate(order.estimatedDeliveryTime!)}',
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOrderTimeline(OrderEntity order) {
    return AppCard(
      backgroundColor: AppColors.surface,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CustomText(
            'Order Status',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.neutral600,
          ),
          const SizedBox(height: 16),
          ...order.orderTimeline.asMap().entries.map((entry) {
            final index = entry.key;
            final timeline = entry.value;
            final isLast = index == order.orderTimeline.length - 1;

            return Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                      ),
                    ),
                    if (!isLast)
                      Container(
                        width: 2,
                        height: 40,
                        color: AppColors.textHint,
                      ),
                  ],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                        timeline.title,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.neutral600,
                      ),
                      const SizedBox(height: 4),
                      CustomText(
                        timeline.description,
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(height: 4),
                      CustomText(
                        timeline.formattedDateTime,
                        fontSize: 12,
                        color: AppColors.textHint,
                      ),
                      if (!isLast) const SizedBox(height: 16),
                    ],
                  ),
                ),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildOrderItems(OrderEntity order) {
    return AppCard(
      backgroundColor: AppColors.surface,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            'Items (${order.totalItems})',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.neutral600,
          ),
          const SizedBox(height: 16),
          ...order.items.map((item) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CustomImage(
                      imageUrl: item.imageUrl ??
                          'assets/images/image-placeholder.jpg',
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                          item.name,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.neutral600,
                        ),
                        const SizedBox(height: 4),
                        CustomText(
                          '${item.quantity} ${item.unit}',
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                        if (item.facilityName != null) ...[
                          const SizedBox(height: 4),
                          CustomText(
                            'From ${item.facilityName}',
                            fontSize: 12,
                            color: AppColors.textHint,
                          ),
                        ],
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      CustomText(
                        '₹${item.totalPrice.toStringAsFixed(2)}',
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.neutral600,
                      ),
                      if (item.hasDiscount) ...[
                        const SizedBox(height: 2),
                        Text(
                          '₹${(item.price * item.quantity).toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.textHint,
                            decoration: TextDecoration.lineThrough,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildDeliveryAddress(OrderEntity order) {
    final address = order.deliveryAddress;

    // This method should only be called when address is not null
    if (address == null) {
      return const SizedBox.shrink();
    }

    return AppCard(
      backgroundColor: AppColors.surface,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CustomText(
            'Delivery Address',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.neutral600,
          ),
          const SizedBox(height: 12),
          CustomText(
            address.name ?? '',
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.neutral600,
          ),
          const SizedBox(height: 4),
          CustomText(
            '${address.addressLine1 ?? ''}, ${address.addressLine2 ?? ''}',
            fontSize: 14,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 4),
          CustomText(
            '${address.city ?? ''}, ${address.state ?? ''} - ${address.pincode ?? ''}',
            fontSize: 14,
            color: AppColors.textSecondary,
          ),
          if (address.phone != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.phone,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 4),
                CustomText(
                  address.phone!,
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentDetails(OrderEntity order) {
    return AppCard(
      backgroundColor: AppColors.surface,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CustomText(
            'Payment Details',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.neutral600,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.payment,
                size: 16,
                color: AppColors.textSecondary,
              ),
              const SizedBox(width: 8),
              CustomText(
                order.paymentMethod,
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummary(OrderEntity order) {
    double suboTotal = 0;
    double tax = 0;
    double deliveryFee = 0;
    order.items.map((e) {
      suboTotal = suboTotal + (e.totalPrice);
    }).toList();

    if (suboTotal <= 0) {
      deliveryFee = 0.0; // No delivery fee for empty cart
    } else if (suboTotal < 200) {
      deliveryFee = 60.0; // ₹40 base + ₹20 small order fee
    } else if (suboTotal < 500) {
      deliveryFee = 40.0; // Base delivery fee
    } // else free delivery (₹0)
    tax = (suboTotal * 0.05);
    return AppCard(
      backgroundColor: AppColors.surface,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CustomText(
            'Order Summary',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.neutral600,
          ),
          const SizedBox(height: 16),
          _buildSummaryRow('Subtotal', '₹${suboTotal.toStringAsFixed(2)}'),
          _buildSummaryRow('Tax', '₹${tax.toStringAsFixed(2)}'),
          _buildSummaryRow(
              'Delivery Fee', '₹${deliveryFee.toStringAsFixed(2)}'),
          if (order.discount > 0)
            _buildSummaryRow(
                'Discount', '-₹${order.discount.toStringAsFixed(2)}',
                color: AppColors.success),
          const Divider(height: 24, color: AppColors.textHint),
          _buildSummaryRow(
            'Total Amount',
            '₹${order.totalAmount.toStringAsFixed(2)}',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value,
      {Color? color, bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CustomText(
            label,
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
            color: color ?? AppColors.textSecondary,
          ),
          CustomText(
            value,
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
            color: color ??
                (isTotal ? AppColors.neutral600 : AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(OrderEntity order) {
    return Column(
      children: [
        if (order.canCancel) ...[
          SizedBox(
            width: double.infinity,
            child: AppButton(
              text: 'Cancel Order',
              onPressed: () => _handleCancelOrder(order.id),
              backgroundColor: AppColors.error,
              textColor: AppColors.surface,
              borderRadius: 8,
              height: 48,
            ),
          ),
          const SizedBox(height: 12),
        ],
        if (order.canReorder) ...[
          SizedBox(
            width: double.infinity,
            child: AppButton(
              text: 'Reorder',
              onPressed: () => _handleReorder(order.id),
              isOutlined: true,
              backgroundColor: AppColors.primary,
              textColor: AppColors.primary,
              borderRadius: 8,
              height: 48,
            ),
          ),
          const SizedBox(height: 12),
        ],
        SizedBox(
          width: double.infinity,
          child: AppButton(
            text: 'Need Help?',
            onPressed: () => _contactSupport(),
            isOutlined: true,
            backgroundColor: AppColors.textSecondary,
            textColor: AppColors.textSecondary,
            borderRadius: 8,
            height: 48,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            const CustomText(
              'Order Not Found',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.neutral600,
            ),
            const SizedBox(height: 8),
            CustomText(
              message,
              fontSize: 14,
              color: AppColors.textSecondary,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            AppButton(
              text: 'Go Back',
              onPressed: () => context.pop(),
              backgroundColor: AppColors.primary,
              textColor: AppColors.surface,
              borderRadius: 8,
            ),
          ],
        ),
      ),
    );
  }

  void _handleCancelOrder(String orderId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const CustomText(
          'Cancel Order',
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
        content: const CustomText(
          'Are you sure you want to cancel this order? This action cannot be undone.',
          fontSize: 14,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: CustomText(
              'No',
              color: AppColors.textSecondary,
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<OrderBloc>().add(OrderEvent.cancelOrder(orderId));
            },
            child: CustomText(
              'Yes, Cancel Order',
              color: AppColors.error,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  void _handleReorder(String orderId) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Reorder functionality will be implemented'),
      ),
    );
  }

  void _shareOrderDetails() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality will be implemented'),
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    return SingleChildScrollView(
      child: SkeletonLoaderFactory.createOrderDetailsSkeleton(),
    );
  }

  void _contactSupport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Contact support functionality will be implemented'),
      ),
    );
  }
}

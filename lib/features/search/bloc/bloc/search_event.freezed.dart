// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'search_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SearchEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SearchEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SearchEvent()';
  }
}

/// @nodoc
class $SearchEventCopyWith<$Res> {
  $SearchEventCopyWith(SearchEvent _, $Res Function(SearchEvent) __);
}

/// Adds pattern-matching-related methods to [SearchEvent].
extension SearchEventPatterns on SearchEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Search value)? search,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ClearSearch value)? clearSearch,
    TResult Function(_ClearRecent value)? clearRecent,
    TResult Function(_RemoveRecent value)? removeRecent,
    TResult Function(_SelectRecent value)? selectRecent,
    TResult Function(_SelectSuggestion value)? selectSuggestion,
    TResult Function(_InputChange value)? inputChange,
    TResult Function(_UpdateSuggestions value)? updateSuggestions,
    TResult Function(_ClearState value)? clearState,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Init() when init != null:
        return init(_that);
      case _Search() when search != null:
        return search(_that);
      case _LoadMore() when loadMore != null:
        return loadMore(_that);
      case _ClearSearch() when clearSearch != null:
        return clearSearch(_that);
      case _ClearRecent() when clearRecent != null:
        return clearRecent(_that);
      case _RemoveRecent() when removeRecent != null:
        return removeRecent(_that);
      case _SelectRecent() when selectRecent != null:
        return selectRecent(_that);
      case _SelectSuggestion() when selectSuggestion != null:
        return selectSuggestion(_that);
      case _InputChange() when inputChange != null:
        return inputChange(_that);
      case _UpdateSuggestions() when updateSuggestions != null:
        return updateSuggestions(_that);
      case _ClearState() when clearState != null:
        return clearState(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Search value) search,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ClearSearch value) clearSearch,
    required TResult Function(_ClearRecent value) clearRecent,
    required TResult Function(_RemoveRecent value) removeRecent,
    required TResult Function(_SelectRecent value) selectRecent,
    required TResult Function(_SelectSuggestion value) selectSuggestion,
    required TResult Function(_InputChange value) inputChange,
    required TResult Function(_UpdateSuggestions value) updateSuggestions,
    required TResult Function(_ClearState value) clearState,
  }) {
    final _that = this;
    switch (_that) {
      case _Init():
        return init(_that);
      case _Search():
        return search(_that);
      case _LoadMore():
        return loadMore(_that);
      case _ClearSearch():
        return clearSearch(_that);
      case _ClearRecent():
        return clearRecent(_that);
      case _RemoveRecent():
        return removeRecent(_that);
      case _SelectRecent():
        return selectRecent(_that);
      case _SelectSuggestion():
        return selectSuggestion(_that);
      case _InputChange():
        return inputChange(_that);
      case _UpdateSuggestions():
        return updateSuggestions(_that);
      case _ClearState():
        return clearState(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Search value)? search,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ClearSearch value)? clearSearch,
    TResult? Function(_ClearRecent value)? clearRecent,
    TResult? Function(_RemoveRecent value)? removeRecent,
    TResult? Function(_SelectRecent value)? selectRecent,
    TResult? Function(_SelectSuggestion value)? selectSuggestion,
    TResult? Function(_InputChange value)? inputChange,
    TResult? Function(_UpdateSuggestions value)? updateSuggestions,
    TResult? Function(_ClearState value)? clearState,
  }) {
    final _that = this;
    switch (_that) {
      case _Init() when init != null:
        return init(_that);
      case _Search() when search != null:
        return search(_that);
      case _LoadMore() when loadMore != null:
        return loadMore(_that);
      case _ClearSearch() when clearSearch != null:
        return clearSearch(_that);
      case _ClearRecent() when clearRecent != null:
        return clearRecent(_that);
      case _RemoveRecent() when removeRecent != null:
        return removeRecent(_that);
      case _SelectRecent() when selectRecent != null:
        return selectRecent(_that);
      case _SelectSuggestion() when selectSuggestion != null:
        return selectSuggestion(_that);
      case _InputChange() when inputChange != null:
        return inputChange(_that);
      case _UpdateSuggestions() when updateSuggestions != null:
        return updateSuggestions(_that);
      case _ClearState() when clearState != null:
        return clearState(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String initialQuery, Map<String, dynamic>? dynamicQuery)?
        init,
    TResult Function(String query, Map<String, dynamic>? dynamicQuery,
            bool submit, String? sku)?
        search,
    TResult Function()? loadMore,
    TResult Function()? clearSearch,
    TResult Function()? clearRecent,
    TResult Function(int index)? removeRecent,
    TResult Function(String query)? selectRecent,
    TResult Function(String suggestion, String? sku)? selectSuggestion,
    TResult Function(String query)? inputChange,
    TResult Function(String query)? updateSuggestions,
    TResult Function()? clearState,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Init() when init != null:
        return init(_that.initialQuery, _that.dynamicQuery);
      case _Search() when search != null:
        return search(_that.query, _that.dynamicQuery, _that.submit, _that.sku);
      case _LoadMore() when loadMore != null:
        return loadMore();
      case _ClearSearch() when clearSearch != null:
        return clearSearch();
      case _ClearRecent() when clearRecent != null:
        return clearRecent();
      case _RemoveRecent() when removeRecent != null:
        return removeRecent(_that.index);
      case _SelectRecent() when selectRecent != null:
        return selectRecent(_that.query);
      case _SelectSuggestion() when selectSuggestion != null:
        return selectSuggestion(_that.suggestion, _that.sku);
      case _InputChange() when inputChange != null:
        return inputChange(_that.query);
      case _UpdateSuggestions() when updateSuggestions != null:
        return updateSuggestions(_that.query);
      case _ClearState() when clearState != null:
        return clearState();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String initialQuery, Map<String, dynamic>? dynamicQuery)
        init,
    required TResult Function(String query, Map<String, dynamic>? dynamicQuery,
            bool submit, String? sku)
        search,
    required TResult Function() loadMore,
    required TResult Function() clearSearch,
    required TResult Function() clearRecent,
    required TResult Function(int index) removeRecent,
    required TResult Function(String query) selectRecent,
    required TResult Function(String suggestion, String? sku) selectSuggestion,
    required TResult Function(String query) inputChange,
    required TResult Function(String query) updateSuggestions,
    required TResult Function() clearState,
  }) {
    final _that = this;
    switch (_that) {
      case _Init():
        return init(_that.initialQuery, _that.dynamicQuery);
      case _Search():
        return search(_that.query, _that.dynamicQuery, _that.submit, _that.sku);
      case _LoadMore():
        return loadMore();
      case _ClearSearch():
        return clearSearch();
      case _ClearRecent():
        return clearRecent();
      case _RemoveRecent():
        return removeRecent(_that.index);
      case _SelectRecent():
        return selectRecent(_that.query);
      case _SelectSuggestion():
        return selectSuggestion(_that.suggestion, _that.sku);
      case _InputChange():
        return inputChange(_that.query);
      case _UpdateSuggestions():
        return updateSuggestions(_that.query);
      case _ClearState():
        return clearState();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String initialQuery, Map<String, dynamic>? dynamicQuery)?
        init,
    TResult? Function(String query, Map<String, dynamic>? dynamicQuery,
            bool submit, String? sku)?
        search,
    TResult? Function()? loadMore,
    TResult? Function()? clearSearch,
    TResult? Function()? clearRecent,
    TResult? Function(int index)? removeRecent,
    TResult? Function(String query)? selectRecent,
    TResult? Function(String suggestion, String? sku)? selectSuggestion,
    TResult? Function(String query)? inputChange,
    TResult? Function(String query)? updateSuggestions,
    TResult? Function()? clearState,
  }) {
    final _that = this;
    switch (_that) {
      case _Init() when init != null:
        return init(_that.initialQuery, _that.dynamicQuery);
      case _Search() when search != null:
        return search(_that.query, _that.dynamicQuery, _that.submit, _that.sku);
      case _LoadMore() when loadMore != null:
        return loadMore();
      case _ClearSearch() when clearSearch != null:
        return clearSearch();
      case _ClearRecent() when clearRecent != null:
        return clearRecent();
      case _RemoveRecent() when removeRecent != null:
        return removeRecent(_that.index);
      case _SelectRecent() when selectRecent != null:
        return selectRecent(_that.query);
      case _SelectSuggestion() when selectSuggestion != null:
        return selectSuggestion(_that.suggestion, _that.sku);
      case _InputChange() when inputChange != null:
        return inputChange(_that.query);
      case _UpdateSuggestions() when updateSuggestions != null:
        return updateSuggestions(_that.query);
      case _ClearState() when clearState != null:
        return clearState();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Init implements SearchEvent {
  const _Init(this.initialQuery, final Map<String, dynamic>? dynamicQuery)
      : _dynamicQuery = dynamicQuery;

  final String initialQuery;
  final Map<String, dynamic>? _dynamicQuery;
  Map<String, dynamic>? get dynamicQuery {
    final value = _dynamicQuery;
    if (value == null) return null;
    if (_dynamicQuery is EqualUnmodifiableMapView) return _dynamicQuery;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InitCopyWith<_Init> get copyWith =>
      __$InitCopyWithImpl<_Init>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Init &&
            (identical(other.initialQuery, initialQuery) ||
                other.initialQuery == initialQuery) &&
            const DeepCollectionEquality()
                .equals(other._dynamicQuery, _dynamicQuery));
  }

  @override
  int get hashCode => Object.hash(runtimeType, initialQuery,
      const DeepCollectionEquality().hash(_dynamicQuery));

  @override
  String toString() {
    return 'SearchEvent.init(initialQuery: $initialQuery, dynamicQuery: $dynamicQuery)';
  }
}

/// @nodoc
abstract mixin class _$InitCopyWith<$Res>
    implements $SearchEventCopyWith<$Res> {
  factory _$InitCopyWith(_Init value, $Res Function(_Init) _then) =
      __$InitCopyWithImpl;
  @useResult
  $Res call({String initialQuery, Map<String, dynamic>? dynamicQuery});
}

/// @nodoc
class __$InitCopyWithImpl<$Res> implements _$InitCopyWith<$Res> {
  __$InitCopyWithImpl(this._self, this._then);

  final _Init _self;
  final $Res Function(_Init) _then;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? initialQuery = null,
    Object? dynamicQuery = freezed,
  }) {
    return _then(_Init(
      null == initialQuery
          ? _self.initialQuery
          : initialQuery // ignore: cast_nullable_to_non_nullable
              as String,
      freezed == dynamicQuery
          ? _self._dynamicQuery
          : dynamicQuery // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _Search implements SearchEvent {
  const _Search(this.query,
      {final Map<String, dynamic>? dynamicQuery = null,
      this.submit = false,
      this.sku})
      : _dynamicQuery = dynamicQuery;

  final String query;
  final Map<String, dynamic>? _dynamicQuery;
  @JsonKey()
  Map<String, dynamic>? get dynamicQuery {
    final value = _dynamicQuery;
    if (value == null) return null;
    if (_dynamicQuery is EqualUnmodifiableMapView) return _dynamicQuery;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @JsonKey()
  final bool submit;
  final String? sku;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SearchCopyWith<_Search> get copyWith =>
      __$SearchCopyWithImpl<_Search>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Search &&
            (identical(other.query, query) || other.query == query) &&
            const DeepCollectionEquality()
                .equals(other._dynamicQuery, _dynamicQuery) &&
            (identical(other.submit, submit) || other.submit == submit) &&
            (identical(other.sku, sku) || other.sku == sku));
  }

  @override
  int get hashCode => Object.hash(runtimeType, query,
      const DeepCollectionEquality().hash(_dynamicQuery), submit, sku);

  @override
  String toString() {
    return 'SearchEvent.search(query: $query, dynamicQuery: $dynamicQuery, submit: $submit, sku: $sku)';
  }
}

/// @nodoc
abstract mixin class _$SearchCopyWith<$Res>
    implements $SearchEventCopyWith<$Res> {
  factory _$SearchCopyWith(_Search value, $Res Function(_Search) _then) =
      __$SearchCopyWithImpl;
  @useResult
  $Res call(
      {String query,
      Map<String, dynamic>? dynamicQuery,
      bool submit,
      String? sku});
}

/// @nodoc
class __$SearchCopyWithImpl<$Res> implements _$SearchCopyWith<$Res> {
  __$SearchCopyWithImpl(this._self, this._then);

  final _Search _self;
  final $Res Function(_Search) _then;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? query = null,
    Object? dynamicQuery = freezed,
    Object? submit = null,
    Object? sku = freezed,
  }) {
    return _then(_Search(
      null == query
          ? _self.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
      dynamicQuery: freezed == dynamicQuery
          ? _self._dynamicQuery
          : dynamicQuery // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      submit: null == submit
          ? _self.submit
          : submit // ignore: cast_nullable_to_non_nullable
              as bool,
      sku: freezed == sku
          ? _self.sku
          : sku // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _LoadMore implements SearchEvent {
  const _LoadMore();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _LoadMore);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SearchEvent.loadMore()';
  }
}

/// @nodoc

class _ClearSearch implements SearchEvent {
  const _ClearSearch();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ClearSearch);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SearchEvent.clearSearch()';
  }
}

/// @nodoc

class _ClearRecent implements SearchEvent {
  const _ClearRecent();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ClearRecent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SearchEvent.clearRecent()';
  }
}

/// @nodoc

class _RemoveRecent implements SearchEvent {
  const _RemoveRecent(this.index);

  final int index;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RemoveRecentCopyWith<_RemoveRecent> get copyWith =>
      __$RemoveRecentCopyWithImpl<_RemoveRecent>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RemoveRecent &&
            (identical(other.index, index) || other.index == index));
  }

  @override
  int get hashCode => Object.hash(runtimeType, index);

  @override
  String toString() {
    return 'SearchEvent.removeRecent(index: $index)';
  }
}

/// @nodoc
abstract mixin class _$RemoveRecentCopyWith<$Res>
    implements $SearchEventCopyWith<$Res> {
  factory _$RemoveRecentCopyWith(
          _RemoveRecent value, $Res Function(_RemoveRecent) _then) =
      __$RemoveRecentCopyWithImpl;
  @useResult
  $Res call({int index});
}

/// @nodoc
class __$RemoveRecentCopyWithImpl<$Res>
    implements _$RemoveRecentCopyWith<$Res> {
  __$RemoveRecentCopyWithImpl(this._self, this._then);

  final _RemoveRecent _self;
  final $Res Function(_RemoveRecent) _then;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? index = null,
  }) {
    return _then(_RemoveRecent(
      null == index
          ? _self.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _SelectRecent implements SearchEvent {
  const _SelectRecent(this.query);

  final String query;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SelectRecentCopyWith<_SelectRecent> get copyWith =>
      __$SelectRecentCopyWithImpl<_SelectRecent>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SelectRecent &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(runtimeType, query);

  @override
  String toString() {
    return 'SearchEvent.selectRecent(query: $query)';
  }
}

/// @nodoc
abstract mixin class _$SelectRecentCopyWith<$Res>
    implements $SearchEventCopyWith<$Res> {
  factory _$SelectRecentCopyWith(
          _SelectRecent value, $Res Function(_SelectRecent) _then) =
      __$SelectRecentCopyWithImpl;
  @useResult
  $Res call({String query});
}

/// @nodoc
class __$SelectRecentCopyWithImpl<$Res>
    implements _$SelectRecentCopyWith<$Res> {
  __$SelectRecentCopyWithImpl(this._self, this._then);

  final _SelectRecent _self;
  final $Res Function(_SelectRecent) _then;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? query = null,
  }) {
    return _then(_SelectRecent(
      null == query
          ? _self.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _SelectSuggestion implements SearchEvent {
  const _SelectSuggestion(this.suggestion, this.sku);

  final String suggestion;
  final String? sku;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SelectSuggestionCopyWith<_SelectSuggestion> get copyWith =>
      __$SelectSuggestionCopyWithImpl<_SelectSuggestion>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SelectSuggestion &&
            (identical(other.suggestion, suggestion) ||
                other.suggestion == suggestion) &&
            (identical(other.sku, sku) || other.sku == sku));
  }

  @override
  int get hashCode => Object.hash(runtimeType, suggestion, sku);

  @override
  String toString() {
    return 'SearchEvent.selectSuggestion(suggestion: $suggestion, sku: $sku)';
  }
}

/// @nodoc
abstract mixin class _$SelectSuggestionCopyWith<$Res>
    implements $SearchEventCopyWith<$Res> {
  factory _$SelectSuggestionCopyWith(
          _SelectSuggestion value, $Res Function(_SelectSuggestion) _then) =
      __$SelectSuggestionCopyWithImpl;
  @useResult
  $Res call({String suggestion, String? sku});
}

/// @nodoc
class __$SelectSuggestionCopyWithImpl<$Res>
    implements _$SelectSuggestionCopyWith<$Res> {
  __$SelectSuggestionCopyWithImpl(this._self, this._then);

  final _SelectSuggestion _self;
  final $Res Function(_SelectSuggestion) _then;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? suggestion = null,
    Object? sku = freezed,
  }) {
    return _then(_SelectSuggestion(
      null == suggestion
          ? _self.suggestion
          : suggestion // ignore: cast_nullable_to_non_nullable
              as String,
      freezed == sku
          ? _self.sku
          : sku // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _InputChange implements SearchEvent {
  const _InputChange(this.query);

  final String query;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InputChangeCopyWith<_InputChange> get copyWith =>
      __$InputChangeCopyWithImpl<_InputChange>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InputChange &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(runtimeType, query);

  @override
  String toString() {
    return 'SearchEvent.inputChange(query: $query)';
  }
}

/// @nodoc
abstract mixin class _$InputChangeCopyWith<$Res>
    implements $SearchEventCopyWith<$Res> {
  factory _$InputChangeCopyWith(
          _InputChange value, $Res Function(_InputChange) _then) =
      __$InputChangeCopyWithImpl;
  @useResult
  $Res call({String query});
}

/// @nodoc
class __$InputChangeCopyWithImpl<$Res> implements _$InputChangeCopyWith<$Res> {
  __$InputChangeCopyWithImpl(this._self, this._then);

  final _InputChange _self;
  final $Res Function(_InputChange) _then;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? query = null,
  }) {
    return _then(_InputChange(
      null == query
          ? _self.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _UpdateSuggestions implements SearchEvent {
  const _UpdateSuggestions(this.query);

  final String query;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdateSuggestionsCopyWith<_UpdateSuggestions> get copyWith =>
      __$UpdateSuggestionsCopyWithImpl<_UpdateSuggestions>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateSuggestions &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(runtimeType, query);

  @override
  String toString() {
    return 'SearchEvent.updateSuggestions(query: $query)';
  }
}

/// @nodoc
abstract mixin class _$UpdateSuggestionsCopyWith<$Res>
    implements $SearchEventCopyWith<$Res> {
  factory _$UpdateSuggestionsCopyWith(
          _UpdateSuggestions value, $Res Function(_UpdateSuggestions) _then) =
      __$UpdateSuggestionsCopyWithImpl;
  @useResult
  $Res call({String query});
}

/// @nodoc
class __$UpdateSuggestionsCopyWithImpl<$Res>
    implements _$UpdateSuggestionsCopyWith<$Res> {
  __$UpdateSuggestionsCopyWithImpl(this._self, this._then);

  final _UpdateSuggestions _self;
  final $Res Function(_UpdateSuggestions) _then;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? query = null,
  }) {
    return _then(_UpdateSuggestions(
      null == query
          ? _self.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _ClearState implements SearchEvent {
  const _ClearState();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ClearState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SearchEvent.clearState()';
  }
}

// dart format on

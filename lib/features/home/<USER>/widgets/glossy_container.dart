import 'package:flutter/material.dart';
import 'package:rozana/widgets/custom_image.dart';

import '../../../../widgets/custom_text.dart';

class AppBarCategoryBox extends StatelessWidget {
  const AppBarCategoryBox({
    super.key,
    required this.imagePath,
    this.onTap,
    required this.isSelected,
    required this.categoryName,
    required this.iconPrimaryColor,
    required this.iconSecondaryColor,
  });

  final String imagePath;
  final void Function()? onTap;
  final bool isSelected;
  final String categoryName;

  final Color iconPrimaryColor;
  final Color iconSecondaryColor;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: IntrinsicWidth(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 24,
              height: 24,
              child: CustomImage(
                imageUrl: imagePath,
                imageColor: isSelected ? iconPrimaryColor : iconSecondaryColor,
              ),
            ),
            Sized<PERSON><PERSON>(height: 2),
            CustomText(
              categoryName,
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w800 : FontWeight.w700,
              color: isSelected ? iconPrimaryColor : iconSecondaryColor,
            ),
            Container(
              height: 3,
              margin: EdgeInsets.only(top: 3),
              decoration: BoxDecoration(
                color: isSelected ? iconPrimaryColor : Colors.transparent,
              ),
            )
          ],
        ),
      ),
    );
  }
}

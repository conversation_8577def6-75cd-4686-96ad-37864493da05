import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/extensions/localization_extension.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/features/location/bloc/location%20bloc/location_bloc.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/app/bloc/app_bloc.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../data/models/adress_model.dart';
import '../../../../widgets/custom_text.dart';

class RefreshableAddressBar extends StatelessWidget {
  final double height;
  final EdgeInsetsGeometry padding;
  final Color backgroundColor;
  final Color textColor;
  final ValueChanged<AddressModel?>? onAddressChanged;

  final Color iconPrimaryColor;
  final Color iconSecondaryColor;

  const RefreshableAddressBar({
    super.key,
    this.height = 60,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    this.backgroundColor = Colors.transparent,
    this.textColor = Colors.white,
    this.onAddressChanged,
    required this.iconPrimaryColor,
    required this.iconSecondaryColor,
  });

  void _handleTap(BuildContext context) async {
    // Check if user is authenticated first
    final appState = context.read<AppBloc>().state;
    final isAuthenticated = appState.maybeMap(
      loaded: (loaded) => loaded.isAuthenticated,
      orElse: () => false,
    );

    if (!isAuthenticated) {
      // Redirect to login screen with return path to addresses
      if (context.mounted) {
        // context.push(RouteNames.login, extra: {
        //   'returnRoute': RouteNames.addresses,
        // });
        context
            .push(
          RouteNames.mapForNewAddress,
        )
            .then((_) {
          // _loadAddresses();
        });
      }
      return;
    }

    // For authenticated users, navigate to address list or add address screen
    final addressService = context.read<LocationBloc>().addressService;
    final addresses =
        await addressService.getAllAddresses(); // Uses cached data

    // Check if the widget is still mounted before using context
    if (context.mounted) {
      if (addresses.isEmpty) {
        context.push(RouteNames.mapForNewAddress);
      } else {
        // Pass onAddressSelected callback to automatically return after selection
        context.push(RouteNames.addresses, extra: {
          'onAddressSelected': (AddressModel selectedAddress) {
            final locationBloc = context.read<LocationBloc>();
            locationBloc.refreshUI();
            context.pop();
          },
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<LocationBloc, LocationState>(
      listener: (context, state) {
        state.maybeWhen(
          loaded: (address) {
            if (onAddressChanged != null) {
              onAddressChanged!(address);
            }
          },
          orElse: () {},
        );
      },
      builder: (context, state) {
        String address = '';
        String addressType = '';
        String addressCity = '';
        bool isRefreshing = false;
        state.maybeWhen(
          loaded: (addressData) {
            address = addressData.fullAddress ?? '';
            addressType = addressData.addressType ?? 'Home';
            addressCity = addressData.city ?? '';
          },
          loading: () {
            address = 'Detecting location...';
          },
          notServiceable: (addressData) {
            // Show the address even when it's not serviceable
            address = addressData.fullAddress ?? '';
            addressType = addressData.addressType ?? 'Location';
          },
          orElse: () {
            address = 'Select an address';
          },
        );

        return InkWell(
          onTap: () => _handleTap(context),
          child: Container(
            padding: AppDimensions.screenPadding.copyWith(top: 0, bottom: 0),
            color: backgroundColor,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    context.push(RouteNames.profile);
                  },
                  child: CircleAvatar(
                    radius: 18,
                    backgroundColor: AppColors.secondary100,
                    child: BlocBuilder<AppBloc, AppState>(
                      builder: (context, state) {
                        Map<String, dynamic> userData = {};

                        if (getIt<AppBloc>().isAuthenticated) {
                          String? userJson = AppPreferences.getUserdata();
                          userData = jsonDecode(userJson ?? '');
                        }
                        String? initial = userData['displayName']
                            ?.toString()
                            .toUpperCase()
                            .split('')
                            .first;
                        return Center(
                            child: (initial ?? '').isNotEmpty
                                ? CustomText(
                                    initial!,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w800,
                                    color: AppColors.secondary700,
                                  )
                                : Image.asset(
                                    'assets/new/icons/person.png',
                                    width: 24,
                                    height: 24,
                                  ));
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CustomText(
                            // TextFormatter.formatToUiText(addressType),
                            context.l10n.nextDayDelivery,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: iconPrimaryColor,
                          ),
                          SizedBox(width: 2),
                          Image.asset(
                            'assets/new/icons/shield-tick.png',
                            width: 12,
                            height: 12,
                          ),
                        ],
                      ),
                      SizedBox(height: 4),
                      Row(
                        children: [
                          Flexible(
                            child: CustomText(
                              address,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              overflow: TextOverflow.ellipsis,
                              color: iconSecondaryColor,
                            ),
                          ),
                          Visibility(
                            visible: address.isNotEmpty,
                            child: Flexible(
                              child: CustomText(
                                " ⸱ $addressCity",
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                overflow: TextOverflow.ellipsis,
                                color: iconSecondaryColor,
                              ),
                            ),
                          ),
                          SizedBox.square(
                            dimension: 24,
                            child: Icon(
                              Icons.arrow_drop_down_outlined,
                              size: 16,
                              color: iconSecondaryColor,
                            ),
                          )
                        ],
                      ),
                    ],
                  ),
                ),
                if (addressType.toLowerCase() == 'current')
                  StatefulBuilder(builder: (context, setState) {
                    return CircleAvatar(
                      backgroundColor: AppColors.primary.withValues(alpha: 0.2),
                      child: IconButton(
                        icon: isRefreshing
                            ? SizedBox(
                                width: 14,
                                height: 14,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: AppColors.white,
                                ),
                              )
                            : Icon(
                                Icons.refresh,
                                size: 16,
                                color: AppColors.white,
                              ),
                        onPressed: () {
                          if (!isRefreshing) {
                            setState(
                              () {
                                isRefreshing = true;
                              },
                            );
                            getIt<LocationBloc>()
                                .add(LocationEvent.refreshLocation());
                          }
                        },
                        padding: EdgeInsets.zero,
                        tooltip: 'Refresh location',
                        constraints: const BoxConstraints(),
                      ),
                    );
                  }),
              ],
            ),
          ),
        );
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/domain/entities/banner_entity.dart';
import 'package:rozana/domain/entities/category_entity.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/app_carousel.dart';
import 'package:rozana/widgets/skeleton_loader_factory.dart';
import 'package:rozana/widgets/shimmer_widgets.dart';

import '../../../categories/bloc/categories_bloc.dart';

class BannerSection extends StatelessWidget {
  const BannerSection({
    super.key,
    required this.banners,
    this.topPadding = 30,
    this.showIndicator = true,
    this.height = 220,
    this.parentCategory,
  });
  final double topPadding;
  final bool showIndicator;
  final double height;

  final List<BannerEntity>? banners;
  final CategoryEntity? parentCategory;

  @override
  Widget build(BuildContext context) {
    // Return empty widget if banners list is empty and not null
    if (banners != null && banners!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(top: topPadding),
      // AnimatedSwitcher for smooth transition between skeleton and content
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        switchInCurve: Curves.easeInOut,
        switchOutCurve: Curves.easeInOut,
        child: banners == null
            ? _buildSkeletonLoader(context)
            : AppCarousel<BannerEntity?>(
                key: const ValueKey('loaded_banner_carousel'),
                items: banners!,
                height: height,
                viewportFraction: 0.92,
                borderRadius: 12,
                autoPlay: (banners?.length ?? 0) > 1,
                infiniteScroll: (banners?.length ?? 0) > 1,
                autoPlayInterval: const Duration(seconds: 4),
                indicatorType: CarouselIndicatorType.dottedBar,
                showIndicator: showIndicator,
                itemBuilder: (context, banner, index) {
                  return Container(
                    padding: EdgeInsets.all(4),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24),
                        color: Colors.transparent,
                        border:
                            Border.all(color: Color(0xFF767676), width: 1.5)),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: Image.network(
                        banner?.imageUrl ?? '',
                        fit: BoxFit.fill,
                        errorBuilder: (context, error, stackTrace) =>
                            ShimmerBox(),
                      ),
                    ),
                  );
                },
                onItemTap: (index) async {
                  HapticFeedback.lightImpact();

                  String displayPage = parentCategory?.displayPage ?? '';

                  if (displayPage.isEmpty) {
                    CategoriesBloc.loadedSubcategories.clear();
                    context.push(RouteNames.categories, extra: {
                      'category': parentCategory,
                    });
                  } else if (RouteNames.products.contains(displayPage)) {
                    CategoriesBloc.loadedSubcategories.clear();
                    Map<String, dynamic> extras = {};
                    extras['category'] = parentCategory;

                    extras['query'] = {
                      'query': parentCategory?.query,
                      'collection': parentCategory?.queryCollection
                    };

                    context.push(
                      RouteNames.products,
                      extra: extras,
                    );
                  } else if (RouteNames.search.contains(displayPage)) {
                    context.push('${RouteNames.search}?initialQuery=', extra: {
                      'query': parentCategory?.query,
                      'collection': parentCategory?.queryCollection
                    });
                  } else {
                    CategoriesBloc.loadedSubcategories.clear();
                    context.push(RouteNames.categories, extra: {
                      'category': parentCategory,
                    });
                  }
                },
              ),
      ),
    );
  }

  // Enhanced skeleton loader for banners with staggered animation effect
  Widget _buildSkeletonLoader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 30),
      child: Column(
        children: [
          Container(
            height: 220,
            margin: const EdgeInsets.symmetric(horizontal: 16),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              // Using the standardized skeleton loader factory
              child: SkeletonLoaderFactory.createBannerSkeleton(
                height: 220,
                radius: 12,
              ),
            ),
          ),
          const SizedBox(height: 8),
          // Dots indicator skeleton with staggered effect
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              3,
              (index) => Container(
                width: 8,
                height: 8,
                margin: const EdgeInsets.symmetric(horizontal: 2),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.grey.shade300,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:freezed_annotation/freezed_annotation.dart';

part 'location_permission_state.freezed.dart';

@freezed
class LocationPermissionState with _$LocationPermissionState {
  const factory LocationPermissionState.initial() = _Initial;
  const factory LocationPermissionState.loading() = _Loading;
  const factory LocationPermissionState.grantedAndEnabled() = _GrantedAndEnabled;
  const factory LocationPermissionState.permissionDenied() = _PermissionDenied; // User denied, but can ask again
  const factory LocationPermissionState.permissionPermanentlyDenied() = _PermissionPermanentlyDenied; // User denied permanently, needs app settings
  const factory LocationPermissionState.serviceDisabled() = _ServiceDisabled; // Location service is off
  const factory LocationPermissionState.error(String message) = _Error;
}
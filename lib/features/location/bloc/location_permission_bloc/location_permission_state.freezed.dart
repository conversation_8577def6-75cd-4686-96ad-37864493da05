// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_permission_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LocationPermissionState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is LocationPermissionState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionState()';
  }
}

/// @nodoc
class $LocationPermissionStateCopyWith<$Res> {
  $LocationPermissionStateCopyWith(
      LocationPermissionState _, $Res Function(LocationPermissionState) __);
}

/// Adds pattern-matching-related methods to [LocationPermissionState].
extension LocationPermissionStatePatterns on LocationPermissionState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_GrantedAndEnabled value)? grantedAndEnabled,
    TResult Function(_PermissionDenied value)? permissionDenied,
    TResult Function(_PermissionPermanentlyDenied value)?
        permissionPermanentlyDenied,
    TResult Function(_ServiceDisabled value)? serviceDisabled,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _GrantedAndEnabled() when grantedAndEnabled != null:
        return grantedAndEnabled(_that);
      case _PermissionDenied() when permissionDenied != null:
        return permissionDenied(_that);
      case _PermissionPermanentlyDenied()
          when permissionPermanentlyDenied != null:
        return permissionPermanentlyDenied(_that);
      case _ServiceDisabled() when serviceDisabled != null:
        return serviceDisabled(_that);
      case _Error() when error != null:
        return error(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_GrantedAndEnabled value) grantedAndEnabled,
    required TResult Function(_PermissionDenied value) permissionDenied,
    required TResult Function(_PermissionPermanentlyDenied value)
        permissionPermanentlyDenied,
    required TResult Function(_ServiceDisabled value) serviceDisabled,
    required TResult Function(_Error value) error,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _Loading():
        return loading(_that);
      case _GrantedAndEnabled():
        return grantedAndEnabled(_that);
      case _PermissionDenied():
        return permissionDenied(_that);
      case _PermissionPermanentlyDenied():
        return permissionPermanentlyDenied(_that);
      case _ServiceDisabled():
        return serviceDisabled(_that);
      case _Error():
        return error(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_GrantedAndEnabled value)? grantedAndEnabled,
    TResult? Function(_PermissionDenied value)? permissionDenied,
    TResult? Function(_PermissionPermanentlyDenied value)?
        permissionPermanentlyDenied,
    TResult? Function(_ServiceDisabled value)? serviceDisabled,
    TResult? Function(_Error value)? error,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _GrantedAndEnabled() when grantedAndEnabled != null:
        return grantedAndEnabled(_that);
      case _PermissionDenied() when permissionDenied != null:
        return permissionDenied(_that);
      case _PermissionPermanentlyDenied()
          when permissionPermanentlyDenied != null:
        return permissionPermanentlyDenied(_that);
      case _ServiceDisabled() when serviceDisabled != null:
        return serviceDisabled(_that);
      case _Error() when error != null:
        return error(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? grantedAndEnabled,
    TResult Function()? permissionDenied,
    TResult Function()? permissionPermanentlyDenied,
    TResult Function()? serviceDisabled,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _GrantedAndEnabled() when grantedAndEnabled != null:
        return grantedAndEnabled();
      case _PermissionDenied() when permissionDenied != null:
        return permissionDenied();
      case _PermissionPermanentlyDenied()
          when permissionPermanentlyDenied != null:
        return permissionPermanentlyDenied();
      case _ServiceDisabled() when serviceDisabled != null:
        return serviceDisabled();
      case _Error() when error != null:
        return error(_that.message);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() grantedAndEnabled,
    required TResult Function() permissionDenied,
    required TResult Function() permissionPermanentlyDenied,
    required TResult Function() serviceDisabled,
    required TResult Function(String message) error,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial();
      case _Loading():
        return loading();
      case _GrantedAndEnabled():
        return grantedAndEnabled();
      case _PermissionDenied():
        return permissionDenied();
      case _PermissionPermanentlyDenied():
        return permissionPermanentlyDenied();
      case _ServiceDisabled():
        return serviceDisabled();
      case _Error():
        return error(_that.message);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? grantedAndEnabled,
    TResult? Function()? permissionDenied,
    TResult? Function()? permissionPermanentlyDenied,
    TResult? Function()? serviceDisabled,
    TResult? Function(String message)? error,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _GrantedAndEnabled() when grantedAndEnabled != null:
        return grantedAndEnabled();
      case _PermissionDenied() when permissionDenied != null:
        return permissionDenied();
      case _PermissionPermanentlyDenied()
          when permissionPermanentlyDenied != null:
        return permissionPermanentlyDenied();
      case _ServiceDisabled() when serviceDisabled != null:
        return serviceDisabled();
      case _Error() when error != null:
        return error(_that.message);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements LocationPermissionState {
  const _Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionState.initial()';
  }
}

/// @nodoc

class _Loading implements LocationPermissionState {
  const _Loading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Loading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionState.loading()';
  }
}

/// @nodoc

class _GrantedAndEnabled implements LocationPermissionState {
  const _GrantedAndEnabled();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _GrantedAndEnabled);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionState.grantedAndEnabled()';
  }
}

/// @nodoc

class _PermissionDenied implements LocationPermissionState {
  const _PermissionDenied();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _PermissionDenied);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionState.permissionDenied()';
  }
}

/// @nodoc

class _PermissionPermanentlyDenied implements LocationPermissionState {
  const _PermissionPermanentlyDenied();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PermissionPermanentlyDenied);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionState.permissionPermanentlyDenied()';
  }
}

/// @nodoc

class _ServiceDisabled implements LocationPermissionState {
  const _ServiceDisabled();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ServiceDisabled);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionState.serviceDisabled()';
  }
}

/// @nodoc

class _Error implements LocationPermissionState {
  const _Error(this.message);

  final String message;

  /// Create a copy of LocationPermissionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ErrorCopyWith<_Error> get copyWith =>
      __$ErrorCopyWithImpl<_Error>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Error &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @override
  String toString() {
    return 'LocationPermissionState.error(message: $message)';
  }
}

/// @nodoc
abstract mixin class _$ErrorCopyWith<$Res>
    implements $LocationPermissionStateCopyWith<$Res> {
  factory _$ErrorCopyWith(_Error value, $Res Function(_Error) _then) =
      __$ErrorCopyWithImpl;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$ErrorCopyWithImpl<$Res> implements _$ErrorCopyWith<$Res> {
  __$ErrorCopyWithImpl(this._self, this._then);

  final _Error _self;
  final $Res Function(_Error) _then;

  /// Create a copy of LocationPermissionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
  }) {
    return _then(_Error(
      null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

import 'package:rozana/routes/app_router.dart';
import 'package:rozana/core/utils/text_field_manager.dart';
import 'package:rozana/widgets/custom_textfield.dart';

import '../../../../core/dependency_injection/di_container.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../core/utils/app_validators.dart';
import '../../../../data/models/adress_model.dart';
import '../../../../widgets/custom_button.dart';
import '../../../../core/services/app_preferences_service.dart';
import '../../bloc/location bloc/location_bloc.dart';
import '../../services/adress_services.dart';

class AddressFormScreen extends StatefulWidget {
  final AddressModel? address;
  final bool fromCart;

  const AddressFormScreen({
    super.key,
    this.address,
    this.fromCart = false,
  });

  @override
  State<AddressFormScreen> createState() => _AddressFormScreenState();
}

class _AddressFormScreenState extends State<AddressFormScreen> {
  final _addressService = AddressService();

  final _addressLine1Controller = TextFieldManager();
  final _landmarkController = TextFieldManager();
  final _cityController = TextFieldManager();
  final _stateController = TextFieldManager();
  final _pincodeController = TextFieldManager();
  final _searchController = TextFieldManager();
  final _contactNameController = TextFieldManager();
  final _contactNumberController = TextFieldManager();
  final _addressNameController = TextFieldManager();

  bool _isLoading = false;
  bool _isDefault = false;
  String _addressType = '';

  // To track if home and work address types already exist
  bool _hasHomeAddress = false;
  bool _hasWorkAddress = false;

  // List to store all user addresses
  List<AddressModel> _userAddresses = [];

  // User profile data
  String? _userName;
  String? _userPhone;

  double _latitude = 0.0;
  double _longitude = 0.0;

  @override
  void initState() {
    super.initState();

    if (widget.address != null) {
      _populateFormWithAddress(widget.address!);
    }

    _loadUserData();

    _loadExistingAddressTypes();
  }

  void _loadUserData() {
    final userDataString = AppPreferences.getUserdata();
    if (userDataString != null) {
      try {
        final userData = json.decode(userDataString);
        setState(() {
          _userName = userData['displayName'] ?? userData['name'];
          _userPhone = userData['phoneNumber'] ?? userData['phone'];
        });
      } catch (e) {
        debugPrint('Error parsing user data: $e');
      }
    }
  }

  Future<void> _loadExistingAddressTypes() async {
    try {
      _userAddresses = await _addressService.getAllAddresses();
      bool foundHome = false;
      bool foundWork = false;
      for (var address in _userAddresses) {
        if (widget.address != null && widget.address!.id == address.id) {
          continue;
        }
        if (address.addressType == 'home') foundHome = true;
        if (address.addressType == 'work') foundWork = true;
        if (foundHome && foundWork) break;
      }
      setState(() {
        _hasHomeAddress = foundHome;
        _hasWorkAddress = foundWork;

        // Only set default address type if we're creating a new address
        // Check if this is a new address (no existing ID in saved addresses)
        bool isExistingAddress = widget.address != null &&
            _userAddresses.any((addr) => addr.id == widget.address!.id);

        if (!isExistingAddress) {
          if (_hasHomeAddress && _hasWorkAddress) {
            _addressType = 'other';
          } else if (_hasHomeAddress) {
            _addressType = 'work';
          } else if (_hasWorkAddress) {
            _addressType = 'home';
          } else {
            _addressType = 'home';
          }
        }
        // If editing existing address, _addressType is preserved from _populateFormWithAddress()
      });
    } catch (e) {
      _showSnackBar('Failed to load addresses');
    }
  }

  @override
  void dispose() {
    _addressLine1Controller.dispose();
    _landmarkController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _pincodeController.dispose();
    _searchController.dispose();
    _contactNameController.dispose();
    _contactNumberController.dispose();
    _addressNameController.dispose();
    super.dispose();
  }

  void _populateFormWithAddress(AddressModel address) {
    _addressLine1Controller.text = address.addressLine1 ?? '';
    _landmarkController.text = address.landmark ?? '';
    _cityController.text = address.city ?? '';
    _stateController.text = address.state ?? '';
    _pincodeController.text = address.pincode ?? '';

    String addressType = address.addressType ?? '';

    if (addressType != 'home' && addressType != 'work') {
      _addressType = 'other';

      _contactNameController.text = address.name ?? '';
      _contactNumberController.text = address.phone ?? '';

      _addressNameController.text = addressType;
    } else {
      _addressType = addressType;
      _contactNameController.text = address.name ?? '';
      _contactNumberController.text = address.phone ?? '';
      // _addressNameController.text = address.addressType ?? '';
    }

    _isDefault = address.isDefault ?? false;
    _latitude = (address.latitude ?? 0).toDouble();
    _longitude = (address.longitude ?? 0).toDouble();
  }

  void _navigateToLocationSelection() {
    context.pushReplacement(
      RouteNames.mapForEditAddress,
      extra: widget.address,
    );
  }

  Future<void> _saveAddress() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final addressId = widget.address?.id ??
          DateTime.now().millisecondsSinceEpoch.toString();

      String? name;
      String? phone;
      String addressType;

      if (_addressType == 'other') {
        name = _contactNameController.text;
        phone = _contactNumberController.text;
        addressType = _addressNameController.text;
      } else {
        name = _userName;
        phone = _userPhone;
        addressType = _addressType;
      }

      final address = AddressModel(
        id: addressId,
        fullAddress: _buildFullAddress(),
        addressLine1: _addressLine1Controller.text,
        landmark: _landmarkController.text.isNotEmpty
            ? _landmarkController.text
            : null,
        city: _cityController.text,
        state: _stateController.text,
        pincode: _pincodeController.text,
        latitude: _latitude,
        longitude: _longitude,
        addressType: addressType,
        isDefault: _isDefault,
        name: name,
        phone: phone,
      );

      final addresses = await _addressService.getAllAddresses();
      bool isAddressEmpty = addresses.isEmpty;

      await _addressService.saveAddress(address);

      if (isAddressEmpty) {
        _addressService.setSelectedAddress(address);
        getIt<LocationBloc>().add(LocationEvent.refreshLocation());
      }
      if (mounted) {
        if (widget.fromCart) {
          context.pop(address);
        } else {
          context.pop();
        }
      }
    } catch (e) {
      _showSnackBar('Failed to save address');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _buildFullAddress() {
    final parts = [
      _addressLine1Controller.text,
      _landmarkController.text,
      _cityController.text,
      _stateController.text,
      _pincodeController.text,
    ];

    return parts.where((part) => part.isNotEmpty).join(', ');
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Add Address Details'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: AppColors.primary,
          statusBarIconBrightness: Brightness.light,
        ),
      ),
      body: _buildForm(),
    );
  }

  Widget _buildForm() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Select location on map button
        AppButton(
          text: 'Select Location on Map',
          onPressed: _navigateToLocationSelection,
          isOutlined: true,
          prefixIcon: const Icon(Icons.map, size: 18),
        ),
        const SizedBox(height: 24),

        // Address type selector
        Text(
          'Address Type',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 8),
        _buildAddressTypeSelector(),
        const SizedBox(height: 24),

        // Contact fields for 'other' address type
        if (_addressType == 'other') ...[
          ValueListenableBuilder(
              valueListenable: _contactNameController.errorText,
              builder: (context, error, _) {
                return Titledfield(
                  title: 'Contact Name',
                  field: CustomTextField(
                    hintText: 'Enter contact name',
                    controller: _contactNameController.controller,
                    decoration: InputDecoration(
                      prefixIcon: const Icon(Icons.person),
                    ),
                  ),
                  errorText: error,
                );
              }),
          const SizedBox(height: 16),
          ValueListenableBuilder(
              valueListenable: _contactNumberController.errorText,
              builder: (context, error, _) {
                return Titledfield(
                  title: 'Contact Number',
                  field: CustomTextField(
                    hintText: 'Enter contact number',
                    controller: _contactNumberController.controller,
                    keyboardType: TextInputType.phone,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(10),
                    ],
                    decoration: InputDecoration(
                      prefixIcon: const Icon(Icons.phone),
                    ),
                  ),
                  errorText: error,
                );
              }),
          const SizedBox(height: 16),
          ValueListenableBuilder(
              valueListenable: _addressNameController.errorText,
              builder: (context, error, _) {
                return Titledfield(
                  title: 'Address Name',
                  field: CustomTextField(
                    hintText: "E.g., Mom's House, Office, etc.",
                    controller: _addressNameController.controller,
                    decoration: InputDecoration(
                      prefixIcon: const Icon(Icons.bookmark),
                    ),
                  ),
                  errorText: error,
                );
              }),
          const SizedBox(height: 16),
        ],

        // Address form fields
        ValueListenableBuilder(
            valueListenable: _addressLine1Controller.errorText,
            builder: (context, error, _) {
              return Titledfield(
                title: 'Address Line',
                field: CustomTextField(
                  hintText: 'House/Flat No., Building, Street',
                  controller: _addressLine1Controller.controller,
                  // focusNode: _addressLine1Controller.focusNode,
                  decoration: InputDecoration(
                    prefixIcon: const Icon(Icons.home),
                  ),
                ),
                errorText: error,
              );
            }),
        const SizedBox(height: 16),

        Titledfield(
          title: 'Landmark (Optional)',
          field: CustomTextField(
              hintText: 'Nearby landmark',
              controller: _landmarkController.controller,
              // focusNode: _landmarkController.focusNode,
              decoration: InputDecoration(
                prefixIcon: const Icon(Icons.location_on),
              )),
        ),
        const SizedBox(height: 16),

        ValueListenableBuilder(
            valueListenable: _cityController.errorText,
            builder: (context, error, _) {
              return Titledfield(
                title: 'City',
                field: CustomTextField(
                  hintText: 'City',
                  controller: _cityController.controller,
                  readOnly: true, // Makes the field read-only
                  // focusNode: _cityController.focusNode,
                  decoration: InputDecoration(
                    prefixIcon: const Icon(Icons.location_city),
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[\p{L}\p{M}\s]', unicode: true)),
                  ],
                ),
                errorText: error,
              );
            }),
        const SizedBox(height: 16),

        ValueListenableBuilder(
            valueListenable: _stateController.errorText,
            builder: (context, error, _) {
              return Titledfield(
                title: 'State',
                field: CustomTextField(
                  hintText: 'State',
                  controller: _stateController.controller,
                  // focusNode: _stateController.focusNode,
                  readOnly: true, // Makes the field read-only
                  decoration: InputDecoration(
                    prefixIcon: const Icon(Icons.map),
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[\p{L}\p{M}\s]', unicode: true)),
                  ],
                ),
                errorText: error,
              );
            }),
        const SizedBox(height: 16),

        ValueListenableBuilder(
            valueListenable: _pincodeController.errorText,
            builder: (context, error, _) {
              return Titledfield(
                title: 'Pincode',
                field: CustomTextField(
                    hintText: 'Pincode',
                    controller: _pincodeController.controller,
                    // focusNode: _pincodeController.focusNode,
                    keyboardType: TextInputType.number,
                    readOnly: true, // Makes the field read-only
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(6),
                    ],
                    decoration: InputDecoration(
                      prefixIcon: const Icon(Icons.pin_drop),
                    )),
                errorText: error,
              );
            }),
        const SizedBox(height: 24),

        // Save button
        AppButton(
          text: 'Save Address',
          onPressed: () {
            ValidationState contacNameValidation =
                AppValidator.invalidStringValidator(
                    _contactNameController.text, 'Please enter contact name',
                    invalidMessage: 'Please enter valid contact name');

            ValidationState contacNumberValidation =
                AppValidator.mobileNumberValidator(
                    _contactNumberController.text);

            ValidationState addressNameValidation =
                AppValidator.invalidStringValidator(
                    _addressNameController.text, 'Please enter address name',
                    invalidMessage: 'Please enter valid address name');

            ValidationState addressValidationState =
                AppValidator.emptyStringValidator(
                    _addressLine1Controller.text, 'Please enter address');

            ValidationState cityValidationState =
                AppValidator.invalidStringValidator(
                    _cityController.text, 'Please enter city',
                    invalidMessage: 'Please enter valid city name');
            ValidationState stateValidationState =
                AppValidator.invalidStringValidator(
                    _stateController.text, 'Please enter state',
                    invalidMessage: 'Please enter valid state name');
            ValidationState pincodeValidationState =
                AppValidator.emptyStringValidator(
                    _pincodeController.text, 'Please enter pincode',
                    minLength: 6,
                    lengthMessage: 'Please enter a valid 6-digit pincode');

            if (_addressType == 'other') {
              if (!contacNameValidation.valid) {
                _contactNameController
                    .throwError(contacNameValidation.message ?? '');
                // return;
              } else {
                _contactNameController.throwError('');
              }

              if (!contacNumberValidation.valid) {
                _contactNumberController
                    .throwError(contacNumberValidation.message ?? '');
                // return;
              } else {
                _contactNumberController.throwError('');
              }
              if (!addressNameValidation.valid) {
                _addressNameController
                    .throwError(addressNameValidation.message ?? '');
                // return;
              } else if ((_addressNameController.text.trim().toLowerCase() ==
                      'home') ||
                  (_addressNameController.text.trim().toLowerCase() ==
                      'work')) {
                _addressNameController.throwError(
                    "Home & Work are address types. Please choose another name.");
              } else {
                _addressNameController.throwError('');
              }
            }

            if (!addressValidationState.valid) {
              _addressLine1Controller
                  .throwError(addressValidationState.message ?? '');
              // return;
            } else {
              _addressLine1Controller.throwError('');
            }

            if (!cityValidationState.valid) {
              _cityController.throwError(cityValidationState.message ?? '');
              // return;
            } else {
              _cityController.throwError('');
            }

            if (!stateValidationState.valid) {
              _stateController.throwError(stateValidationState.message ?? '');
              // return;
            } else {
              _stateController.throwError('');
            }

            if (!pincodeValidationState.valid) {
              _pincodeController
                  .throwError(pincodeValidationState.message ?? '');
              // return;
            } else {
              _pincodeController.throwError('');
            }

            if (addressValidationState.valid &&
                cityValidationState.valid &&
                stateValidationState.valid &&
                pincodeValidationState.valid) {
              if (_addressType == 'other') {
                if (contacNameValidation.valid &&
                    contacNumberValidation.valid &&
                    addressNameValidation.valid &&
                    ((_addressNameController.text.trim().toLowerCase() !=
                            'home') &&
                        (_addressNameController.text.trim().toLowerCase() !=
                            'work'))) {
                  _saveAddress();
                }
              } else {
                _saveAddress();
              }
            }
          },
          isLoading: _isLoading,
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildAddressTypeSelector() {
    bool disableHome = _hasHomeAddress && _addressType != 'home';
    bool disableWork = _hasWorkAddress && _addressType != 'work';

    return Row(
      children: [
        _buildAddressTypeOption('home', 'Home', Icons.home, disableHome),
        const SizedBox(width: 16),
        _buildAddressTypeOption('work', 'Work', Icons.work, disableWork),
        const SizedBox(width: 16),
        _buildAddressTypeOption('other', 'Other', Icons.place, false),
      ],
    );
  }

  Widget _buildAddressTypeOption(
      String type, String label, IconData icon, bool isDisabled) {
    final isSelected = _addressType == type;
    final isSelectable = isSelected || !isDisabled;

    return Expanded(
      child: Tooltip(
        message: isDisabled ? 'You already have a $label address' : '',
        child: InkWell(
          onTap: isSelectable
              ? () {
                  setState(() {
                    _addressType = type;
                  });
                }
              : null,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected
                    ? AppColors.primary
                    : isDisabled
                        ? Colors.grey.shade200
                        : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
              color: isSelected
                  ? AppColors.primary.withValues(alpha: 0.1)
                  : isDisabled
                      ? Colors.grey.shade100
                      : Colors.transparent,
            ),
            child: Column(
              children: [
                Icon(
                  icon,
                  color: isSelected
                      ? AppColors.primary
                      : isDisabled
                          ? Colors.grey.shade400
                          : Colors.grey,
                ),
                const SizedBox(height: 4),
                Text(
                  label,
                  style: TextStyle(
                    color: isSelected
                        ? AppColors.primary
                        : isDisabled
                            ? Colors.grey.shade400
                            : Colors.grey.shade700,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

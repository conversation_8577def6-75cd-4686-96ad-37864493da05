import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';

import '../../../../routes/app_router.dart';
import '../../bloc/location_permission_bloc/location_permission_bloc.dart';
import '../../bloc/location_permission_bloc/location_permission_event.dart';

enum LocationIssueType {
  permissionDenied, // User denied, can ask again
  permissionPermanentlyDenied, // User denied forever, needs app settings
  serviceDisabled, // Location service is off on device
}

extension LocationIssueTypeExtension on LocationIssueType {
  String get title {
    switch (this) {
      case LocationIssueType.permissionDenied:
        return 'Location Access Needed';
      case LocationIssueType.permissionPermanentlyDenied:
        return 'Permission Permanently Denied';
      case LocationIssueType.serviceDisabled:
        return 'Location Service Off';
    }
  }

  String get description {
    switch (this) {
      case LocationIssueType.permissionDenied:
        return 'This app needs access to your device\'s location to provide its features. Please grant permission.';
      case LocationIssueType.permissionPermanentlyDenied:
        return 'It looks like you\'ve permanently denied location access. Please enable it from your device settings.';
      case LocationIssueType.serviceDisabled:
        return 'Location services are currently turned off on your device. Please enable them to continue.';
    }
  }

  String get actionButtonText {
    switch (this) {
      case LocationIssueType.permissionDenied:
        return 'Grant Permission';
      case LocationIssueType.permissionPermanentlyDenied:
        return 'Open App Settings';
      case LocationIssueType.serviceDisabled:
        return 'Enable Location'; // Or 'Check Again' depending on desired behavior
    }
  }

  String get lottieAsset {
    // Define your Lottie asset paths here
    switch (this) {
      case LocationIssueType.permissionDenied:
        return 'assets/lottie/location_permission.json'; // Replace with actual path
      case LocationIssueType.permissionPermanentlyDenied:
        return 'assets/lottie/app_settings.json'; // Replace with actual path
      case LocationIssueType.serviceDisabled:
        return 'assets/lottie/gps_off.json'; // Replace with actual path
    }
  }
}

void showLocationIssueBottomSheet(
    BuildContext context, LocationIssueType type) {
  if (LocationPermissionBloc.isBottomSheetShowing) {
    return; // Prevent multiple sheets
  }
  LocationPermissionBloc.isBottomSheetShowing = true;

  showModalBottomSheet(
    context: context,
    isDismissible: false,
    enableDrag: false,
    builder: (BuildContext bc) {
      return PopScope(
        canPop: false,
        child: Container(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              // Lottie Animation
              Lottie.asset(
                'assets/lotties/location.json',
                height: 120,
                repeat: true,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) =>
                    const Icon(Icons.error), // Fallback
              ),
              const SizedBox(height: 15),
              // Title
              Text(
                type.title,
                style:
                    const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              // Description
              Text(
                type.description,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 20),
              // Action Button (dynamically changed)
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(bc); // Dismiss bottom sheet
                  LocationPermissionBloc.isBottomSheetShowing = false;
                  switch (type) {
                    case LocationIssueType.permissionDenied:
                      context.read<LocationPermissionBloc>().add(
                          const LocationPermissionEvent.requestPermissions());
                      break;
                    case LocationIssueType.permissionPermanentlyDenied:
                      context
                          .read<LocationPermissionBloc>()
                          .add(const LocationPermissionEvent.openAppSettings());
                      break;
                    case LocationIssueType.serviceDisabled:
                      context.read<LocationPermissionBloc>().add(
                          const LocationPermissionEvent
                              .requestLocationServiceToggle());

                      break;
                  }
                },
                child: Text(type.actionButtonText),
              ),
              const SizedBox(height: 10),
              TextButton(
                onPressed: () {
                  Navigator.pop(bc);
                  LocationPermissionBloc.isBottomSheetShowing = false;
                  context.push(RouteNames.mapForNewAddress);
                },
                child: const Text('Select Location Manually'),
              ),
            ],
          ),
        ),
      );
    },
  ).whenComplete(() {
    LocationPermissionBloc.isBottomSheetShowing =
        false; // Reset flag when sheet is dismissed
  });
}

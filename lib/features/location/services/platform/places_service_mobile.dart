import 'dart:async';
import 'package:uuid/uuid.dart';
import '../../../../core/network/google_api_client.dart';
import '../../../../core/utils/logger.dart';
import 'places_service_interface.dart';

/// Mobile implementation of the Places service interface
class PlacesServiceMobile implements PlacesServiceInterface {
  final GoogleApiClient _apiClient = GoogleApiClient();
  
  @override
  Future<bool> initPlacesService() async {
    // No initialization needed for mobile
    return true;
  }

  @override
  Future<String> generateSessionToken() async {
    // Generate a UUID for session token
    return const Uuid().v4();
  }

  @override
  Future<List<Map<String, dynamic>>> getPlaceAutocomplete({
    required String input,
    String? sessionToken,
    Map<String, dynamic>? options,
  }) async {
    try {
      LogMessage.p('Mobile: Getting place autocomplete for "$input"', 
          subTag: 'PlacesServiceMobile');

      final response = await _apiClient.getPlaceAutocomplete(
        input: input,
        sessionToken: sessionToken ?? await generateSessionToken(),
      );

      return response.when(
        success: (predictions) {
          LogMessage.p(
              'Mobile: Success - received ${predictions.length} predictions',
              subTag: 'PlacesServiceMobile');
          
          // Convert to the same format as web implementation
          return predictions.map((item) {
            return {
              'placeId': item['place_id'] ?? '',
              'description': item['description'] ?? '',
              'mainText': item['structured_formatting']?['main_text'] ?? '',
              'secondaryText': item['structured_formatting']?['secondary_text'] ?? '',
              'types': item['types'] ?? [],
            };
          }).toList();
        },
        error: (message) {
          LogMessage.p('Error getting place autocomplete: $message',
              subTag: 'PlacesServiceMobile');
          return [];
        },
      );
    } catch (e) {
      LogMessage.p('Error getting place autocomplete: $e',
          subTag: 'PlacesServiceMobile');
      return [];
    }
  }

  @override
  Future<Map<String, dynamic>?> getPlaceDetailsById(String placeId) async {
    try {
      LogMessage.p('Mobile: Getting place details for place_id: $placeId',
          subTag: 'PlacesServiceMobile');
          
      final detailsResponse = await _apiClient.getPlaceDetails(
        placeId: placeId,
        fields: 'name,formatted_address,types,geometry',
      );

      return detailsResponse.when(
        success: (result) {
          // Convert to the same format as web implementation
          final geometry = result['geometry'] ?? {};
          final location = geometry['location'] ?? {};
          
          return {
            'placeId': result['place_id'] ?? '',
            'name': result['name'] ?? '',
            'formattedAddress': result['formatted_address'] ?? '',
            'lat': location['lat'] ?? 0,
            'lng': location['lng'] ?? 0,
            'addressComponents': (result['address_components'] as List?)?.map((component) => {
              'longName': component['long_name'] ?? '',
              'shortName': component['short_name'] ?? '',
              'types': component['types'] ?? [],
            }).toList() ?? [],
          };
        },
        error: (message) {
          LogMessage.p('Error getting place details: $message',
              subTag: 'PlacesServiceMobile');
          return null;
        },
      );
    } catch (e) {
      LogMessage.p('Error getting place details by ID: $e',
          subTag: 'PlacesServiceMobile');
      return null;
    }
  }
}

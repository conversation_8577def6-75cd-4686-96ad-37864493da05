import 'package:flutter/material.dart';
import 'package:rozana/widgets/skeleton_loader_factory.dart';

class CategorySkeletonLoader extends StatelessWidget {
  final bool useGridView;
  final bool showAsRow;
  final int gridCrossAxisCount;
  final double gridChildAspectRatio;
  final double verticalPading;
  final double? itemHeight;

  const CategorySkeletonLoader({
    super.key,
    this.useGridView = false,
    this.showAsRow = false,
    this.gridCrossAxisCount = 4,
    this.gridChildAspectRatio = 0.75,
    this.verticalPading = 0,
    this.itemHeight,
  });

  static const int _itemCount = 12;

  @override
  Widget build(BuildContext context) {
    return showAsRow
        ? _buildRowSkeleton(context)
        : useGridView
            ? _buildCustomGridSkeleton(context)
            : _buildDefaultGridSkeleton(context);
  }

  Widget _buildDefaultGridSkeleton(BuildContext context) {
    return SkeletonLoaderFactory.createSkeletonGrid(
      itemCount: _itemCount,
      itemHeight: itemHeight ?? 70,
      itemWidth: 55,
      crossAxisCount: 4,
      mainAxisSpacing: 12,
      crossAxisSpacing: 8,
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: verticalPading),
    );
  }

  Widget _buildCustomGridSkeleton(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final cardWidth = (screenWidth - 44) / gridCrossAxisCount;
    final cardHeight = cardWidth / gridChildAspectRatio;

    return SkeletonLoaderFactory.createSkeletonGrid(
      itemCount: _itemCount,
      itemHeight: cardHeight,
      itemWidth: cardWidth,
      crossAxisCount: gridCrossAxisCount,
      mainAxisSpacing: 12,
      crossAxisSpacing: 12,
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: verticalPading),
    );
  }

  Widget _buildRowSkeleton(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final cardWidth = screenWidth - 32;

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: verticalPading),
      itemCount: _itemCount,
      itemBuilder: (context, index) => Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: SkeletonLoaderFactory.createCategorySkeleton(
          width: cardWidth,
          height: 70,
          radius: 12,
        ),
      ),
    );
  }
}

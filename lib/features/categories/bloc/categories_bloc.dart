import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../domain/entities/category_entity.dart';
import '../../../domain/usecases/get_categories_usecase.dart';

part 'categories_event.dart';
part 'categories_state.dart';
part 'categories_bloc.freezed.dart';

class CategoriesBloc extends Bloc<CategoriesEvent, CategoriesState> {
  final GetCategoriesUseCase _getCategoriesUseCase;
  static Map<String, List<CategoryEntity>> loadedSubcategories = {};

  ScrollController? scrollController;
  CategoriesBloc(this._getCategoriesUseCase)
      : super(CategoriesState.initial()) {
    scrollController = ScrollController();
    on<CategoriesEvent>(
      (event, emit) => event.map(
        fetchCategories: (_) => _onFetchCategories(emit),
        updateLoadedList: (e) => _onUpdateCategoryList(e.categories, emit),
        scrollToItem: (e) => _onScrollToItem(e.categories, emit),
      ),
    );
  }

  Future<void> _onFetchCategories(Emitter<CategoriesState> emit) async {
    try {
      loadedSubcategories.clear();
      // Start all futures without awaiting
      final categoriesFuture = _getCategoriesUseCase.execute();

      // Keep current progressive data
      List<CategoryEntity>? categories;

      categoriesFuture.then((value) {
        categories = value;
        add(CategoriesEvent.updateLoadedList(categories: categories ?? []));
      });
    } catch (e) {
      emit(CategoriesState.error(message: e.toString()));
    }
  }

  void _onUpdateCategoryList(
      List<CategoryEntity>? categories, Emitter<CategoriesState> emit) {
    state.maybeMap(
      loaded: (value) {
        emit(value.copyWith(
          categories: categories ?? value.categories,
        ));
      },
      orElse: () {
        emit(CategoriesState.loaded(
          categories: categories,
        ));
      },
    );
  }

  void _onScrollToItem(
      CategoryEntity? categories, Emitter<CategoriesState> emit) {
    state.maybeMap(
      loaded: (value) {
        emit(value.copyWith(
          switchToItem: categories,
        ));
      },
      orElse: () {},
    );
  }

  @override
  Future<void> close() {
    scrollController?.dispose();
    return super.close();
  }
}

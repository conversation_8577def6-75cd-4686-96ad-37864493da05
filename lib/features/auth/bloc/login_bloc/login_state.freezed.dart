// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'login_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LoginState {
  String get mobile;
  bool get showError;
  bool get isLoading;

  /// Create a copy of LoginState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LoginStateCopyWith<LoginState> get copyWith =>
      _$LoginStateCopyWithImpl<LoginState>(this as LoginState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LoginState &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.showError, showError) ||
                other.showError == showError) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mobile, showError, isLoading);

  @override
  String toString() {
    return 'LoginState(mobile: $mobile, showError: $showError, isLoading: $isLoading)';
  }
}

/// @nodoc
abstract mixin class $LoginStateCopyWith<$Res> {
  factory $LoginStateCopyWith(
          LoginState value, $Res Function(LoginState) _then) =
      _$LoginStateCopyWithImpl;
  @useResult
  $Res call({String mobile, bool showError, bool isLoading});
}

/// @nodoc
class _$LoginStateCopyWithImpl<$Res> implements $LoginStateCopyWith<$Res> {
  _$LoginStateCopyWithImpl(this._self, this._then);

  final LoginState _self;
  final $Res Function(LoginState) _then;

  /// Create a copy of LoginState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mobile = null,
    Object? showError = null,
    Object? isLoading = null,
  }) {
    return _then(_self.copyWith(
      mobile: null == mobile
          ? _self.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String,
      showError: null == showError
          ? _self.showError
          : showError // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// Adds pattern-matching-related methods to [LoginState].
extension LoginStatePatterns on LoginState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoginInitial value)? initial,
    TResult Function(_LoginOTP value)? otp,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LoginInitial() when initial != null:
        return initial(_that);
      case _LoginOTP() when otp != null:
        return otp(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoginInitial value) initial,
    required TResult Function(_LoginOTP value) otp,
  }) {
    final _that = this;
    switch (_that) {
      case _LoginInitial():
        return initial(_that);
      case _LoginOTP():
        return otp(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoginInitial value)? initial,
    TResult? Function(_LoginOTP value)? otp,
  }) {
    final _that = this;
    switch (_that) {
      case _LoginInitial() when initial != null:
        return initial(_that);
      case _LoginOTP() when otp != null:
        return otp(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String mobile, bool showError, bool isLoading)? initial,
    TResult Function(String mobile, String otp, String verificationId,
            bool showError, bool isLoading, bool canResend, int resendSeconds)?
        otp,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LoginInitial() when initial != null:
        return initial(_that.mobile, _that.showError, _that.isLoading);
      case _LoginOTP() when otp != null:
        return otp(
            _that.mobile,
            _that.otp,
            _that.verificationId,
            _that.showError,
            _that.isLoading,
            _that.canResend,
            _that.resendSeconds);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String mobile, bool showError, bool isLoading)
        initial,
    required TResult Function(String mobile, String otp, String verificationId,
            bool showError, bool isLoading, bool canResend, int resendSeconds)
        otp,
  }) {
    final _that = this;
    switch (_that) {
      case _LoginInitial():
        return initial(_that.mobile, _that.showError, _that.isLoading);
      case _LoginOTP():
        return otp(
            _that.mobile,
            _that.otp,
            _that.verificationId,
            _that.showError,
            _that.isLoading,
            _that.canResend,
            _that.resendSeconds);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String mobile, bool showError, bool isLoading)? initial,
    TResult? Function(String mobile, String otp, String verificationId,
            bool showError, bool isLoading, bool canResend, int resendSeconds)?
        otp,
  }) {
    final _that = this;
    switch (_that) {
      case _LoginInitial() when initial != null:
        return initial(_that.mobile, _that.showError, _that.isLoading);
      case _LoginOTP() when otp != null:
        return otp(
            _that.mobile,
            _that.otp,
            _that.verificationId,
            _that.showError,
            _that.isLoading,
            _that.canResend,
            _that.resendSeconds);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _LoginInitial implements LoginState {
  const _LoginInitial(
      {this.mobile = '', this.showError = false, this.isLoading = false});

  @override
  @JsonKey()
  final String mobile;
  @override
  @JsonKey()
  final bool showError;
  @override
  @JsonKey()
  final bool isLoading;

  /// Create a copy of LoginState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoginInitialCopyWith<_LoginInitial> get copyWith =>
      __$LoginInitialCopyWithImpl<_LoginInitial>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoginInitial &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.showError, showError) ||
                other.showError == showError) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mobile, showError, isLoading);

  @override
  String toString() {
    return 'LoginState.initial(mobile: $mobile, showError: $showError, isLoading: $isLoading)';
  }
}

/// @nodoc
abstract mixin class _$LoginInitialCopyWith<$Res>
    implements $LoginStateCopyWith<$Res> {
  factory _$LoginInitialCopyWith(
          _LoginInitial value, $Res Function(_LoginInitial) _then) =
      __$LoginInitialCopyWithImpl;
  @override
  @useResult
  $Res call({String mobile, bool showError, bool isLoading});
}

/// @nodoc
class __$LoginInitialCopyWithImpl<$Res>
    implements _$LoginInitialCopyWith<$Res> {
  __$LoginInitialCopyWithImpl(this._self, this._then);

  final _LoginInitial _self;
  final $Res Function(_LoginInitial) _then;

  /// Create a copy of LoginState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? mobile = null,
    Object? showError = null,
    Object? isLoading = null,
  }) {
    return _then(_LoginInitial(
      mobile: null == mobile
          ? _self.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String,
      showError: null == showError
          ? _self.showError
          : showError // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _LoginOTP implements LoginState {
  const _LoginOTP(
      {this.mobile = '',
      this.otp = '',
      this.verificationId = '',
      this.showError = false,
      this.isLoading = false,
      this.canResend = false,
      this.resendSeconds = 30});

  @override
  @JsonKey()
  final String mobile;
  @JsonKey()
  final String otp;
  @JsonKey()
  final String verificationId;
  @override
  @JsonKey()
  final bool showError;
  @override
  @JsonKey()
  final bool isLoading;
  @JsonKey()
  final bool canResend;
  @JsonKey()
  final int resendSeconds;

  /// Create a copy of LoginState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoginOTPCopyWith<_LoginOTP> get copyWith =>
      __$LoginOTPCopyWithImpl<_LoginOTP>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoginOTP &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.otp, otp) || other.otp == otp) &&
            (identical(other.verificationId, verificationId) ||
                other.verificationId == verificationId) &&
            (identical(other.showError, showError) ||
                other.showError == showError) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.canResend, canResend) ||
                other.canResend == canResend) &&
            (identical(other.resendSeconds, resendSeconds) ||
                other.resendSeconds == resendSeconds));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mobile, otp, verificationId,
      showError, isLoading, canResend, resendSeconds);

  @override
  String toString() {
    return 'LoginState.otp(mobile: $mobile, otp: $otp, verificationId: $verificationId, showError: $showError, isLoading: $isLoading, canResend: $canResend, resendSeconds: $resendSeconds)';
  }
}

/// @nodoc
abstract mixin class _$LoginOTPCopyWith<$Res>
    implements $LoginStateCopyWith<$Res> {
  factory _$LoginOTPCopyWith(_LoginOTP value, $Res Function(_LoginOTP) _then) =
      __$LoginOTPCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String mobile,
      String otp,
      String verificationId,
      bool showError,
      bool isLoading,
      bool canResend,
      int resendSeconds});
}

/// @nodoc
class __$LoginOTPCopyWithImpl<$Res> implements _$LoginOTPCopyWith<$Res> {
  __$LoginOTPCopyWithImpl(this._self, this._then);

  final _LoginOTP _self;
  final $Res Function(_LoginOTP) _then;

  /// Create a copy of LoginState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? mobile = null,
    Object? otp = null,
    Object? verificationId = null,
    Object? showError = null,
    Object? isLoading = null,
    Object? canResend = null,
    Object? resendSeconds = null,
  }) {
    return _then(_LoginOTP(
      mobile: null == mobile
          ? _self.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String,
      otp: null == otp
          ? _self.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String,
      verificationId: null == verificationId
          ? _self.verificationId
          : verificationId // ignore: cast_nullable_to_non_nullable
              as String,
      showError: null == showError
          ? _self.showError
          : showError // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      canResend: null == canResend
          ? _self.canResend
          : canResend // ignore: cast_nullable_to_non_nullable
              as bool,
      resendSeconds: null == resendSeconds
          ? _self.resendSeconds
          : resendSeconds // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on

// Web-specific implementation for Razorpay
import 'dart:js' as js;
import 'package:flutter/foundation.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';

/// Web implementation of Razorpay checkout
class RazorpayWebImpl {
  final Function(PaymentSuccessResponse) onPaymentSuccess;
  final Function(PaymentFailureResponse) onPaymentError;

  RazorpayWebImpl({
    required this.onPaymentSuccess,
    required this.onPaymentError,
  });

  /// Process payment on web platform
  void processPayment(Map<String, dynamic> options) {
    debugPrint('Processing web payment with Razorpay');

    try {
      // Extract key values from options
      final keyId = options['key'] as String;
      final orderId = options['order_id'] as String;
      final name = options['name'] as String? ?? 'Rozana';
      final description =
          options['description'] as String? ?? 'Payment for your order';
      final prefill = options['prefill'] as Map<String, dynamic>? ?? {};
      final email = prefill['email'] as String? ?? '';
      final contact = prefill['contact'] as String? ?? '';
      final customerName = prefill['name'] as String? ?? '';

      // Register callback functions for Razorpay to call
      _registerCallbacks();

      // Check if Razorpay is available
      bool isRazorpayAvailable = _isRazorpayAvailable();
      debugPrint('Razorpay available: $isRazorpayAvailable');

      if (!isRazorpayAvailable) {
        // If not available, try to reload the script
        debugPrint('Reloading Razorpay script');
        _reloadRazorpayScript();

        // Wait for script to load (max 5 seconds)
        debugPrint('Waiting for Razorpay script to load...');
        Future.delayed(const Duration(seconds: 5), () {
          _initializeRazorpayCheckout(
              keyId, orderId, name, description, customerName, email, contact);
        });
      } else {
        // If available, initialize checkout immediately
        _initializeRazorpayCheckout(
            keyId, orderId, name, description, customerName, email, contact);
      }
    } catch (e) {
      debugPrint('Error calling Razorpay web checkout: $e');
      onPaymentError(PaymentFailureResponse(
        0,
        'Failed to initialize Razorpay: $e',
        null,
      ));
    }
  }

  /// Register JavaScript callbacks for Razorpay to call
  void _registerCallbacks() {
    try {
      js.context['flutterRazorpaySuccess'] = js.allowInterop((response) {
        debugPrint('Payment success callback received from web');
        onPaymentSuccess(PaymentSuccessResponse(
          response['razorpay_payment_id'],
          response['razorpay_order_id'],
          response['razorpay_signature'],
          null, // walletName is null for direct payments
        ));
      });

      js.context['flutterRazorpayError'] = js.allowInterop((response) {
        debugPrint(
            'Payment error callback received from web: ${response['description']}');
        onPaymentError(PaymentFailureResponse(
          response['code'] ?? 0,
          response['description'] ?? 'Payment failed',
          null,
        ));
      });
    } catch (e) {
      debugPrint('Error registering JS callbacks: $e');
    }
  }

  /// Check if Razorpay is available in the browser
  bool _isRazorpayAvailable() {
    try {
      return js.context.callMethod('eval', ['typeof Razorpay === "function"'])
              as bool? ??
          false;
    } catch (e) {
      debugPrint('Error checking Razorpay availability: $e');
      return false;
    }
  }

  /// Reload the Razorpay script
  void _reloadRazorpayScript() {
    try {
      final reloadScript = '''
        var script = document.createElement('script');
        script.src = 'https://checkout.razorpay.com/v1/checkout.js';
        script.async = false;
        script.onload = function() {
          console.log('Razorpay script loaded successfully');
          window.razorpayScriptLoaded = true;
        };
        script.onerror = function() {
          console.error('Failed to load Razorpay script');
          window.flutterRazorpayError({code: 0, description: 'Failed to load Razorpay script'});
        };
        document.head.appendChild(script);
      ''';

      js.context.callMethod('eval', [reloadScript]);
    } catch (e) {
      debugPrint('Error reloading Razorpay script: $e');
    }
  }

  /// Initialize and open Razorpay checkout
  void _initializeRazorpayCheckout(String keyId, String orderId, String name,
      String description, String customerName, String email, String contact) {
    try {
      final script = '''
        try {
          console.log('Initializing Razorpay checkout');
          var options = {
            key: "$keyId",
            order_id: "$orderId",
            name: "$name",
            description: "$description",
            prefill: {
              name: "$customerName",
              email: "$email",
              contact: "$contact"
            },
            theme: {
              color: "#3F51B5"
            },
            handler: function(response) {
              console.log('Payment success', response);
              window.flutterRazorpaySuccess(response);
            },
            modal: {
              ondismiss: function() {
                console.log('Checkout dismissed');
                window.flutterRazorpayError({code: 2, description: 'Payment cancelled by user'});
              }
            }
          };
          
          if (typeof Razorpay === 'function') {
            console.log('Creating Razorpay instance');
            var rzp = new Razorpay(options);
            rzp.on('payment.failed', function(response) {
              console.log('Payment failed', response.error);
              window.flutterRazorpayError({code: response.error.code, description: response.error.description});
            });
            console.log('Opening Razorpay checkout');
            rzp.open();
          } else {
            console.error('Razorpay not available');
            window.flutterRazorpayError({code: 0, description: 'Razorpay not available'});
          }
        } catch(e) {
          console.error('Error in Razorpay checkout:', e);
          window.flutterRazorpayError({code: 0, description: e.toString()});
        }
      ''';

      // Execute the script
      debugPrint('Executing Razorpay script');
      js.context.callMethod('eval', [script]);
    } catch (e) {
      debugPrint('Error initializing Razorpay checkout: $e');
      onPaymentError(PaymentFailureResponse(
        0,
        'Failed to initialize Razorpay: $e',
        null,
      ));
    }
  }
}

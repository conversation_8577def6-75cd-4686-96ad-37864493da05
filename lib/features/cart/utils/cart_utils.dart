import 'package:rozana/data/models/cart_item_model.dart';
import 'package:rozana/data/models/cart_model.dart';

class CartUtils {
  /// Get quantity of a specific product in cart
  static num getItemQuantity(String productId, String sku, CartModel cart) {
    CartItemModel? item = cart.items?.firstWhere(
      (item) => ((item.productId == productId) || (item.skuID == sku)),
      orElse: () => CartItemModel(
        id: '',
        productId: '',
        name: '',
        price: 0,
        imageUrl: '',
        quantity: 0,
        unit: '',
        facilityId: '',
        facilityName: '',
        skuID: '',
      ),
    );
    return (item?.quantity ?? 0);
  }

  /// Get cart item ID by product ID
  static String? getCartItemId(String productId, String sku, CartModel cart) {
    final item = cart.items?.firstWhere(
      (item) => ((item.productId == productId) || (item.skuID == sku)),
      orElse: () => CartItemModel(
        id: '',
        productId: '',
        name: '',
        price: 0,
        imageUrl: '',
        quantity: 0,
        unit: '',
        facilityId: '',
        facilityName: '',
        skuID: '',
      ),
    );
    return (item?.id?.isEmpty ?? false) ? null : item!.id;
  }
}

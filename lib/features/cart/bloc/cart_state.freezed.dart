// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'cart_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CartState {
  CartModel get cart;
  bool get isLoading;
  String? get appliedCoupon;
  String? get error;
  AddressModel? get deliveryAddress;
  bool get isLoadingAddress;
  OrderProcessingStatus get orderStatus;
  String? get orderId;
  Map<String, dynamic>? get orderData;

  /// Create a copy of CartState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CartStateCopyWith<CartState> get copyWith =>
      _$CartStateCopyWithImpl<CartState>(this as CartState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CartState &&
            (identical(other.cart, cart) || other.cart == cart) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.appliedCoupon, appliedCoupon) ||
                other.appliedCoupon == appliedCoupon) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.deliveryAddress, deliveryAddress) ||
                other.deliveryAddress == deliveryAddress) &&
            (identical(other.isLoadingAddress, isLoadingAddress) ||
                other.isLoadingAddress == isLoadingAddress) &&
            (identical(other.orderStatus, orderStatus) ||
                other.orderStatus == orderStatus) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            const DeepCollectionEquality().equals(other.orderData, orderData));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      cart,
      isLoading,
      appliedCoupon,
      error,
      deliveryAddress,
      isLoadingAddress,
      orderStatus,
      orderId,
      const DeepCollectionEquality().hash(orderData));

  @override
  String toString() {
    return 'CartState(cart: $cart, isLoading: $isLoading, appliedCoupon: $appliedCoupon, error: $error, deliveryAddress: $deliveryAddress, isLoadingAddress: $isLoadingAddress, orderStatus: $orderStatus, orderId: $orderId, orderData: $orderData)';
  }
}

/// @nodoc
abstract mixin class $CartStateCopyWith<$Res> {
  factory $CartStateCopyWith(CartState value, $Res Function(CartState) _then) =
      _$CartStateCopyWithImpl;
  @useResult
  $Res call(
      {CartModel cart,
      bool isLoading,
      String? appliedCoupon,
      String? error,
      AddressModel? deliveryAddress,
      bool isLoadingAddress,
      OrderProcessingStatus orderStatus,
      String? orderId,
      Map<String, dynamic>? orderData});
}

/// @nodoc
class _$CartStateCopyWithImpl<$Res> implements $CartStateCopyWith<$Res> {
  _$CartStateCopyWithImpl(this._self, this._then);

  final CartState _self;
  final $Res Function(CartState) _then;

  /// Create a copy of CartState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cart = null,
    Object? isLoading = null,
    Object? appliedCoupon = freezed,
    Object? error = freezed,
    Object? deliveryAddress = freezed,
    Object? isLoadingAddress = null,
    Object? orderStatus = null,
    Object? orderId = freezed,
    Object? orderData = freezed,
  }) {
    return _then(_self.copyWith(
      cart: null == cart
          ? _self.cart
          : cart // ignore: cast_nullable_to_non_nullable
              as CartModel,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      appliedCoupon: freezed == appliedCoupon
          ? _self.appliedCoupon
          : appliedCoupon // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryAddress: freezed == deliveryAddress
          ? _self.deliveryAddress
          : deliveryAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      isLoadingAddress: null == isLoadingAddress
          ? _self.isLoadingAddress
          : isLoadingAddress // ignore: cast_nullable_to_non_nullable
              as bool,
      orderStatus: null == orderStatus
          ? _self.orderStatus
          : orderStatus // ignore: cast_nullable_to_non_nullable
              as OrderProcessingStatus,
      orderId: freezed == orderId
          ? _self.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      orderData: freezed == orderData
          ? _self.orderData
          : orderData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// Adds pattern-matching-related methods to [CartState].
extension CartStatePatterns on CartState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_CartState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CartState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_CartState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CartState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_CartState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CartState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            CartModel cart,
            bool isLoading,
            String? appliedCoupon,
            String? error,
            AddressModel? deliveryAddress,
            bool isLoadingAddress,
            OrderProcessingStatus orderStatus,
            String? orderId,
            Map<String, dynamic>? orderData)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CartState() when $default != null:
        return $default(
            _that.cart,
            _that.isLoading,
            _that.appliedCoupon,
            _that.error,
            _that.deliveryAddress,
            _that.isLoadingAddress,
            _that.orderStatus,
            _that.orderId,
            _that.orderData);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            CartModel cart,
            bool isLoading,
            String? appliedCoupon,
            String? error,
            AddressModel? deliveryAddress,
            bool isLoadingAddress,
            OrderProcessingStatus orderStatus,
            String? orderId,
            Map<String, dynamic>? orderData)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CartState():
        return $default(
            _that.cart,
            _that.isLoading,
            _that.appliedCoupon,
            _that.error,
            _that.deliveryAddress,
            _that.isLoadingAddress,
            _that.orderStatus,
            _that.orderId,
            _that.orderData);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            CartModel cart,
            bool isLoading,
            String? appliedCoupon,
            String? error,
            AddressModel? deliveryAddress,
            bool isLoadingAddress,
            OrderProcessingStatus orderStatus,
            String? orderId,
            Map<String, dynamic>? orderData)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _CartState() when $default != null:
        return $default(
            _that.cart,
            _that.isLoading,
            _that.appliedCoupon,
            _that.error,
            _that.deliveryAddress,
            _that.isLoadingAddress,
            _that.orderStatus,
            _that.orderId,
            _that.orderData);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _CartState implements CartState {
  const _CartState(
      {required this.cart,
      required this.isLoading,
      this.appliedCoupon,
      this.error,
      this.deliveryAddress,
      this.isLoadingAddress = false,
      this.orderStatus = OrderProcessingStatus.initial,
      this.orderId,
      final Map<String, dynamic>? orderData})
      : _orderData = orderData;

  @override
  final CartModel cart;
  @override
  final bool isLoading;
  @override
  final String? appliedCoupon;
  @override
  final String? error;
  @override
  final AddressModel? deliveryAddress;
  @override
  @JsonKey()
  final bool isLoadingAddress;
  @override
  @JsonKey()
  final OrderProcessingStatus orderStatus;
  @override
  final String? orderId;
  final Map<String, dynamic>? _orderData;
  @override
  Map<String, dynamic>? get orderData {
    final value = _orderData;
    if (value == null) return null;
    if (_orderData is EqualUnmodifiableMapView) return _orderData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of CartState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CartStateCopyWith<_CartState> get copyWith =>
      __$CartStateCopyWithImpl<_CartState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CartState &&
            (identical(other.cart, cart) || other.cart == cart) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.appliedCoupon, appliedCoupon) ||
                other.appliedCoupon == appliedCoupon) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.deliveryAddress, deliveryAddress) ||
                other.deliveryAddress == deliveryAddress) &&
            (identical(other.isLoadingAddress, isLoadingAddress) ||
                other.isLoadingAddress == isLoadingAddress) &&
            (identical(other.orderStatus, orderStatus) ||
                other.orderStatus == orderStatus) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            const DeepCollectionEquality()
                .equals(other._orderData, _orderData));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      cart,
      isLoading,
      appliedCoupon,
      error,
      deliveryAddress,
      isLoadingAddress,
      orderStatus,
      orderId,
      const DeepCollectionEquality().hash(_orderData));

  @override
  String toString() {
    return 'CartState(cart: $cart, isLoading: $isLoading, appliedCoupon: $appliedCoupon, error: $error, deliveryAddress: $deliveryAddress, isLoadingAddress: $isLoadingAddress, orderStatus: $orderStatus, orderId: $orderId, orderData: $orderData)';
  }
}

/// @nodoc
abstract mixin class _$CartStateCopyWith<$Res>
    implements $CartStateCopyWith<$Res> {
  factory _$CartStateCopyWith(
          _CartState value, $Res Function(_CartState) _then) =
      __$CartStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {CartModel cart,
      bool isLoading,
      String? appliedCoupon,
      String? error,
      AddressModel? deliveryAddress,
      bool isLoadingAddress,
      OrderProcessingStatus orderStatus,
      String? orderId,
      Map<String, dynamic>? orderData});
}

/// @nodoc
class __$CartStateCopyWithImpl<$Res> implements _$CartStateCopyWith<$Res> {
  __$CartStateCopyWithImpl(this._self, this._then);

  final _CartState _self;
  final $Res Function(_CartState) _then;

  /// Create a copy of CartState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? cart = null,
    Object? isLoading = null,
    Object? appliedCoupon = freezed,
    Object? error = freezed,
    Object? deliveryAddress = freezed,
    Object? isLoadingAddress = null,
    Object? orderStatus = null,
    Object? orderId = freezed,
    Object? orderData = freezed,
  }) {
    return _then(_CartState(
      cart: null == cart
          ? _self.cart
          : cart // ignore: cast_nullable_to_non_nullable
              as CartModel,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      appliedCoupon: freezed == appliedCoupon
          ? _self.appliedCoupon
          : appliedCoupon // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryAddress: freezed == deliveryAddress
          ? _self.deliveryAddress
          : deliveryAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      isLoadingAddress: null == isLoadingAddress
          ? _self.isLoadingAddress
          : isLoadingAddress // ignore: cast_nullable_to_non_nullable
              as bool,
      orderStatus: null == orderStatus
          ? _self.orderStatus
          : orderStatus // ignore: cast_nullable_to_non_nullable
              as OrderProcessingStatus,
      orderId: freezed == orderId
          ? _self.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      orderData: freezed == orderData
          ? _self._orderData
          : orderData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

// dart format on

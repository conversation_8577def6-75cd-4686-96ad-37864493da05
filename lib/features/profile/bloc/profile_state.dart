import 'package:freezed_annotation/freezed_annotation.dart';

part 'profile_state.freezed.dart'; // Required for Freezed

@freezed
class ProfileState with _$ProfileState {
  const factory ProfileState.initial() = _Initial;
  const factory ProfileState.loading() = _Loading;
  const factory ProfileState.loaded({
    required String userName,
    required String userEmail,
    required String userGender,
     required int addressCount,
  }) = _Loaded;
  const factory ProfileState.error({required String message}) = _Error;
}

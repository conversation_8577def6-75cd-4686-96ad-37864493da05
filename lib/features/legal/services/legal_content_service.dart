import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/legal_content_model.dart';

enum LegalContentType {
  aboutUs,
  privacyPolicy,
  termsConditions,
}

class LegalContentService {
  static const String _basePath = 'assets/data/';

  /// Loads legal content based on type and language
  static Future<LegalContentModel> loadContent(
    LegalContentType type,
    String language,
  ) async {
    try {
      final fileName = _getFileName(type, language);
      final jsonString = await rootBundle.loadString('$_basePath$fileName');
      final jsonData = json.decode(jsonString) as Map<String, dynamic>;

      return LegalContentModel.fromJson(jsonData);
    } catch (e) {
      // Return default content if file loading fails
      return _getDefaultContent(type, language);
    }
  }

  /// Gets the appropriate filename based on content type and language
  static String _getFileName(LegalContentType type, String language) {
    final typeName = _getTypeName(type);
    final langSuffix = language == 'hi' ? '_hi' : '_en';
    return '$typeName$langSuffix.json';
  }

  /// Converts enum to filename prefix
  static String _getTypeName(LegalContentType type) {
    switch (type) {
      case LegalContentType.aboutUs:
        return 'about_us';
      case LegalContentType.privacyPolicy:
        return 'privacy_policy';
      case LegalContentType.termsConditions:
        return 'terms_conditions';
    }
  }

  /// Returns default content if file loading fails
  static LegalContentModel _getDefaultContent(
    LegalContentType type,
    String language,
  ) {
    final isHindi = language == 'hi';

    switch (type) {
      case LegalContentType.aboutUs:
        return LegalContentModel(
          title: isHindi ? 'हमारे बारे में' : 'About Us',
          sections: [
            LegalSection(
              heading:
                  isHindi ? 'जानकारी लोड नहीं हो सकी' : 'Content Not Available',
              content: isHindi
                  ? 'कृपया बाद में पुनः प्रयास करें।'
                  : 'Please try again later.',
            ),
          ],
        );

      case LegalContentType.privacyPolicy:
        return LegalContentModel(
          title: isHindi ? 'गोपनीयता नीति' : 'Privacy Policy',
          sections: [
            LegalSection(
              heading:
                  isHindi ? 'जानकारी लोड नहीं हो सकी' : 'Content Not Available',
              content: isHindi
                  ? 'कृपया बाद में पुनः प्रयास करें।'
                  : 'Please try again later.',
            ),
          ],
        );

      case LegalContentType.termsConditions:
        return LegalContentModel(
          title: isHindi ? 'नियम और शर्तें' : 'Terms and Conditions',
          sections: [
            LegalSection(
              heading:
                  isHindi ? 'जानकारी लोड नहीं हो सकी' : 'Content Not Available',
              content: isHindi
                  ? 'कृपया बाद में पुनः प्रयास करें।'
                  : 'Please try again later.',
            ),
          ],
        );
    }
  }
}

import 'package:flutter_bloc/flutter_bloc.dart';
import 'legal_event.dart';
import 'legal_state.dart';
import '../services/legal_content_service.dart';

class LegalBloc extends Bloc<LegalEvent, LegalState> {
  LegalBloc() : super(LegalInitial()) {
    on<LoadLegalContent>(_onLoadLegalContent);
  }

  Future<void> _onLoadLegalContent(
    LoadLegalContent event,
    Emitter<LegalState> emit,
  ) async {
    emit(LegalLoading());

    try {
      final content = await LegalContentService.loadContent(
        event.contentType,
        event.language,
      );
      emit(LegalLoaded(content));
    } catch (e) {
      emit(LegalError('Failed to load content: ${e.toString()}'));
    }
  }
}

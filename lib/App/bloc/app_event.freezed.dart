// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is AppEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AppEvent()';
  }
}

/// @nodoc
class $AppEventCopyWith<$Res> {
  $AppEventCopyWith(AppEvent _, $Res Function(AppEvent) __);
}

/// Adds pattern-matching-related methods to [AppEvent].
extension AppEventPatterns on AppEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AppStarted value)? appStarted,
    TResult Function(LoginRequested value)? loginRequested,
    TResult Function(LoginWithProfileCheck value)? loginWithProfileCheck,
    TResult Function(LogoutRequested value)? logoutRequested,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case AppStarted() when appStarted != null:
        return appStarted(_that);
      case LoginRequested() when loginRequested != null:
        return loginRequested(_that);
      case LoginWithProfileCheck() when loginWithProfileCheck != null:
        return loginWithProfileCheck(_that);
      case LogoutRequested() when logoutRequested != null:
        return logoutRequested(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AppStarted value) appStarted,
    required TResult Function(LoginRequested value) loginRequested,
    required TResult Function(LoginWithProfileCheck value)
        loginWithProfileCheck,
    required TResult Function(LogoutRequested value) logoutRequested,
  }) {
    final _that = this;
    switch (_that) {
      case AppStarted():
        return appStarted(_that);
      case LoginRequested():
        return loginRequested(_that);
      case LoginWithProfileCheck():
        return loginWithProfileCheck(_that);
      case LogoutRequested():
        return logoutRequested(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AppStarted value)? appStarted,
    TResult? Function(LoginRequested value)? loginRequested,
    TResult? Function(LoginWithProfileCheck value)? loginWithProfileCheck,
    TResult? Function(LogoutRequested value)? logoutRequested,
  }) {
    final _that = this;
    switch (_that) {
      case AppStarted() when appStarted != null:
        return appStarted(_that);
      case LoginRequested() when loginRequested != null:
        return loginRequested(_that);
      case LoginWithProfileCheck() when loginWithProfileCheck != null:
        return loginWithProfileCheck(_that);
      case LogoutRequested() when logoutRequested != null:
        return logoutRequested(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? appStarted,
    TResult Function(String token, Map<String, dynamic>? user)? loginRequested,
    TResult Function(String token, Map<String, dynamic>? user)?
        loginWithProfileCheck,
    TResult Function()? logoutRequested,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case AppStarted() when appStarted != null:
        return appStarted();
      case LoginRequested() when loginRequested != null:
        return loginRequested(_that.token, _that.user);
      case LoginWithProfileCheck() when loginWithProfileCheck != null:
        return loginWithProfileCheck(_that.token, _that.user);
      case LogoutRequested() when logoutRequested != null:
        return logoutRequested();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() appStarted,
    required TResult Function(String token, Map<String, dynamic>? user)
        loginRequested,
    required TResult Function(String token, Map<String, dynamic>? user)
        loginWithProfileCheck,
    required TResult Function() logoutRequested,
  }) {
    final _that = this;
    switch (_that) {
      case AppStarted():
        return appStarted();
      case LoginRequested():
        return loginRequested(_that.token, _that.user);
      case LoginWithProfileCheck():
        return loginWithProfileCheck(_that.token, _that.user);
      case LogoutRequested():
        return logoutRequested();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? appStarted,
    TResult? Function(String token, Map<String, dynamic>? user)? loginRequested,
    TResult? Function(String token, Map<String, dynamic>? user)?
        loginWithProfileCheck,
    TResult? Function()? logoutRequested,
  }) {
    final _that = this;
    switch (_that) {
      case AppStarted() when appStarted != null:
        return appStarted();
      case LoginRequested() when loginRequested != null:
        return loginRequested(_that.token, _that.user);
      case LoginWithProfileCheck() when loginWithProfileCheck != null:
        return loginWithProfileCheck(_that.token, _that.user);
      case LogoutRequested() when logoutRequested != null:
        return logoutRequested();
      case _:
        return null;
    }
  }
}

/// @nodoc

class AppStarted implements AppEvent {
  const AppStarted();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is AppStarted);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AppEvent.appStarted()';
  }
}

/// @nodoc

class LoginRequested implements AppEvent {
  const LoginRequested(this.token, {final Map<String, dynamic>? user})
      : _user = user;

  final String token;
  final Map<String, dynamic>? _user;
  Map<String, dynamic>? get user {
    final value = _user;
    if (value == null) return null;
    if (_user is EqualUnmodifiableMapView) return _user;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LoginRequestedCopyWith<LoginRequested> get copyWith =>
      _$LoginRequestedCopyWithImpl<LoginRequested>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LoginRequested &&
            (identical(other.token, token) || other.token == token) &&
            const DeepCollectionEquality().equals(other._user, _user));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, token, const DeepCollectionEquality().hash(_user));

  @override
  String toString() {
    return 'AppEvent.loginRequested(token: $token, user: $user)';
  }
}

/// @nodoc
abstract mixin class $LoginRequestedCopyWith<$Res>
    implements $AppEventCopyWith<$Res> {
  factory $LoginRequestedCopyWith(
          LoginRequested value, $Res Function(LoginRequested) _then) =
      _$LoginRequestedCopyWithImpl;
  @useResult
  $Res call({String token, Map<String, dynamic>? user});
}

/// @nodoc
class _$LoginRequestedCopyWithImpl<$Res>
    implements $LoginRequestedCopyWith<$Res> {
  _$LoginRequestedCopyWithImpl(this._self, this._then);

  final LoginRequested _self;
  final $Res Function(LoginRequested) _then;

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? token = null,
    Object? user = freezed,
  }) {
    return _then(LoginRequested(
      null == token
          ? _self.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
      user: freezed == user
          ? _self._user
          : user // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class LoginWithProfileCheck implements AppEvent {
  const LoginWithProfileCheck(this.token, {final Map<String, dynamic>? user})
      : _user = user;

  final String token;
  final Map<String, dynamic>? _user;
  Map<String, dynamic>? get user {
    final value = _user;
    if (value == null) return null;
    if (_user is EqualUnmodifiableMapView) return _user;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LoginWithProfileCheckCopyWith<LoginWithProfileCheck> get copyWith =>
      _$LoginWithProfileCheckCopyWithImpl<LoginWithProfileCheck>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LoginWithProfileCheck &&
            (identical(other.token, token) || other.token == token) &&
            const DeepCollectionEquality().equals(other._user, _user));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, token, const DeepCollectionEquality().hash(_user));

  @override
  String toString() {
    return 'AppEvent.loginWithProfileCheck(token: $token, user: $user)';
  }
}

/// @nodoc
abstract mixin class $LoginWithProfileCheckCopyWith<$Res>
    implements $AppEventCopyWith<$Res> {
  factory $LoginWithProfileCheckCopyWith(LoginWithProfileCheck value,
          $Res Function(LoginWithProfileCheck) _then) =
      _$LoginWithProfileCheckCopyWithImpl;
  @useResult
  $Res call({String token, Map<String, dynamic>? user});
}

/// @nodoc
class _$LoginWithProfileCheckCopyWithImpl<$Res>
    implements $LoginWithProfileCheckCopyWith<$Res> {
  _$LoginWithProfileCheckCopyWithImpl(this._self, this._then);

  final LoginWithProfileCheck _self;
  final $Res Function(LoginWithProfileCheck) _then;

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? token = null,
    Object? user = freezed,
  }) {
    return _then(LoginWithProfileCheck(
      null == token
          ? _self.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
      user: freezed == user
          ? _self._user
          : user // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class LogoutRequested implements AppEvent {
  const LogoutRequested();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is LogoutRequested);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AppEvent.logoutRequested()';
  }
}

// dart format on

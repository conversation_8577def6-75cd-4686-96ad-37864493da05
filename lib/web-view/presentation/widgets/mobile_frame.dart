import 'package:flutter/material.dart';

class MobileFrame extends StatelessWidget {
  final Widget child;
  const MobileFrame({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    const double phoneWidth = 380.0;
    const double phoneHeight = 710.0;
    const double frameThickness = 8.0;
    const double borderRadius = 32.0;

    return Center(
      child: Container(
        width: phoneWidth,
        height: phoneHeight,
        decoration: BoxDecoration(
          color: const Color(0xFF1F1F1F),
          borderRadius: BorderRadius.circular(borderRadius),
          border: Border.all(color: const Color(0xFF3A3A3A), width: 1.5),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.4),
              blurRadius: 25,
              offset: const Offset(0, 12),
              spreadRadius: 3,
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.15),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Main Screen Area
            Positioned(
              top: frameThickness,
              left: frameThickness,
              right: frameThickness,
              bottom: frameThickness - 2,
              child: ClipRRect(
                borderRadius:
                    BorderRadius.circular(borderRadius - frameThickness),
                child: Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                  ),
                  child: MediaQuery(
                    data: MediaQuery.of(context).copyWith(
                      size: Size(
                        phoneWidth - (frameThickness * 2),
                        phoneHeight - 58 - (frameThickness - 2),
                      ),
                      devicePixelRatio: 2.5,
                      textScaler: const TextScaler.linear(1.0),
                      platformBrightness: Brightness.light,
                      padding: const EdgeInsets.all(0),
                    ),
                    child: child,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

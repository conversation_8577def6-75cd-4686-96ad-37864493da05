import 'package:flutter/material.dart';

class CompanyInfo extends StatelessWidget {
  const CompanyInfo({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.all(32.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Company Name
          Text(
            'Rozana',
            style: theme.headlineLarge?.copyWith(
              fontWeight: FontWeight.bold,
              letterSpacing: 1.2,
            ),
          ),
          const SizedBox(height: 16),
          // Company Tagline
          Text(
            'Empowering your daily shopping experience.',
            style: theme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 24),
          // Company Description
          const Text(
            'Rozana is a leading B2C platform offering a wide range of products delivered to your doorstep. '
            'We focus on quality, affordability, and convenience for our customers. '
            'Download our app to experience seamless shopping, exclusive deals, and fast delivery!',
            style: TextStyle(
              fontSize: 16,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 32),
          // Download Buttons
          Row(
            children: [
              DownloadButton(
                label: 'Get it on Play Store',
                icon: Icons.android_outlined,
                color: Colors.green,
                onPressed: () {
                  // TODO: Add Play Store link
                },
              ),
              const SizedBox(width: 20),
              DownloadButton(
                label: 'Download on App Store',
                icon: Icons.apple,
                color: Colors.black,
                onPressed: () {
                  // TODO: Add App Store link
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class DownloadButton extends StatelessWidget {
  final String label;
  final IconData icon;
  final Color color;
  final VoidCallback onPressed;

  const DownloadButton({
    required this.label,
    required this.icon,
    required this.color,
    required this.onPressed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, color: Colors.white),
      label: Text(
        label,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}

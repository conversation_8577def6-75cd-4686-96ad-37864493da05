import 'package:flutter/material.dart';

class KeyFeature extends StatelessWidget {
  const KeyFeature({super.key});

  @override
  Widget build(BuildContext context) {
    final features = [
      {
        'icon': Icons.shopping_cart,
        'title': 'Fresh Products',
        'subtitle': 'and Groceries'
      },
      {'icon': Icons.delivery_dining, 'title': 'Fast Delivery'},
      {'icon': Icons.currency_rupee, 'title': 'Affordable Prices'},
      {'icon': Icons.lock, 'title': 'Secure Payment'},
      {'icon': Icons.headset_mic, 'title': 'Customer Support'},
    ];

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Why are we the best',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 20),
          Wrap(
            spacing: 50,
            runSpacing: 20,
            children: features.map((feature) {
              return FeatureItem(
                icon: feature['icon'] as IconData,
                title: feature['title'] as String,
                subtitle: feature['subtitle'] as String?,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}

class FeatureItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;

  const FeatureItem({
    required this.icon,
    required this.title,
    this.subtitle,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: Colors.white),
        ),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title),
            if (subtitle != null) Text(subtitle!),
          ],
        ),
      ],
    );
  }
}

import 'package:rozana/domain/entities/category_entity.dart';

import '../../../../data/services/data_loading_manager.dart';

/// Encapsulates the business logic for retrieving categories
class GetSubCategoriesUseCase {
  final DataLoadingManager _dataLoadingManager;
  const GetSubCategoriesUseCase(this._dataLoadingManager);

  /// Execute the use case to get sub categories
  ///
  /// [page] - Page number for pagination (0-based)
  /// [pageSize] - Number of items per page
  /// [refresh] - Whether to refresh the data
  /// [query] - Search query filter
  /// [level] - Level of the category
  ///
  /// Returns a list of [CategoryEntity]
  Future<List<CategoryEntity>> execute({
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
    String query = '',
    String? level,
  }) async {
    // Business logic can be added here
    // For example: validation, caching, filtering, etc.

    // Validate inputs
    if (page < 0) {
      throw ArgumentError('Page number cannot be negative');
    }

    if (pageSize <= 0) {
      throw ArgumentError('Page size must be greater than 0');
    }

    // Call repository to get data
    final categories = await _dataLoadingManager.loadSubCategories(
      page: page,
      pageSize: pageSize,
      refresh: refresh,
      query: query,
      level: level,
    );

    // Apply business rules
    // Return all sub categories (removed count filter as it was hiding sub categories)
    return categories;
  }
}

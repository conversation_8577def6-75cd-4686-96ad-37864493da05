import '../entities/order_entity.dart';
import '../repositories/order_repository_interface.dart';

/// Use case for getting order details
/// Encapsulates the business logic for fetching specific order information
class GetOrderDetailsUseCase {
  final OrderRepositoryInterface _repository;

  GetOrderDetailsUseCase(this._repository);

  /// Execute the use case to get order details
  /// 
  /// [orderId] - ID of the order to fetch
  /// Returns the order entity or null if not found
  Future<OrderEntity?> call(String orderId) async {
    try {
      if (orderId.isEmpty) {
        throw ArgumentError('Order ID cannot be empty');
      }

      return await _repository.getOrderDetails(orderId);
    } catch (e) {
      // Log error and rethrow
      throw Exception('Failed to fetch order details: $e');
    }
  }

  /// Get order details with validation
  /// Throws exception if order is not found
  Future<OrderEntity> getOrderDetailsRequired(String orderId) async {
    final order = await call(orderId);
    if (order == null) {
      throw Exception('Order not found with ID: $orderId');
    }
    return order;
  }

  /// Check if order exists
  Future<bool> orderExists(String orderId) async {
    try {
      final order = await call(orderId);
      return order != null;
    } catch (e) {
      return false;
    }
  }

  /// Get order timeline for tracking
  Future<List<dynamic>> getOrderTimeline(String orderId) async {
    final order = await call(orderId);
    return order?.orderTimeline ?? [];
  }

  /// Check if order can be cancelled
  Future<bool> canCancelOrder(String orderId) async {
    final order = await call(orderId);
    return order?.canCancel ?? false;
  }

  /// Check if order can be reordered
  Future<bool> canReorderOrder(String orderId) async {
    final order = await call(orderId);
    return order?.canReorder ?? false;
  }
}

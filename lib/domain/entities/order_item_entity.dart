/// Domain entity representing an order item
/// Pure business object without external dependencies
class OrderItemEntity {
  final String id;
  final String productId;
  final String name;
  final String? imageUrl;
  final double price;
  final int quantity;
  final String unit;
  final double discountedPrice;
  final String? facilityId;
  final String? facilityName;
  final String? skuID;

  const OrderItemEntity({
    required this.id,
    required this.productId,
    required this.name,
    this.imageUrl,
    required this.price,
    required this.quantity,
    required this.unit,
    required this.discountedPrice,
    this.facilityId,
    this.facilityName,
    this.skuID,
  });

  /// Calculate total price for this item
  double get totalPrice => discountedPrice * quantity;

  /// Calculate savings for this item
  double get savings => (price - discountedPrice) * quantity;

  /// Check if item has discount
  bool get hasDiscount => price > discountedPrice;

  /// Get discount percentage
  double get discountPercentage {
    if (!hasDiscount) return 0.0;
    return ((price - discountedPrice) / price) * 100;
  }

  /// Get formatted price
  String get formattedPrice => '₹${price.toStringAsFixed(2)}';

  /// Get formatted discounted price
  String get formattedDiscountedPrice => '₹${discountedPrice.toStringAsFixed(2)}';

  /// Get formatted total price
  String get formattedTotalPrice => '₹${totalPrice.toStringAsFixed(2)}';

  /// Get quantity with unit
  String get quantityWithUnit => '$quantity $unit';
}

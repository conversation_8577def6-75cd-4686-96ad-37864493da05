import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomImage extends StatelessWidget {
  final String? imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final String imageType;
  final Color? imageColor;

  const CustomImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.imageType = 'product',
    this.imageColor,
  });

  String get fallbackImagePath {
    return imageType == 'product'
        ? 'assets/images/image-placeholder.jpg'
        : 'assets/categories/shopping-bag.png';
  }

  bool get isNetworkImage {
    return imageUrl != null &&
        (imageUrl!.startsWith('http://') || imageUrl!.startsWith('https://'));
  }

  bool get isSvgImage {
    return imageUrl != null && (imageUrl!.toLowerCase().contains('.svg'));
  }

  @override
  Widget build(BuildContext context) {
    final imageWidget = isSvgImage
        ? isNetworkImage
            ? SvgPicture.network(
                imageUrl!,
                fit: fit,
                width: width,
                height: height,
                errorBuilder: (context, error, stackTrace) =>
                    _buildFallbackImage(),
                colorFilter: imageColor != null
                    ? ColorFilter.mode(imageColor!, BlendMode.srcIn)
                    : null,
              )
            : SvgPicture.asset(
                imageUrl!,
                fit: fit,
                width: width,
                height: height,
                errorBuilder: (context, error, stackTrace) =>
                    _buildFallbackImage(),
              )
        : isNetworkImage
            ? CachedNetworkImage(
                imageUrl: imageUrl!,
                fit: fit,
                width: width,
                height: height,
                errorWidget: (context, url, error) => _buildFallbackImage(),
              )
            : Image.asset(
                imageUrl ?? fallbackImagePath,
                fit: fit,
                width: width,
                height: height,
                errorBuilder: (context, error, stackTrace) =>
                    _buildFallbackImage(),
                color: imageColor,
              );

    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: imageWidget,
    );
  }

  Widget _buildFallbackImage() {
    return Container(
      margin: const EdgeInsets.all(8),
      child: Image.asset(
        fallbackImagePath,
        color: Colors.black,
        width: 30,
        height: 30,
        fit: fit,
      ),
    );
  }
}

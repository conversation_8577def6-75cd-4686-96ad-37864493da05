import 'package:flutter/material.dart';

class RozanaLogoImage extends StatelessWidget {
  const RozanaLogoImage({super.key, this.width, this.height, this.padding});
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Image.asset(
        'assets/images/rozana_logo.png',
        width: width,
        height: height,
      ),
    );
  }
}

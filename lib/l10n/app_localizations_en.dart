// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Rozana';

  @override
  String get myCart => 'My Cart';

  @override
  String get placeOrder => 'Place Order';

  @override
  String get selectAddress => 'Select Address';

  @override
  String get loginToProceed => 'Login to Proceed';

  @override
  String get myProfile => 'My Profile';

  @override
  String get yourOrders => 'Your Orders';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get logout => 'Logout';

  @override
  String get retry => 'Retry';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get somethingWentWrong => 'Something went wrong';

  @override
  String get welcomeToRozana => 'Welcome to Rozana';

  @override
  String get verifyOtp => 'Verify OTP';

  @override
  String get total => 'Total';

  @override
  String totalItems(int count) {
    return 'Total ($count items)';
  }

  @override
  String get savedAddresses => 'Saved Addresses';

  @override
  String get address => 'Address';

  @override
  String get addresses => 'Addresses';

  @override
  String get refunds => 'Refunds';

  @override
  String get profile => 'Profile';

  @override
  String get rewards => 'Rewards';

  @override
  String get paymentManagement => 'Payment Management';

  @override
  String get wishlist => 'Wishlist';

  @override
  String get notifications => 'Notifications';

  @override
  String get rozanaPay => 'Rozana Pay';

  @override
  String get language => 'Language';

  @override
  String get english => 'English';

  @override
  String get hindi => 'हिंदी';

  @override
  String get clearCart => 'Clear Cart';

  @override
  String get confirmClearCart => 'Are you sure you want to clear your cart?';

  @override
  String get cancel => 'Cancel';

  @override
  String get clear => 'Clear';

  @override
  String get save => 'Save';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get home => 'Home';

  @override
  String get work => 'Work';

  @override
  String get other => 'Other';

  @override
  String get addToCart => 'Add to Cart';

  @override
  String get buyNow => 'Buy Now';

  @override
  String get outOfStock => 'Out of Stock';

  @override
  String get login => 'Login';

  @override
  String get signUp => 'Sign Up';

  @override
  String get otpVerification => 'OTP Verification';

  @override
  String get addAddress => 'Add Address';

  @override
  String get editAddress => 'Edit Address';

  @override
  String get deleteAddress => 'Delete Address';

  @override
  String get defaultAddress => 'Default Address';

  @override
  String get setAsDefault => 'Set as Default';

  @override
  String get deliveryAddress => 'Delivery Address';

  @override
  String get paymentMethod => 'Payment Method';

  @override
  String get cashOnDelivery => 'Cash on Delivery';

  @override
  String get orderSummary => 'Order Summary';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get deliveryFee => 'Delivery Fee';

  @override
  String get tax => 'Tax';

  @override
  String get discount => 'Discount';

  @override
  String get free => 'FREE';

  @override
  String get youSaved => 'You saved';

  @override
  String get onThisOrder => 'on this order';

  @override
  String get emptyCart => 'Your cart is empty';

  @override
  String get startShopping => 'Start Shopping';

  @override
  String get remove => 'Remove';

  @override
  String get quantity => 'Quantity';

  @override
  String get price => 'Price';

  @override
  String get originalPrice => 'Original Price';

  @override
  String get discountedPrice => 'Discounted Price';

  @override
  String get refresh => 'Refresh';

  @override
  String get pullToRefresh => 'Pull to refresh';

  @override
  String get deleteMyAccount => 'Delete My Account';

  @override
  String get aboutUs => 'About Us';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get nextDayDelivery => 'Next day delivery';
}

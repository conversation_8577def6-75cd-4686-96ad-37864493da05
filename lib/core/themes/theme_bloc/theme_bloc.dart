export 'theme_event.dart';
export 'theme_state.dart';

import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'theme_event.dart';
import 'theme_state.dart';

enum AppThemeMode {
  system,
  light,
  dark,
}

class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  static AppThemeMode defaultTheme = AppThemeMode.light;
  ThemeBloc()
      : super(ThemeState(
            themeMode: ThemeMode.light, appThemeMode: defaultTheme)) {
    on<ThemeEvent>((event, emit) async {
      await event.map(
        init: (_) => _onInit(emit),
        themeChanged: (event) => _onThemeChanged(event.mode, emit),
      );
    });
  }

  Future<void> _onInit(Emitter<ThemeState> emit) async {
    String saved = AppPreferences.getTheme() ?? '';
    AppThemeMode appTheme = AppThemeMode.values.firstWhere(
      (e) => e.toString() == saved,
      orElse: () => defaultTheme,
    );
    emit(_mapAppThemeToState(appTheme));
  }

  Future<void> _onThemeChanged(
      AppThemeMode mode, Emitter<ThemeState> emit) async {
    await AppPreferences.setTheme(mode.toString());
    emit(_mapAppThemeToState(mode));
  }

  ThemeState _mapAppThemeToState(AppThemeMode mode) {
    return ThemeState(
      appThemeMode: mode,
      themeMode: switch (mode) {
        AppThemeMode.system => ThemeMode.system,
        AppThemeMode.light => ThemeMode.light,
        AppThemeMode.dark => ThemeMode.dark,
      },
    );
  }
}

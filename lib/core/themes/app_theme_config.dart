import 'package:flutter/material.dart';

import 'app_theme.dart';
import 'color_schemes.dart';

/// Theme configuration for the app
class AppThemeConfig {
  /// Get the current theme mode
  static ThemeMode get themeMode => ThemeMode.system;

  /// Initialize theme settings

  /// Get theme based on brightness
  static ThemeData getTheme(Brightness brightness) {
    return brightness == Brightness.light
        ? AppTheme.lightTheme
        : AppTheme.darkTheme;
  }

  /// Get color scheme based on brightness
  static ColorScheme getColorScheme(Brightness brightness) {
    return brightness == Brightness.light
        ? const ColorScheme(
            brightness: Brightness.light,
            primary: AppColors.primary,
            onPrimary: Colors.white,
            secondary: AppColors.secondary,
            onSecondary: Colors.white,
            error: AppColors.error,
            onError: Colors.white,
            surface: AppColors.background,
            onSurface: AppColors.neutral600,
          )
        : const ColorScheme(
            brightness: Brightness.dark,
            primary: AppColors.primaryDark,
            onPrimary: Colors.white,
            secondary: AppColors.secondaryDark,
            onSecondary: Colors.white,
            error: AppColors.error,
            onError: Colors.white,
            surface: Color(0xFF121212),
            onSurface: Colors.white,
          );
  }
}

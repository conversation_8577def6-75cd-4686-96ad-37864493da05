import 'package:flutter/material.dart';


class AppColors {
  // Primary colors
  static const Color primary = Color(0xFF7D56A6);
  static const Color primary100 = Color(0xFFD8D5EA);
  static const Color primary200 = Color(0xFFBEABD3);
  static const Color primary300 = Color(0xFF9D80BC);
  static const Color primary400 = Color(0xFF7D56A6);
  static const Color primary500 = Color(0xFF5C2C90);
  static const Color primary600 = Color(0xFF4A2373);
  static const Color primary700 = Color(0xFF371A56);
  static const Color primary800 = Color(0xFF25123A);

  //Need to remove primary
  static const Color primaryAverage = Color(0xFFCC1626);
  static const Color primaryLight = Color.fromARGB(255, 127, 226, 219);
  static const Color primaryDark = Color(0xFF99050C);

  // Secondary colors
  static const Color secondary100 = Color(0xFFF9EBEC);
  static const Color secondary200 = Color(0xFFEDC4C5);
  static const Color secondary300 = Color(0xFFE19D9F);
  static const Color secondary400 = Color(0xFFCF6365);
  static const Color secondary500 = Color(0xFF902C2E);
  static const Color secondary600 = Color(0xFF9C3032);
  static const Color secondary700 = Color(0xFF752425);
  static const Color secondary800 = Color(0xFF4E1819);

  // Need to remove Secondary colors
  static const Color secondary = Color(0xFFFF4081);
  static const Color secondaryLight = Color(0xFFFF80AB);
  static const Color secondaryDark = Color(0xFFC51162);

  // Neutral colors
  static const Color neutral100 = Color(0xFFFFFFFF);
  static const Color neutral150 = Color(0xFFF0F0F6);
  static const Color neutral200 = Color(0xFFDBDBDB);
  static const Color neutral300 = Color(0xFFB6B6B6);
  static const Color neutral400 = Color(0xFF929292);
  static const Color neutral500 = Color(0xFF6D6D6D);
  static const Color neutral600 = Color(0xFF494949);
  static const Color neutral650 = Color(0xFF333333);
  static const Color neutral700 = Color(0xFF242424);
  static const Color neutral800 = Color(0xFF000000);

  // Yellow Colors
  static const Color yellow800 = Color(0xFF664D00);
  static const Color yellow700 = Color(0xFF997300);
  static const Color yellow600 = Color(0xFFCC9900);
  static const Color yellow500 = Color(0xFFFFBF00);
  static const Color yellow400 = Color(0xFFFFCC33);
  static const Color yellow300 = Color(0xFFFFDF80);
  static const Color yellow200 = Color(0xFFFFECB2);
  static const Color yellow100 = Color(0xFFFFF9E5);

  // Green colors
  static const Color green800 = Color(0xFF1F4721);
  static const Color green700 = Color(0xFF2E6B31);
  static const Color green600 = Color(0xFF3E8E41);
  static const Color green500 = Color(0xFF4DB251);
  static const Color green400 = Color(0xFF71C174);
  static const Color green300 = Color(0xFFA6D8A8);
  static const Color green200 = Color(0xFFCAE8CB);
  static const Color green100 = Color(0xFFEDF7EE);

  // Red colors
  static const Color red800 = Color(0xFF610C05);
  static const Color red700 = Color(0xFF911108);
  static const Color red600 = Color(0xFFC1170B);
  static const Color red500 = Color(0xFFF21D0D);
  static const Color red400 = Color(0xFFF44A3E);
  static const Color red300 = Color(0xFFF88E86);
  static const Color red200 = Color(0xFFFBBBB6);
  static const Color red100 = Color(0xFFFEE8E7);

  // Blue colors
  static const Color blue800 = Color(0xFF053861);
  static const Color blue700 = Color(0xFF085491);
  static const Color blue600 = Color(0xFF0A70C2);
  static const Color blue500 = Color(0xFF0D8DF2);
  static const Color blue400 = Color(0xFF3DA3F5);
  static const Color blue300 = Color(0xFF86C6F8);
  static const Color blue200 = Color(0xFFB6DDFB);
  static const Color blue100 = Color(0xFFE7F4FE);

  // Background colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);

  // Text colors

  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  static const Color textGrey = Color(0xFF6B7280);

  // Status colors
  static const Color success = Color(0xFF4CAF50);
  static const Color error = Color(0xFFF44336);
  static const Color warning = Color(0xFFFF9800);
  static const Color info = Color(0xFF2196F3);
  static const Color white = Color(0xFFFFFFFF);

  // Order status colors
  static const Color orderPending = Color(0xFFFF9800); // Orange
  static const Color orderConfirmed = Color(0xFF2196F3); // Blue
  static const Color orderPreparing = Color(0xFFFFC107); // Amber
  static const Color orderOutForDelivery = Color(0xFF9C27B0); // Purple

  //Shadow colors
  static const Color shadowGrey = Color(0x1A00001A);
}

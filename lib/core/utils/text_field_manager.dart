import 'package:flutter/material.dart';

class TextFieldManager {
  final TextEditingController _controller;
  final FocusNode _focusNode;
  ValueNotifier<String> errorText = ValueNotifier('');
  bool _isDisposed = false;

  TextFieldManager()
      : _controller = TextEditingController(),
        _focusNode = FocusNode();

  TextEditingController get controller => _controller;
  FocusNode get focusNode => _focusNode;

  String get text => _controller.text.trim();
  bool get isNotEmpty => text.isNotEmpty;
  bool get hasFocus => _focusNode.hasFocus;
  bool get isDisposed => _isDisposed;

  void addListener(void Function() listener) =>
      _focusNode.addListener(listener);

  set text(String? text) {
    _controller.text = text ?? '';
    _controller.selection = TextSelection.fromPosition(
      TextPosition(offset: _controller.text.length),
    );
  }

  void clear() => _controller.clear();

  void throwError(String error) {
    errorText.value = error;
  }

  void dispose() {
    if (_isDisposed) return; // Prevent double disposal
    _isDisposed = true;
    _controller.dispose();
    _focusNode.dispose();
    errorText.value = '';
  }
}

import 'dart:async';

class Debouncer {
  final int milliseconds;
  Timer? _timer;

  Debouncer({this.milliseconds = 500}); // Default debounce delay is 500ms

  // Call this function to debounce
  void run(Function callback) {
    // Cancel the existing timer
    if (_timer != null) {
      _timer!.cancel();
    }

    // Start a new timer with the specified delay
    _timer = Timer(Duration(milliseconds: milliseconds), () {
      callback();
    });
  }

  void dispose() {
    _timer?.cancel();
  }
}

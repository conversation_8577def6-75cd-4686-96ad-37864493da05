import 'dart:math';
import 'package:flutter/material.dart';

/// Utility class for color-related functions
class ColorUtils {
  /// Generates a random light pastel color with transparency
  /// 
  /// [baseOpacity] - The base opacity for the color (0.0 to 1.0)
  /// [seed] - Optional seed for consistent color generation
  /// 
  /// Returns a light pastel color with transparency
  static Color generateRandomPastelColor({double baseOpacity = 0.3, int? seed}) {
    final random = seed != null ? Random(seed) : Random();
    
    // Generate high values for RGB to ensure light colors
    // Using ranges that create pastel colors (170-240 range for RGB values)
    final r = 170 + random.nextInt(70);
    final g = 170 + random.nextInt(70);
    final b = 170 + random.nextInt(70);
    
    return Color.fromRGBO(r, g, b, baseOpacity);
  }
  
  /// Generates a consistent color based on a string input
  /// Useful for generating the same color for the same category each time
  /// 
  /// [input] - String input to generate color from (e.g., category name or ID)
  /// [baseOpacity] - The base opacity for the color (0.0 to 1.0)
  /// 
  /// Returns a consistent pastel color for the given input
  static Color generateConsistentColor(String input, {double baseOpacity = 0.3}) {
    // Create a simple hash from the string
    int hash = 0;
    for (var i = 0; i < input.length; i++) {
      hash = input.codeUnitAt(i) + ((hash << 5) - hash);
    }
    
    // Use the hash as a seed for the random generator
    return generateRandomPastelColor(baseOpacity: baseOpacity, seed: hash);
  }
}

import '../config/environment_config.dart';

//List loading types>
enum ListLoaderType { initial, refresh, pagination }

class AppConstants {
  // App related - now loaded from environment configuration
  static String get appName => EnvironmentConfig.appName;
  static String get appVersion => EnvironmentConfig.appVersion;

  // Timeouts
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds

  /// Default language code
  static const String defaultLanguage = 'en';

  /// Supported languages
  static const List<String> supportedLanguages = ['en', 'hi', 'ar'];

  /// Cache duration in hours
  static const int cacheDuration = 24;
}

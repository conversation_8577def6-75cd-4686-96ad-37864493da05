import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:rozana/core/config/environment_config.dart';
import 'package:rozana/core/utils/notifier.dart';
import '../dependency_injection/di_container.dart';

/// Log Handler with conditional visibility
class LogMessage {
  static bool showLog =
      EnvironmentConfig.environment != Environment.production; // Do not edit.
  static String tag = "Rozana-Log::::";

  // Default log color
  static const Color defaultColor = Colors.purple;

  /// Prints the message with the default or specified color.
  static void p(String? message, {String? subTag, Color? color}) {
    if (showLog) {
      final colorCode = _getColorCode(color ?? defaultColor);
      debugPrint('\x1B[${colorCode}m $tag${subTag ?? ''}$message \x1B[0m');
    }
  }

  /// Logs the message to the console.
  static void l(String? message, {String? subTag, Color? color}) {
    if (showLog) {
      final colorCode = _getColorCode(color ?? defaultColor);
      log('\x1B[${colorCode}m $tag${subTag ?? ''}$message \x1B[0m');
    }
  }

  /// Shows a [SnackBar] with the message for debugging (only visible in non-production builds).
  static void s(String? message) {
    if (showLog) {
      getIt<AppNotifier>().showSnackBar("$tag$message");
    }
  }

  /// Maps Flutter Colors to ANSI color codes for console logs.
  static String _getColorCode(Color color) {
    if (color == Colors.blue) return "34"; // Blue
    if (color == Colors.orange) return "33"; // Orange
    if (color == Colors.green) return "32"; // Green
    if (color == Colors.red) return "31"; // Red
    if (color == Colors.yellow) return "93"; // Yellow
    if (color == Colors.purple) return "35"; // Purple
    return "0"; // Default (white)
  }
}

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'connectivity_event.freezed.dart';

/// Defines the sealed union for all connectivity-related events using Freezed.
/// This allows for robust event handling and pattern matching in the BLoC.
@freezed
abstract class ConnectivityEvent with _$ConnectivityEvent {
  /// Event to explicitly trigger an initial or retry connectivity check.
  const factory ConnectivityEvent.checkConnectivity() = _CheckConnectivity;

  /// Event dispatched by the BLoC when the connectivity status changes.
  const factory ConnectivityEvent.connectivityChanged(
      ConnectivityResult result) = _ConnectivityChanged;

  /// Event to request a retry of the connection check, typically from a UI button.
  const factory ConnectivityEvent.retryConnection() = _RetryConnection;
}

import 'dart:async';
import 'package:dio/dio.dart';
import 'package:rozana/core/network/api_client.dart';
import 'package:rozana/core/network/api_endpoints.dart';

/// Enhanced API client that automatically determines service type (OMS/IMS)
/// and routes requests to the appropriate base URL
class ServiceApiClient {
  /// Send HTTP request with automatic service detection
  /// 
  /// This method automatically determines whether to use OMS or IMS
  /// based on the endpoint pattern
  static Future<Response<dynamic>?> sendRequest({
    required String endUrl,
    required HttpMethod method,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    String? tag,
  }) async {
    // Automatically determine service type based on endpoint
    final bool isIms = EndUrl.isImsEndpoint(endUrl);
    
    return await ApiClient.sendHttpRequest(
      endUrl: endUrl,
      method: method,
      data: data,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      tag: tag,
      isIms: isIms,
    );
  }

  /// Send HTTP request to OMS (Order Management System)
  /// 
  /// Use this when you want to explicitly target OMS
  static Future<Response<dynamic>?> sendOmsRequest({
    required String endUrl,
    required HttpMethod method,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    String? tag,
  }) async {
    return await ApiClient.sendHttpRequest(
      endUrl: endUrl,
      method: method,
      data: data,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      tag: tag,
      isIms: false, // Explicitly use OMS
    );
  }

  /// Send HTTP request to IMS (Inventory Management System)
  /// 
  /// Use this when you want to explicitly target IMS
  static Future<Response<dynamic>?> sendImsRequest({
    required String endUrl,
    required HttpMethod method,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    String? tag,
  }) async {
    return await ApiClient.sendHttpRequest(
      endUrl: endUrl,
      method: method,
      data: data,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      tag: tag,
      isIms: true, // Explicitly use IMS
    );
  }

  // ==================== Convenience Methods ====================

  /// GET request with automatic service detection
  static Future<Response<dynamic>?> get({
    required String endUrl,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    String? tag,
  }) async {
    return await sendRequest(
      endUrl: endUrl,
      method: HttpMethod.get,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      tag: tag,
    );
  }

  /// POST request with automatic service detection
  static Future<Response<dynamic>?> post({
    required String endUrl,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    String? tag,
  }) async {
    return await sendRequest(
      endUrl: endUrl,
      method: HttpMethod.post,
      data: data,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      tag: tag,
    );
  }

  /// PUT request with automatic service detection
  static Future<Response<dynamic>?> put({
    required String endUrl,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    String? tag,
  }) async {
    return await sendRequest(
      endUrl: endUrl,
      method: HttpMethod.put,
      data: data,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      tag: tag,
    );
  }

  /// DELETE request with automatic service detection
  static Future<Response<dynamic>?> delete({
    required String endUrl,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    String? tag,
  }) async {
    return await sendRequest(
      endUrl: endUrl,
      method: HttpMethod.delete,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      tag: tag,
    );
  }

  // ==================== Explicit Service Methods ====================

  /// GET request to OMS
  static Future<Response<dynamic>?> getFromOms({
    required String endUrl,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    String? tag,
  }) async {
    return await sendOmsRequest(
      endUrl: endUrl,
      method: HttpMethod.get,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      tag: tag,
    );
  }

  /// GET request to IMS
  static Future<Response<dynamic>?> getFromIms({
    required String endUrl,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    String? tag,
  }) async {
    return await sendImsRequest(
      endUrl: endUrl,
      method: HttpMethod.get,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      tag: tag,
    );
  }

  /// POST request to OMS
  static Future<Response<dynamic>?> postToOms({
    required String endUrl,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    String? tag,
  }) async {
    return await sendOmsRequest(
      endUrl: endUrl,
      method: HttpMethod.post,
      data: data,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      tag: tag,
    );
  }

  /// POST request to IMS
  static Future<Response<dynamic>?> postToIms({
    required String endUrl,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    String? tag,
  }) async {
    return await sendImsRequest(
      endUrl: endUrl,
      method: HttpMethod.post,
      data: data,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      tag: tag,
    );
  }
}

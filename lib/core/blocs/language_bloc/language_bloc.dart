import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/services/language_service.dart';

import 'language_event.dart';
import 'language_state.dart';

export 'language_event.dart';
export 'language_state.dart';

class LanguageBloc extends Bloc<LanguageEvent, LanguageState> {
  LanguageBloc() : super(const LanguageState.initial()) {
    on<LanguageEvent>((event, emit) async {
      await event.map(
        init: (e) => _mapInitToState(emit),
        changeLanguage: (e) => _mapChangeLanguageToState(emit, e.languageCode),
        toggleLanguage: (e) => _mapToggleLanguageToState(emit),
      );
    });
  }

  Future<void> _mapInitToState(Emitter<LanguageState> emit) async {
    try {
      emit(const LanguageState.loading());

      final savedLanguage = await LanguageService.getSavedLanguage();
      final locale = LanguageService.getLocaleFromLanguageCode(savedLanguage);

      emit(LanguageState.loaded(
        languageCode: savedLanguage,
        locale: locale,
      ));
    } catch (e) {
      emit(LanguageState.error('Failed to load language: $e'));
    }
  }

  Future<void> _mapChangeLanguageToState(
    Emitter<LanguageState> emit,
    String languageCode,
  ) async {
    try {
      if (!LanguageService.isLanguageSupported(languageCode)) {
        emit(const LanguageState.error('Language not supported'));
        return;
      }

      emit(const LanguageState.loading());

      await LanguageService.saveLanguage(languageCode);
      final locale = LanguageService.getLocaleFromLanguageCode(languageCode);

      emit(LanguageState.loaded(
        languageCode: languageCode,
        locale: locale,
      ));
    } catch (e) {
      emit(LanguageState.error('Failed to change language: $e'));
    }
  }

  Future<void> _mapToggleLanguageToState(Emitter<LanguageState> emit) async {
    try {
      final currentLanguage = state.mapOrNull(
            loaded: (state) => state.languageCode,
          ) ??
          'en';

      final newLanguage = LanguageService.toggleLanguage(currentLanguage);
      await _mapChangeLanguageToState(emit, newLanguage);
    } catch (e) {
      emit(LanguageState.error('Failed to toggle language: $e'));
    }
  }

  /// Get current language code
  String get currentLanguageCode {
    return state.mapOrNull(
          loaded: (state) => state.languageCode,
        ) ??
        'en'; // Default to English
  }

  /// Get current locale
  Locale get currentLocale {
    return state.mapOrNull(
          loaded: (state) => state.locale,
        ) ??
        const Locale('en'); // Default to English
  }
}

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'language_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LanguageEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is LanguageEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LanguageEvent()';
  }
}

/// @nodoc
class $LanguageEventCopyWith<$Res> {
  $LanguageEventCopyWith(LanguageEvent _, $Res Function(LanguageEvent) __);
}

/// Adds pattern-matching-related methods to [LanguageEvent].
extension LanguageEventPatterns on LanguageEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_ChangeLanguage value)? changeLanguage,
    TResult Function(_ToggleLanguage value)? toggleLanguage,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Init() when init != null:
        return init(_that);
      case _ChangeLanguage() when changeLanguage != null:
        return changeLanguage(_that);
      case _ToggleLanguage() when toggleLanguage != null:
        return toggleLanguage(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_ChangeLanguage value) changeLanguage,
    required TResult Function(_ToggleLanguage value) toggleLanguage,
  }) {
    final _that = this;
    switch (_that) {
      case _Init():
        return init(_that);
      case _ChangeLanguage():
        return changeLanguage(_that);
      case _ToggleLanguage():
        return toggleLanguage(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_ChangeLanguage value)? changeLanguage,
    TResult? Function(_ToggleLanguage value)? toggleLanguage,
  }) {
    final _that = this;
    switch (_that) {
      case _Init() when init != null:
        return init(_that);
      case _ChangeLanguage() when changeLanguage != null:
        return changeLanguage(_that);
      case _ToggleLanguage() when toggleLanguage != null:
        return toggleLanguage(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(String languageCode)? changeLanguage,
    TResult Function()? toggleLanguage,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Init() when init != null:
        return init();
      case _ChangeLanguage() when changeLanguage != null:
        return changeLanguage(_that.languageCode);
      case _ToggleLanguage() when toggleLanguage != null:
        return toggleLanguage();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(String languageCode) changeLanguage,
    required TResult Function() toggleLanguage,
  }) {
    final _that = this;
    switch (_that) {
      case _Init():
        return init();
      case _ChangeLanguage():
        return changeLanguage(_that.languageCode);
      case _ToggleLanguage():
        return toggleLanguage();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(String languageCode)? changeLanguage,
    TResult? Function()? toggleLanguage,
  }) {
    final _that = this;
    switch (_that) {
      case _Init() when init != null:
        return init();
      case _ChangeLanguage() when changeLanguage != null:
        return changeLanguage(_that.languageCode);
      case _ToggleLanguage() when toggleLanguage != null:
        return toggleLanguage();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Init implements LanguageEvent {
  const _Init();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Init);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LanguageEvent.init()';
  }
}

/// @nodoc

class _ChangeLanguage implements LanguageEvent {
  const _ChangeLanguage(this.languageCode);

  final String languageCode;

  /// Create a copy of LanguageEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ChangeLanguageCopyWith<_ChangeLanguage> get copyWith =>
      __$ChangeLanguageCopyWithImpl<_ChangeLanguage>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ChangeLanguage &&
            (identical(other.languageCode, languageCode) ||
                other.languageCode == languageCode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, languageCode);

  @override
  String toString() {
    return 'LanguageEvent.changeLanguage(languageCode: $languageCode)';
  }
}

/// @nodoc
abstract mixin class _$ChangeLanguageCopyWith<$Res>
    implements $LanguageEventCopyWith<$Res> {
  factory _$ChangeLanguageCopyWith(
          _ChangeLanguage value, $Res Function(_ChangeLanguage) _then) =
      __$ChangeLanguageCopyWithImpl;
  @useResult
  $Res call({String languageCode});
}

/// @nodoc
class __$ChangeLanguageCopyWithImpl<$Res>
    implements _$ChangeLanguageCopyWith<$Res> {
  __$ChangeLanguageCopyWithImpl(this._self, this._then);

  final _ChangeLanguage _self;
  final $Res Function(_ChangeLanguage) _then;

  /// Create a copy of LanguageEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? languageCode = null,
  }) {
    return _then(_ChangeLanguage(
      null == languageCode
          ? _self.languageCode
          : languageCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _ToggleLanguage implements LanguageEvent {
  const _ToggleLanguage();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ToggleLanguage);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LanguageEvent.toggleLanguage()';
  }
}

// dart format on

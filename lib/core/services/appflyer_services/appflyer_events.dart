import '../../../data/models/cart_item_model.dart';
import 'app_flyers_services.dart';

class _AppsFlyerEventNames {
  static const String login = 'af_login';
  static const String logout = 'af_logout';
  static const String search = 'af_search';
  static const String categoryView = 'af_list_view';
  static const String productView = 'af_content_view';
  static const String addToCart = 'af_add_to_cart';
  static const String removeFromCart = 'af_remove_from_cart';
  static const String initiatedCheckout = 'af_initiated_checkout';
  static const String purchase = 'af_purchase';
  static const String failedPurchase = 'af_failed_purchase';
  static const String addToWishlist = 'af_add_to_wishlist';
  static const String removeFromWishlist = 'af_remove_from_wishlist';
}

class _AppsFlyerEventValues {
  static const String userId = 'af_user_id';
  static const String searchQuery = 'af_search_query';
  static const String categoryId = 'af_category_id';
  static const String categoryName = 'af_category_name';
  static const String sku = 'af_content_id';
  static const String productName = 'af_content_name';
  static const String price = 'af_price';
  static const String quantity = 'af_quantity';
  static const String revenue = 'af_revenue';
  static const String checkoutItemList = 'af_checkout_item_list';
  static const String orderId = 'af_order_id';
  static const String errorMessage = 'af_error_message';
}

class AppsFlyerEvents {
  static Future<void> login(String userId) async {
    await AppsFlyerServices.logEvent(
        _AppsFlyerEventNames.login, {_AppsFlyerEventValues.userId: userId});
  }

  static Future<void> logout(String userId) async {
    await AppsFlyerServices.logEvent(
        _AppsFlyerEventNames.logout, {_AppsFlyerEventValues.userId: userId});
  }

  static Future<void> search(String query) async {
    await AppsFlyerServices.logEvent(_AppsFlyerEventNames.search,
        {_AppsFlyerEventValues.searchQuery: query});
  }

  static Future<void> categoryView(
      String categoryId, String categoryName) async {
    await AppsFlyerServices.logEvent(_AppsFlyerEventNames.categoryView, {
      _AppsFlyerEventValues.categoryId: categoryId,
      _AppsFlyerEventValues.categoryName: categoryName
    });
  }

  static Future<void> productView(
      {String? sku,
      String? productName,
      String? category,
      double? price}) async {
    await AppsFlyerServices.logEvent(_AppsFlyerEventNames.productView, {
      _AppsFlyerEventValues.price: price,
      _AppsFlyerEventValues.categoryName: category,
      _AppsFlyerEventValues.productName: productName,
      _AppsFlyerEventValues.sku: sku
    });
  }

  static Future<void> addToCart(
      {String? sku, String? productName, double? price}) async {
    await AppsFlyerServices.logEvent(_AppsFlyerEventNames.addToCart, {
      _AppsFlyerEventValues.price: price,
      _AppsFlyerEventValues.productName: productName,
      _AppsFlyerEventValues.sku: sku,
    });
  }

  static Future<void> removeFromCart(
      {String? sku, String? productName, double? price}) async {
    await AppsFlyerServices.logEvent(_AppsFlyerEventNames.removeFromCart, {
      _AppsFlyerEventValues.price: price,
      _AppsFlyerEventValues.productName: productName,
      _AppsFlyerEventValues.sku: sku
    });
  }

  static Future<void> initiatedCheckout({
    List<CartItemModel?>? cartItems,
    double? totalPrice,
  }) async {
    await AppsFlyerServices.logEvent(_AppsFlyerEventNames.initiatedCheckout, {
      _AppsFlyerEventValues.checkoutItemList: (cartItems ?? []).map((i) {
        return {
          _AppsFlyerEventValues.sku: i?.skuID,
          _AppsFlyerEventValues.productName: i?.name,
          _AppsFlyerEventValues.price: i?.price,
          _AppsFlyerEventValues.quantity: i?.quantity,
        };
      }).toList(),
      _AppsFlyerEventValues.price: totalPrice,
    });
  }

  static Future<void> purchase({
    List<CartItemModel?>? cartItems,
    double? revenue,
    String? orderId,
    String? userId,
  }) async {
    await AppsFlyerServices.logEvent(_AppsFlyerEventNames.purchase, {
      _AppsFlyerEventValues.revenue: revenue,
      _AppsFlyerEventValues.orderId: orderId,
      _AppsFlyerEventValues.userId: userId,
      _AppsFlyerEventValues.checkoutItemList: (cartItems ?? []).map((i) {
        return {
          _AppsFlyerEventValues.sku: i?.skuID,
          _AppsFlyerEventValues.productName: i?.name,
          _AppsFlyerEventValues.price: i?.price,
          _AppsFlyerEventValues.quantity: i?.quantity,
        };
      }).toList(),
    });
  }

  static Future<void> failedPurchase({
    List<CartItemModel?>? cartItems,
    double? price,
    String? userId,
    String? errorMessage,
  }) async {
    await AppsFlyerServices.logEvent(_AppsFlyerEventNames.failedPurchase, {
      _AppsFlyerEventValues.price: price,
      _AppsFlyerEventValues.userId: userId,
      _AppsFlyerEventValues.errorMessage: errorMessage,
      _AppsFlyerEventValues.checkoutItemList: (cartItems ?? []).map((i) {
        return {
          _AppsFlyerEventValues.sku: i?.skuID,
          _AppsFlyerEventValues.productName: i?.name,
          _AppsFlyerEventValues.price: i?.price,
          _AppsFlyerEventValues.quantity: i?.quantity,
        };
      }).toList(),
    });
  }

  static Future<void> addToWishlist(String productId) async {
    await AppsFlyerServices.logEvent(_AppsFlyerEventNames.addToWishlist,
        {_AppsFlyerEventValues.sku: productId});
  }

  static Future<void> removeFromWishlist(String productId) async {
    await AppsFlyerServices.logEvent(_AppsFlyerEventNames.removeFromWishlist,
        {_AppsFlyerEventValues.sku: productId});
  }
}

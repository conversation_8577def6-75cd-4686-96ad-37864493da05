import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:rozana/core/utils/logger.dart';

/// Service to handle app verification for Firebase Phone Authentication
class AppVerificationService {
  final FirebaseAuth _auth;

  AppVerificationService({FirebaseAuth? auth})
      : _auth = auth ?? FirebaseAuth.instance;

  /// Configure Firebase Auth settings based on environment
  void configureFirebaseAuth() {
    _auth.setSettings(
      appVerificationDisabledForTesting: false,
    );

    LogMessage.p('Firebase Auth: Using real verification settings');
  }

  /// Get phone authentication options with proper verification settings
  Map<String, dynamic> getPhoneAuthOptions({
    required String phoneNumber,
    required Function(PhoneAuthCredential) verificationCompleted,
    required Function(FirebaseAuthException) verificationFailed,
    required Function(String, int?) codeSent,
    required Function(String) codeAutoRetrievalTimeout,
  }) {
    final options = {
      'phoneNumber': phoneNumber,
      'verificationCompleted': verificationCompleted,
      'verificationFailed': verificationFailed,
      'codeSent': codeSent,
      'codeAutoRetrievalTimeout': codeAutoRetrievalTimeout,
      'timeout': const Duration(seconds: 120),
    };

    return options;
  }

  /// Handle verification failures with better error messages
  String getReadableErrorMessage(FirebaseAuthException e) {
    // Log the error for debugging
    LogMessage.p('Verification failed: ${e.code} - ${e.message}');

    // Handle specific error types
    if (e.code == 'invalid-phone-number') {
      return 'The phone number format is incorrect. Please enter a valid number.';
    } else if (e.code == 'too-many-requests') {
      return 'Too many requests from this device. Please try again later.';
    } else if (e.code == 'quota-exceeded') {
      return 'Service temporarily unavailable. Please try again later.';
    } else if (e.message?.contains('app identifier') == true ||
        e.message?.contains('reCAPTCHA') == true ||
        e.message?.contains('Play Integrity') == true) {
      // App verification errors
      LogMessage.p('App verification issue detected: ${e.message}');

      if (kIsWeb) {
        return 'Please complete the reCAPTCHA verification to continue.';
      } else {
        return 'Device verification failed. Please ensure you have Google Play Services installed and updated.';
      }
    }

    // Default error message
    return e.message ??
        'An error occurred during verification. Please try again.';
  }
}

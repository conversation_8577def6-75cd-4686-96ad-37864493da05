import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// Service responsible for crash reporting and analytics
class CrashAnalyticsService {
  static final CrashAnalyticsService _instance = CrashAnalyticsService._internal();

  factory CrashAnalyticsService() => _instance;

  CrashAnalyticsService._internal();

  final FirebaseCrashlytics _crashlytics = FirebaseCrashlytics.instance;
  bool _isInitialized = false;

  /// Check if the service has been initialized
  bool get isInitialized => _isInitialized;

  /// Initialize crash analytics
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('CrashAnalyticsService: Already initialized, skipping');
      return;
    }

    try {
      debugPrint('CrashAnalyticsService: Starting initialization');

      // Only enable Crashlytics in non-debug mode
      await _crashlytics.setCrashlyticsCollectionEnabled(!kDebugMode);
      
      // Set up Flutter error handling
      FlutterError.onError = (FlutterErrorDetails details) {
        _crashlytics.recordFlutterError(details);
      };
      
      // Capture uncaught asynchronous errors
      PlatformDispatcher.instance.onError = (error, stack) {
        _crashlytics.recordError(error, stack, fatal: true);
        return true;
      };
      
      // Set device information
      await _setDeviceInformation();
      
      _isInitialized = true;
      debugPrint('CrashAnalyticsService: Initialization completed successfully');
    } catch (e) {
      debugPrint('CrashAnalyticsService: Error during initialization: $e');
      // Continue even if there's an error
    }
  }

  /// Set user identifier for better crash tracking
  Future<void> setUserIdentifier(String userId) async {
    if (!_isInitialized) return;
    
    try {
      // Use a hashed or anonymized user ID for privacy
      await _crashlytics.setUserIdentifier(userId);
    } catch (e) {
      debugPrint('CrashAnalyticsService: Error setting user identifier: $e');
    }
  }

  /// Log non-fatal error with optional reason
  Future<void> logError(dynamic exception, StackTrace? stack, {String? reason}) async {
    if (!_isInitialized) return;
    
    try {
      await _crashlytics.recordError(
        exception, 
        stack, 
        reason: reason,
        fatal: false,
      );
    } catch (e) {
      debugPrint('CrashAnalyticsService: Error logging error: $e');
    }
  }

  /// Add custom key for better debugging
  Future<void> setCustomKey(String key, dynamic value) async {
    if (!_isInitialized) return;
    
    try {
      await _crashlytics.setCustomKey(key, value);
    } catch (e) {
      debugPrint('CrashAnalyticsService: Error setting custom key: $e');
    }
  }

  /// Set device information as custom keys
  Future<void> _setDeviceInformation() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      final packageInfo = await PackageInfo.fromPlatform();
      
      // Set app information
      await _crashlytics.setCustomKey('app_name', packageInfo.appName);
      await _crashlytics.setCustomKey('app_version', packageInfo.version);
      await _crashlytics.setCustomKey('build_number', packageInfo.buildNumber);
      
      // Set device information based on platform
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        await _crashlytics.setCustomKey('device_model', androidInfo.model);
        await _crashlytics.setCustomKey('android_version', androidInfo.version.release);
        await _crashlytics.setCustomKey('manufacturer', androidInfo.manufacturer);
        await _crashlytics.setCustomKey('is_physical_device', androidInfo.isPhysicalDevice);
        await _crashlytics.setCustomKey('android_id', androidInfo.id);
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        await _crashlytics.setCustomKey('device_model', iosInfo.model);
        await _crashlytics.setCustomKey('ios_version', iosInfo.systemVersion);
        await _crashlytics.setCustomKey('device_name', iosInfo.name);
        await _crashlytics.setCustomKey('is_physical_device', iosInfo.isPhysicalDevice);
        await _crashlytics.setCustomKey('identifier_for_vendor', iosInfo.identifierForVendor ?? 'unknown');
      } else if (kIsWeb) {
        final webInfo = await deviceInfo.webBrowserInfo;
        await _crashlytics.setCustomKey('browser', webInfo.browserName.name);
        await _crashlytics.setCustomKey('platform', webInfo.platform ?? 'unknown');
        await _crashlytics.setCustomKey('user_agent', webInfo.userAgent ?? 'unknown');
      }
      
      // Set additional device information
      await _crashlytics.setCustomKey('platform', Platform.operatingSystem);
      await _crashlytics.setCustomKey('locale', Platform.localeName);
      
    } catch (e) {
      debugPrint('CrashAnalyticsService: Error setting device information: $e');
    }
  }
  
  /// Log custom event for analytics
  Future<void> logEvent(String name, Map<String, dynamic> parameters) async {
    if (!_isInitialized) return;
    
    try {
      await _crashlytics.setCustomKey('last_event', name);
      for (final entry in parameters.entries) {
        await _crashlytics.setCustomKey('event_${name}_${entry.key}', entry.value.toString());
      }
    } catch (e) {
      debugPrint('CrashAnalyticsService: Error logging event: $e');
    }
  }
}

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service to manage language switching and persistence
class LanguageService {
  static const String _languageKey = 'selected_language';
  static const String _defaultLanguage = 'en';

  /// Get the saved language from local storage
  static Future<String> getSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_languageKey) ?? _defaultLanguage;
    } catch (e) {
      return _defaultLanguage;
    }
  }

  /// Save the selected language to local storage
  static Future<void> saveLanguage(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
    } catch (e) {
      // Handle error silently, language will default to English
    }
  }

  /// Toggle between English and Hindi
  static String toggleLanguage(String currentLanguage) {
    return currentLanguage == 'en' ? 'hi' : 'en';
  }

  /// Get locale from language code
  static Locale getLocaleFromLanguageCode(String languageCode) {
    return Locale(languageCode);
  }

  /// Check if the language code is supported
  static bool isLanguageSupported(String languageCode) {
    return ['en', 'hi'].contains(languageCode);
  }

  /// Get language display name
  static String getLanguageDisplayName(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'hi':
        return 'हिंदी';
      default:
        return 'English';
    }
  }
}

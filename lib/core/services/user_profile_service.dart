import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/data/models/user_profile_model.dart';

/// Service to handle user profile operations with Firestore
class UserProfileService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  // Collection names
  static const String _usersCollection = 'users';
  static const String _profileCollection = 'profile';
  static const String _profileDocument = 'data';

  UserProfileService({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance;

  /// Get current user ID
  String? get _currentUserId => _auth.currentUser?.uid;

  /// Check if user is authenticated
  bool get isUserAuthenticated => _currentUserId != null;

  /// Get user's profile document reference
  DocumentReference<Map<String, dynamic>>? get _userProfileDocument {
    final userId = _currentUserId;
    if (userId == null) return null;

    return _firestore
        .collection(_usersCollection)
        .doc(userId)
        .collection(_profileCollection)
        .doc(_profileDocument);
  }

  /// Create user profile and merge with existing user data in SharedPreferences
  Future<void> createUserProfile(UserProfileModel profile) async {
    if (!isUserAuthenticated) {
      throw Exception('User must be authenticated to create profile');
    }

    try {
      // 1. Save to Firestore (backup/sync)
      final docRef = _userProfileDocument;
      if (docRef != null) {
        final now = DateTime.now().toIso8601String();
        final profileWithTimestamps = profile.copyWith(
          uid: _currentUserId,
          phoneNumber: _auth.currentUser?.phoneNumber,
          createdAt: now,
          updatedAt: now,
          isProfileComplete: true,
        );
        await docRef.set(profileWithTimestamps.toJson());
      }

      // 2. Get existing user data from SharedPreferences
      final existingUserJson = AppPreferences.getUserdata();
      Map<String, dynamic> userData = {};

      if (existingUserJson != null && existingUserJson.isNotEmpty) {
        userData = jsonDecode(existingUserJson);
      }

      // 3. Enhance existing data with profile information
      userData.addAll({
        'displayName': profile.displayName?.trim(),
        'email': profile.email?.trim().toLowerCase(),
        'profileComplete': true,
        'profileUpdatedAt': DateTime.now().toIso8601String(),
      });

      // 4. Save enhanced data back to SharedPreferences
      await AppPreferences.setUserdata(jsonEncode(userData));
    } catch (e) {
      rethrow;
    }
  }

  /// Get user profile from local storage (instant access)
  Future<UserProfileModel?> getUserProfile() async {
    try {
      // 1. Try local storage first (INSTANT)
      final userJson = AppPreferences.getUserdata();
      if (userJson != null && userJson.isNotEmpty) {
        final userData = jsonDecode(userJson);

        // Check if profile is complete in local data
        if (userData['profileComplete'] == true) {
          return UserProfileModel(
            uid: userData['uid'],
            phoneNumber: userData['phoneNumber'],
            displayName: userData['displayName'],
            email: userData['email'],
            isProfileComplete: true,
            createdAt: userData['createdAt'],
            updatedAt: userData['profileUpdatedAt'],
          );
        }
      }

      // 2. Fallback to Firestore if local data is incomplete
      final profile = await _loadFromFirestore();

      if (profile != null) {
        // Cache to local storage for next time
        await _cacheProfileToLocal(profile);
        return profile;
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// Update user profile in both Firestore and local storage
  Future<void> updateUserProfile(UserProfileModel profile) async {
    if (!isUserAuthenticated) {
      throw Exception('User must be authenticated to update profile');
    }

    try {
      // 1. Update Firestore
      final docRef = _userProfileDocument;
      if (docRef != null) {
        final now = DateTime.now().toIso8601String();
        final profileWithUpdate = profile.copyWith(
          uid: _currentUserId,
          phoneNumber: _auth.currentUser?.phoneNumber,
          updatedAt: now,
        );
        await docRef.update(profileWithUpdate.toJson());
      }

      // 2. Update local storage immediately
      await _cacheProfileToLocal(profile);
    } catch (e) {
      rethrow;
    }
  }

  /// Check if user profile is complete (checks Firestore first, then caches locally)
  Future<bool> isProfileComplete() async {
    try {
      // First check if we have cached profile data with complete flag
      final userJson = AppPreferences.getUserdata();
      if (userJson != null && userJson.isNotEmpty) {
        final userData = jsonDecode(userJson);

        // If we have a cached complete profile, use it (instant)
        final profileCompleteFlag = userData['profileComplete'] ?? false;
        final hasName = userData['displayName'] != null &&
            userData['displayName'].toString().trim().isNotEmpty;

        if (profileCompleteFlag && hasName) {
          return true;
        }
      }

      // If no cached complete profile, check Firestore for existing profile data
      final profile = await _loadFromFirestore();

      if (profile != null && profile.isProfileComplete == true) {
        // Found complete profile in Firestore - cache it locally
        await _cacheProfileToLocal(profile);
        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// Cache profile data to local storage
  Future<void> _cacheProfileToLocal(UserProfileModel profile) async {
    try {
      final userJson = AppPreferences.getUserdata();
      Map<String, dynamic> userData = {};

      if (userJson != null && userJson.isNotEmpty) {
        userData = jsonDecode(userJson);
      }

      // Merge profile data with existing user data
      userData.addAll({
        'displayName': profile.displayName,
        'email': profile.email,
        'profileComplete': profile.isProfileComplete ?? true,
        'profileUpdatedAt': DateTime.now().toIso8601String(),
      });

      await AppPreferences.setUserdata(jsonEncode(userData));
    } catch (e) {
      // Silently handle caching errors
    }
  }

  /// Load profile data from Firestore
  Future<UserProfileModel?> _loadFromFirestore() async {
    if (!isUserAuthenticated) {
      return null;
    }

    final docRef = _userProfileDocument;
    if (docRef == null) {
      return null;
    }

    try {
      final doc = await docRef.get();

      if (doc.exists && doc.data() != null) {
        return UserProfileModel.fromJson(doc.data()!);
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// Delete user profile (for logout cleanup)
  Future<void> deleteUserProfile() async {
    if (!isUserAuthenticated) {
      return;
    }

    final docRef = _userProfileDocument;
    if (docRef == null) {
      return;
    }

    try {
      await docRef.delete();
    } catch (e) {
      rethrow;
    }
  }

  /// Create profile from basic user data (name and optional email)
  UserProfileModel createBasicProfile({
    required String displayName,
    String? email,
  }) {
    return UserProfileModel(
      displayName: displayName.trim(),
      email: email?.trim().toLowerCase(),
      isProfileComplete: true,
    );
  }
}

import 'package:firebase_auth/firebase_auth.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/utils/logger.dart';

/// Service to handle Firebase authentication token refreshes
class TokenRefreshService {
  final FirebaseAuth _firebaseAuth;
  
  TokenRefreshService({
    FirebaseAuth? firebaseAuth,
  }) : _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance;

  /// Refreshes the Firebase authentication token and updates it in AppPreferences
  /// 
  /// Returns a fresh token or throws an exception if refresh fails
  Future<String> refreshToken() async {
    try {
      // Get the current user
      User? currentUser = _firebaseAuth.currentUser;
      
      if (currentUser == null) {
        throw 'No authenticated user found';
      }
      
      // Force token refresh
      await currentUser.getIdToken(true);
      
      // Get the fresh token
      String? token = await currentUser.getIdToken();
      
      if (token == null) {
        throw 'Failed to get a valid token';
      }
      
      // Save the refreshed token
      await AppPreferences.setToken(token);
      
      LogMessage.p('Token refreshed successfully');
      return token;
    } on FirebaseAuthException catch (e) {
      LogMessage.p('Failed to refresh token: ${e.code} - ${e.message}');
      
      // Handle specific error cases
      if (e.code == 'user-token-expired' || 
          e.code == 'user-not-found' ||
          e.code == 'requires-recent-login') {
        // User needs to re-authenticate
        throw 'Session expired. Please sign in again.';
      }
      
      throw e.message ?? 'Failed to refresh authentication token';
    } catch (e) {
      LogMessage.p('Unknown error during token refresh: $e');
      throw 'Something went wrong. Please try again.';
    }
  }
}
